# Test Assign Permissions Feature

## Chức năng đã thêm:

### 1. States mới:
- `assigningRole`: Role đang được assign permissions
- `assigningPermissions`: Loading state khi đang assign
- `rolePermissionMappings`: <PERSON>h sách permissions đ<PERSON> được assign cho role

### 2. Functions mới:
- `handleOpenAssignPermissions(role)`: Mở permission selection box cho role
- `handleAssignPermission(permissionId)`: Assign permission cho role ngay lập tức
- `handleCloseAssignPermissions()`: Đóng permission selection box
- `getAvailablePermissionsForRole()`: L<PERSON><PERSON> danh sách permissions chưa được assign

### 3. UI Changes:
- Button ListPlus trong management tab giờ sẽ mở permission selection box
- Permission selection box hiển thị khi có assigningRole
- Click vào permission sẽ assign ngay lập tức
- Hiển thị loading state khi đang assign
- Hiển thị thông bá<PERSON> khi tất cả permissions đã đượ<PERSON> assign

## Cách test:

1. V<PERSON><PERSON> trang Settings
2. <PERSON><PERSON><PERSON><PERSON> sang tab "Management"
3. T<PERSON><PERSON> một role và click button với icon ListPlus
4. Permission selection box sẽ hiển thị
5. Click vào bất kỳ permission nào để assign
6. API sẽ được gọi ngay lập tức
7. Permission sẽ biến mất khỏi danh sách available permissions
8. Toast notification sẽ hiển thị kết quả

## API được sử dụng:
- `rolePermissionMappingsApiRequests.getRolePermissionMappingsByRoleId()`: Load permissions hiện tại của role
- `rolePermissionMappingsApiRequests.createRolePermissionMapping()`: Assign permission cho role
