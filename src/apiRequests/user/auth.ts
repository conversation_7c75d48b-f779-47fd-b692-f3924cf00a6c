import http from "@/lib/http";
import {
  LoginBodyType,
  RegisterBodyType,
  AuthBodyType,
  LoginResType,
} from "@/schemaValidations/auth.schema";

const authApiRequests = {
  login: (body: LoginBodyType) => {
    return http.post<LoginResType>("/api/v1/auth/email/login", body);
  },

  register: (body: RegisterBodyType) => {
    return http.post("/api/v1/auth/email/register", body);
  },

  refreshToken: (body: RegisterBodyType) => {
    return http.post("/api/v1/auth/refresh", body);
  },

  forgetPassword: (body: { email: string }) => {
    return http.post("api/v1/auth/forgot/password", body);
  },

  verifyAccount: (body: { hash: string; password: string }) => {
    return http.post("api/v1/auth/email/confirm", body);
  },

  resetPassword: (body: { hash: string; password: string }) => {
    return http.post("api/v1/auth/reset/password", body);
  },

  forcePassword: (body: { hash: string; password: string }) => {
    return http.post("api/v1/auth/force/password", body);
  },

  // Internal Next.js API routes
  auth: (body: AuthBodyType) => {
    return http.post("/api/auth/login", body, {
      baseUrl: "",
    });
  },
  logout: (body: any) => {
    console.log('logout: ', body);
    return http.post<any>("/api/auth/logout", body, {
      baseUrl: "",
    });
  },

  reload: () => {
    return http.get<any>("/api/auth/reload", {
      baseUrl: "",
    });
  },
};

export default authApiRequests;
