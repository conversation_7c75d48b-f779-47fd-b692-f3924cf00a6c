import http, {PaginateResponse} from "@/lib/http";
import {Group} from "@/app/(DashboardLayout)/devices/types";

const groupsApiRequests = {
  getGroups: (
    userId: string,
    page: number,
    limit: number,
    filters: Record<any, any> = {},
  ) => {
    return http.get<PaginateResponse<Group>>(`/api/v1/users/${userId}/groups`, { filters, page, limit });
  },

  getGroup: (userId: string, id: string) => {
    return http.get<any>(`/api/v1/users/${userId}/groups/${id}`);
  },

  deleteGroup: (userId: string, id: string) => {
    return http.delete<any>(`/api/v1/users/${userId}/groups/${id}`);
  },

  updateGroup: (userId: string, id: string, body: Partial<Group>) => {
    return http.patch<any>(`/api/v1/users/${userId}/groups/${id}`, body);
  },

  createGroup: (userId: string, body: Partial<Group>) => {
    return http.post<any>(`/api/v1/users/${userId}/groups`, body);
  },
};

export default groupsApiRequests;
