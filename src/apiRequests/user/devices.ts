import http, { PaginateResponse } from "@/lib/http";
import { Policy } from "@/app/(DashboardLayout)/policy/types";

// Note: payload is Policies
const userDevicesApiRequests = {
  getUserDevices: (
    userId: string,
    page: number,
    limit: number,
    filters: Record<any, any> = {}
  ) => {
    return http.get<PaginateResponse<Policy>>(
      `/api/v1/users/${userId}/devices`,
      { filters, page, limit }
    );
  },

  getUserDevice: (userId: string, deviceId: string) => {
    return http.get<Policy>(`/api/v1/users/${userId}/devices/${deviceId}`);
  },

  deleteUserDevice: (userId: string, deviceId: string) => {
    return http.delete<any>(`/api/v1/users/${userId}/devices/${deviceId}`);
  },

  updateUserDevice: (
    userId: string,
    deviceId: string,
    body: Partial<Policy>
  ) => {
    return http.patch<Policy>(
      `/api/v1/users/${userId}/devices/${deviceId}`,
      body
    );
  },

  createUserDevice: (
    userId: string,
    deviceId: string,
    policy: Partial<Policy>
  ) => {
    return http.post<any>(
      `/api/v1/users/${userId}/devices/${deviceId}`,
      policy
    );
  },

  bulkCreateUserDevice: (
    userId: string,
    deviceId: string,
    policies: Partial<Policy>[]
  ) => {
    return http.post<any>(
      `/api/v1/users/${userId}/devices/${deviceId}`,
      policies
    );
  },
};

export default userDevicesApiRequests;
