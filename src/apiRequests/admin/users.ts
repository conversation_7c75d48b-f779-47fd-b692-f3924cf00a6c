import http, {PaginateResponse} from "@/lib/http";
import {User} from "@/app/(DashboardLayout)/users/types";
import { getCurrentOrgId } from "@/lib/api-org-context";

const usersApiRequests = {
  getUsers: (
    orgIdOrPage: string | number,
    pageOrLimit?: number,
    limitOrFilters?: number | Record<any, any>,
    filters: Record<any, any> = {}
  ) => {
    // Support both old signature (orgId, page, limit, filters) and new (page, limit, filters)
    let orgId: string;
    let page: number;
    let limit: number;
    let actualFilters: Record<any, any>;

    if (typeof orgIdOrPage === 'string') {
      // Old signature: (orgId: string, page: number, limit: number, filters?)
      orgId = orgIdOrPage;
      page = pageOrLimit as number;
      limit = limitOrFilters as number;
      actualFilters = filters;
    } else {
      // New signature: (page: number, limit: number, filters?)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      page = orgIdOrPage;
      limit = pageOrLimit as number;
      actualFilters = (limitOrFilters as Record<any, any>) || {};
    }

    return http.get<PaginateResponse<User>>(`/api/v1/organizations/${orgId}/users`, { 
      filters: actualFilters, 
      page, 
      limit 
    });
  },

  searchUsers: (
    orgIdOrKeyword: string,
    keywordOrLimit?: string | number,
    limit: number = 10,
  ) => {
    // Support both old signature (orgId, keyword, limit) and new (keyword, limit)
    let orgId: string;
    let keyword: string;
    let actualLimit: number;

    if (typeof keywordOrLimit === 'string') {
      // Old signature: (orgId: string, keyword: string, limit?: number)
      orgId = orgIdOrKeyword;
      keyword = keywordOrLimit;
      actualLimit = limit;
    } else {
      // New signature: (keyword: string, limit?: number)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      keyword = orgIdOrKeyword;
      actualLimit = (keywordOrLimit as number) || 10;
    }

    return http.get<PaginateResponse<User>>(`/api/v1/organizations/${orgId}/users`, {
      filters: { keyword },
      page: 1,
      limit: actualLimit
    });
  },

  getUser: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.get(`/api/v1/organizations/${actualOrgId}/users/${actualId}`);
  },

  updateUser: (orgIdOrId: string, idOrData: string | any, data?: any) => {
    // Support both old signature (orgId, id, data) and new (id, data)
    const actualOrgId = data ? orgIdOrId : getCurrentOrgId()!;
    const actualId = data ? (idOrData as string) : orgIdOrId;
    const actualData = data || idOrData;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.patch(`/api/v1/organizations/${actualOrgId}/users/${actualId}`, actualData);
  },

  deleteUser: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.delete(`/api/v1/organizations/${actualOrgId}/users/${actualId}`);
  },

  createUser: (orgIdOrData: string | any, data?: any) => {
    // Support both old signature (orgId, data) and new (data)
    const actualOrgId = data ? (orgIdOrData as string) : getCurrentOrgId()!;
    const actualData = data || orgIdOrData;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post(`/api/v1/organizations/${actualOrgId}/users`, actualData);
  },
};

export default usersApiRequests;
