import http, {PaginateResponse} from "@/lib/http";
import {CodePush} from "@/app/(DashboardLayout)/code-push/types";
import { getCurrentOrgId } from "@/lib/api-org-context";

const codepushApiRequests = {
  getListCodePush: (
    orgIdOrPage: string | number,
    pageOrLimit?: number,
    limitOrFilters?: number | Record<any, any>,
    filters: Record<any, any> = {},
  ) => {
    // Support both old signature (orgId, page, limit, filters) and new (page, limit, filters)
    let orgId: string;
    let page: number;
    let limit: number;
    let actualFilters: Record<any, any>;

    if (typeof orgIdOrPage === 'string') {
      // Old signature: (orgId: string, page: number, limit: number, filters?)
      orgId = orgIdOrPage;
      page = pageOrLimit as number;
      limit = limitOrFilters as number;
      actualFilters = filters;
    } else {
      // New signature: (page: number, limit: number, filters?)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      page = orgIdOrPage;
      limit = pageOrLimit as number;
      actualFilters = (limitOrFilters as Record<any, any>) || {};
    }

    return http.get<PaginateResponse<CodePush>>(`/api/v1/organizations/${orgId}/code-pushes`, { 
      filters: actualFilters, 
      page, 
      limit 
    });
  },

  getCodePush: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.get<CodePush>(`/api/v1/organizations/${actualOrgId}/code-pushes/${actualId}`);
  },

  deleteCodePush: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.delete<any>(`/api/v1/organizations/${actualOrgId}/code-pushes/${actualId}`);
  },

  updateCodePush: (orgIdOrId: string, idOrBody: string | Partial<CodePush>, body?: Partial<CodePush>) => {
    // Support both old signature (orgId, id, body) and new (id, body)
    const actualOrgId = body ? orgIdOrId : getCurrentOrgId()!;
    const actualId = body ? (idOrBody as string) : orgIdOrId;
    const actualBody = body || (idOrBody as Partial<CodePush>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.patch<CodePush>(`/api/v1/organizations/${actualOrgId}/code-pushes/${actualId}`, actualBody);
  },

  createCodePush: (orgIdOrBody: string | Partial<CodePush>, body?: Partial<CodePush>) => {
    // Support both old signature (orgId, body) and new (body)
    const actualOrgId = body ? (orgIdOrBody as string) : getCurrentOrgId()!;
    const actualBody = body || (orgIdOrBody as Partial<CodePush>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post<CodePush>(`/api/v1/organizations/${actualOrgId}/code-pushes`, actualBody);
  },
};

export default codepushApiRequests;
