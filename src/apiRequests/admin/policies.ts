import http, {PaginateResponse} from "@/lib/http";
import {Policy} from "@/app/(DashboardLayout)/policy/types";
import { getCurrentOrgId } from "@/lib/api-org-context";

const policiesApiRequests = {
  getPolicies: (
    orgIdOrPage: string | number,
    pageOrLimit?: number,
    limitOrFilters?: number | Record<any, any>,
    filters: Record<any, any> = {},
  ) => {
    // Support both old signature (orgId, page, limit, filters) and new (page, limit, filters)
    let orgId: string;
    let page: number;
    let limit: number;
    let actualFilters: Record<any, any>;

    if (typeof orgIdOrPage === 'string') {
      // Old signature: (orgId: string, page: number, limit: number, filters?)
      orgId = orgIdOrPage;
      page = pageOrLimit as number;
      limit = limitOrFilters as number;
      actualFilters = filters;
    } else {
      // New signature: (page: number, limit: number, filters?)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      page = orgIdOrPage;
      limit = pageOrLimit as number;
      actualFilters = (limitOrFilters as Record<any, any>) || {};
    }

    return http.get<PaginateResponse<Policy>>(`/api/v1/organizations/${orgId}/policies`, { 
      filters: actualFilters, 
      page, 
      limit 
    });
  },

  searchPolicies: (
    orgIdOrKeyword: string,
    keywordOrPage?: string | number,
    pageOrLimit?: number,
    limit: number = 10,
  ) => {
    // Support both old signature (orgId, keyword, page, limit) and new (keyword, page, limit)
    let orgId: string;
    let keyword: string;
    let page: number;
    let actualLimit: number;

    if (typeof keywordOrPage === 'string') {
      // Old signature: (orgId: string, keyword: string, page?: number, limit?: number)
      orgId = orgIdOrKeyword;
      keyword = keywordOrPage;
      page = (pageOrLimit as number) || 1;
      actualLimit = limit;
    } else {
      // New signature: (keyword: string, page?: number, limit?: number)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      keyword = orgIdOrKeyword;
      page = (keywordOrPage as number) || 1;
      actualLimit = (pageOrLimit as number) || 10;
    }

    return http.get<PaginateResponse<Policy>>(`/api/v1/organizations/${orgId}/policies`, {
      filters: { keyword },
      page,
      limit: actualLimit
    });
  },

  getPolicy: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.get<any>(`/api/v1/organizations/${actualOrgId}/policies/${actualId}`);
  },

  deletePolicy: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.delete<any>(`/api/v1/organizations/${actualOrgId}/policies/${actualId}`);
  },

  updatePolicy: (orgIdOrId: string, idOrBody: string | Partial<Policy>, body?: Partial<Policy>) => {
    // Support both old signature (orgId, id, body) and new (id, body)
    const actualOrgId = body ? orgIdOrId : getCurrentOrgId()!;
    const actualId = body ? (idOrBody as string) : orgIdOrId;
    const actualBody = body || (idOrBody as Partial<Policy>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.patch<any>(`/api/v1/organizations/${actualOrgId}/policies/${actualId}`, actualBody);
  },

  createPolicy: (orgIdOrBody: string | Partial<Policy>, body?: Partial<Policy>) => {
    // Support both old signature (orgId, body) and new (body)
    const actualOrgId = body ? (orgIdOrBody as string) : getCurrentOrgId()!;
    const actualBody = body || (orgIdOrBody as Partial<Policy>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post<any>(`/api/v1/organizations/${actualOrgId}/policies`, actualBody);
  },

  bulkCreatePolicy: (orgIdOrBody: string | Partial<Policy>[], body?: Partial<Policy>[]) => {
    // Support both old signature (orgId, body) and new (body)
    const actualOrgId = body ? (orgIdOrBody as string) : getCurrentOrgId()!;
    const actualBody = body || (orgIdOrBody as Partial<Policy>[]);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post<any>(`/api/v1/organizations/${actualOrgId}/policies/bulk`, actualBody);
  },
};

export default policiesApiRequests;
