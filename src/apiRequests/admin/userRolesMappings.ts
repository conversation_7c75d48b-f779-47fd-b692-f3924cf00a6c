import http, { PaginateResponse } from "@/lib/http";
import { getCurrentOrgId } from "@/lib/api-org-context";
import {
  UserRoleMapping,
  CreateUserRoleMappingRequest,
  UpdateUserRoleMappingRequest,
  UserRoleMappingFilters,
  BulkCreateUserRoleMappingsRequest,
} from "@/types/mappings";

const userRolesMappingsApiRequests = {
  /**
   * Get all user-role mappings with pagination and filters
   * API: GET /api/v1/organizations/{orgId}/user-roles
   */
  getUserRolesMappings: (
    page: number = 1,
    limit: number = 10,
    filters: UserRoleMappingFilters = {}
  ) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    return http.get<PaginateResponse<UserRoleMapping>>(
      `/api/v1/organizations/${orgId}/user-roles`,
      {
        filters,
        page,
        limit,
      }
    );
  },

  /**
   * Get user-role mappings for a specific user
   * API: GET /api/v1/organizations/{orgId}/user-roles?filters[userId]=...
   */
  getUserRolesMappingsByUserId: (
    userId: string,
    page: number = 1,
    limit: number = 100,
    filters: UserRoleMappingFilters = {}
  ) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    return http.get<PaginateResponse<UserRoleMapping>>(
      `/api/v1/organizations/${orgId}/user-roles`,
      {
        filters: { ...filters, userId },
        page,
        limit,
      }
    );
  },

  /**
   * Get user-role mappings for a specific role
   * API: GET /api/v1/organizations/{orgId}/user-roles?filters[roleId]=...
   */
  getUserRolesMappingsByRoleId: (
    roleId: string,
    page: number = 1,
    limit: number = 100,
    filters: UserRoleMappingFilters = {}
  ) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    return http.get<PaginateResponse<UserRoleMapping>>(
      `/api/v1/organizations/${orgId}/user-roles`,
      {
        filters: { ...filters, roleId },
        page,
        limit,
      }
    );
  },

  /**
   * Get a specific user-role mapping by ID
   * API: GET /api/v1/organizations/{orgId}/user-roles/{id}
   */
  getUserRoleMapping: (id: string) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    return http.get<UserRoleMapping>(
      `/api/v1/organizations/${orgId}/user-roles/${id}`
    );
  },

  /**
   * Create a new user-role mapping
   * API: POST /api/v1/organizations/{orgId}/user-roles
   */
  createUserRoleMapping: (body: CreateUserRoleMappingRequest) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    // For UserRoles API, orgId is in the path, not in the body
    const requestBody = {
      roleId: body.roleId,
      userId: body.userId,
      // Remove orgId from body since it's in the path
    };

    return http.post<UserRoleMapping>(
      `/api/v1/organizations/${orgId}/user-roles`,
      requestBody
    );
  },

  /**
   * Update an existing user-role mapping
   * API: PATCH /api/v1/organizations/{orgId}/user-roles/{id}
   */
  updateUserRoleMapping: (id: string, body: UpdateUserRoleMappingRequest) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    return http.patch<UserRoleMapping>(
      `/api/v1/organizations/${orgId}/user-roles/${id}`,
      body
    );
  },

  /**
   * Delete a user-role mapping
   * API: DELETE /api/v1/organizations/{orgId}/user-roles/{id}
   */
  deleteUserRoleMapping: (id: string) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    return http.delete<void>(
      `/api/v1/organizations/${orgId}/user-roles/${id}`
    );
  },

  /**
   * Bulk create user-role mappings
   * Note: This endpoint may not exist in the actual API, using individual creates as fallback
   */
  bulkCreateUserRoleMappings: async (body: BulkCreateUserRoleMappingsRequest) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    // Since bulk endpoint may not exist, create mappings individually
    const results = await Promise.all(
      body.mappings.map(mapping =>
        userRolesMappingsApiRequests.createUserRoleMapping({
          roleId: mapping.roleId,
          userId: mapping.userId,
          isActive: mapping.isActive,
          expiresAt: mapping.expiresAt,
        })
      )
    );

    return {
      status: 200,
      payload: results.map(r => r.payload).filter(Boolean) as UserRoleMapping[],
    };
  },

  /**
   * Bulk delete user-role mappings
   * Note: This endpoint may not exist in the actual API, using individual deletes as fallback
   */
  bulkDeleteUserRoleMappings: async (ids: string[]) => {
    // Since bulk endpoint may not exist, delete mappings individually
    await Promise.all(
      ids.map(id => userRolesMappingsApiRequests.deleteUserRoleMapping(id))
    );

    return {
      status: 200,
      payload: undefined,
    };
  },

  /**
   * Assign multiple roles to a user
   */
  assignRolesToUser: (userId: string, roleIds: string[]) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    const mappings = roleIds.map(roleId => ({
      userId,
      roleId,
      isActive: true,
    }));

    return userRolesMappingsApiRequests.bulkCreateUserRoleMappings({
      mappings,
    });
  },

  /**
   * Remove multiple roles from a user
   */
  removeRolesFromUser: async (userId: string, roleIds: string[]) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    // First, get existing mappings for this user and these roles
    const response = await userRolesMappingsApiRequests.getUserRolesMappingsByUserId(
      userId,
      1,
      1000,
      { roleId: roleIds.join(',') } // Assuming API supports comma-separated values
    );

    if (response.status === 200 && response.payload) {
      const mappingIds = response.payload.docs
        .filter(mapping => roleIds.includes(mapping.roleId))
        .map(mapping => mapping.id);
      
      if (mappingIds.length > 0) {
        return userRolesMappingsApiRequests.bulkDeleteUserRoleMappings(mappingIds);
      }
    }
    
    return Promise.resolve();
  },
};

export default userRolesMappingsApiRequests;
