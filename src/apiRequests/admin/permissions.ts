import http, {PaginateResponse} from "@/lib/http";
import {Permission} from "@/app/(DashboardLayout)/settings/permissions/types";
import { getCurrentOrgId } from "@/lib/api-org-context";

const permissionsApiRequests = {
  getPermissions: (
    orgIdOrPage: string | number,
    pageOrLimit?: number,
    limitOrFilters?: number | Record<any, any>,
    filters: Record<any, any> = {}
  ) => {
    // Support both old signature (orgId, page, limit, filters) and new (page, limit, filters)
    let page: number;
    let limit: number;
    let actualFilters: Record<any, any>;

    if (typeof orgIdOrPage === 'string') {
      // Old signature: (orgId: string, page: number, limit: number, filters?)
      // But we'll use global permissions endpoint instead
      page = pageOrLimit as number;
      limit = limitOrFilters as number;
      actualFilters = filters;
    } else {
      // New signature: (page: number, limit: number, filters?)
      page = orgIdOrPage;
      limit = pageOrLimit as number;
      actualFilters = (limitOrFilters as Record<any, any>) || {};
    }

    // Use global permissions endpoint (similar to role-permissions)
    return http.get<PaginateResponse<Permission>>(`/api/v1/permissions`, {
      filters: actualFilters,
      page,
      limit
    });
  },

  getPermission: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualId = id || orgIdOrId;

    return http.get<Permission>(`/api/v1/permissions/${actualId}`);
  },

  deletePermission: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualId = id || orgIdOrId;

    return http.delete<any>(`/api/v1/permissions/${actualId}`);
  },

  updatePermission: (orgIdOrId: string, idOrBody: string | Partial<Permission>, body?: Partial<Permission>) => {
    // Support both old signature (orgId, id, body) and new (id, body)
    const actualId = body ? idOrBody as string : orgIdOrId;
    const actualBody = body || idOrBody as Partial<Permission>;

    return http.patch<Permission>(`/api/v1/permissions/${actualId}`, actualBody);
  },

  createPermission: (orgIdOrBody: string | Partial<Permission>, body?: Partial<Permission>) => {
    // Support both old signature (orgId, body) and new (body)
    const actualBody = body || orgIdOrBody as Partial<Permission>;

    return http.post<Permission>(`/api/v1/permissions`, actualBody);
  },
};

export default permissionsApiRequests;
