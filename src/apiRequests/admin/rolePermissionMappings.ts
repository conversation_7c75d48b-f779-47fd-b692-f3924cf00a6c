import http, { PaginateResponse } from "@/lib/http";
import { getCurrentOrgId } from "@/lib/api-org-context";
import {
  RolePermissionMapping,
  CreateRolePermissionMappingRequest,
  UpdateRolePermissionMappingRequest,
  RolePermissionMappingFilters,
  BulkCreateRolePermissionMappingsRequest,
} from "@/types/mappings";

const rolePermissionMappingsApiRequests = {
  /**
   * Get all role-permission mappings with pagination and filters
   * API: GET /api/v1/role-permissions
   */
  getRolePermissionMappings: (
    page: number = 1,
    limit: number = 10,
    filters: RolePermissionMappingFilters = {}
  ) => {
    return http.get<PaginateResponse<RolePermissionMapping>>(
      `/api/v1/role-permissions`,
      {
        ...filters,
        page,
        limit,
      }
    );
  },

  /**
   * Get role-permission mappings for a specific role
   * API: GET /api/v1/role-permissions?filters[roleId]=...
   */
  getRolePermissionMappingsByRoleId: (
    roleId: string,
    page: number = 1,
    limit: number = 100,
    filters: RolePermissionMappingFilters = {}
  ) => {
    return http.get<PaginateResponse<RolePermissionMapping>>(
      `/api/v1/role-permissions`,
      {
        filters: { ...filters, roleId },
        page,
        limit,
      }
    );
  },

  /**
   * Get role-permission mappings for a specific permission
   * API: GET /api/v1/role-permissions?filters[permissionId]=...
   */
  getRolePermissionMappingsByPermissionId: (
    permissionId: string,
    page: number = 1,
    limit: number = 100,
    filters: RolePermissionMappingFilters = {}
  ) => {
    return http.get<PaginateResponse<RolePermissionMapping>>(
      `/api/v1/role-permissions`,
      {
        filters: { ...filters, permissionId },
        page,
        limit,
      }
    );
  },

  /**
   * Get a specific role-permission mapping by ID
   * API: GET /api/v1/role-permissions/{id}
   */
  getRolePermissionMapping: (id: string) => {
    return http.get<RolePermissionMapping>(
      `/api/v1/role-permissions/${id}`
    );
  },

  /**
   * Create a new role-permission mapping
   * API: POST /api/v1/role-permissions
   */
  createRolePermissionMapping: (body: CreateRolePermissionMappingRequest) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    // Ensure orgId is set in the body (required by API)
    const requestBody = {
      ...body,
      orgId: body.orgId || orgId,
    };

    return http.post<RolePermissionMapping>(
      `/api/v1/role-permissions`,
      requestBody
    );
  },

  /**
   * Update an existing role-permission mapping
   * API: PATCH /api/v1/role-permissions/{id}
   */
  updateRolePermissionMapping: (id: string, body: UpdateRolePermissionMappingRequest) => {
    return http.patch<RolePermissionMapping>(
      `/api/v1/role-permissions/${id}`,
      body
    );
  },

  /**
   * Delete a role-permission mapping
   * API: DELETE /api/v1/role-permissions/{id}
   */
  deleteRolePermissionMapping: (id: string) => {
    return http.delete<void>(
      `/api/v1/role-permissions/${id}`
    );
  },

  /**
   * Bulk create role-permission mappings
   * Note: This endpoint may not exist in the actual API, using individual creates as fallback
   */
  bulkCreateRolePermissionMappings: async (body: BulkCreateRolePermissionMappingsRequest) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    // Since bulk endpoint may not exist, create mappings individually
    const results = await Promise.all(
      body.mappings.map(mapping =>
        rolePermissionMappingsApiRequests.createRolePermissionMapping({
          ...mapping,
          orgId: mapping.orgId || orgId,
        })
      )
    );

    return {
      status: 200,
      payload: results.map(r => r.payload).filter(Boolean) as RolePermissionMapping[],
    };
  },

  /**
   * Bulk delete role-permission mappings
   * Note: This endpoint may not exist in the actual API, using individual deletes as fallback
   */
  bulkDeleteRolePermissionMappings: async (ids: string[]) => {
    // Since bulk endpoint may not exist, delete mappings individually
    await Promise.all(
      ids.map(id => rolePermissionMappingsApiRequests.deleteRolePermissionMapping(id))
    );

    return {
      status: 200,
      payload: undefined,
    };
  },

  /**
   * Assign multiple permissions to a role
   */
  assignPermissionsToRole: (roleId: string, permissionIds: string[]) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    const mappings = permissionIds.map(permissionId => ({
      roleId,
      permissionId,
      orgId,
      isActive: true,
    }));

    return rolePermissionMappingsApiRequests.bulkCreateRolePermissionMappings({
      mappings,
    });
  },

  /**
   * Remove multiple permissions from a role
   */
  removePermissionsFromRole: async (roleId: string, permissionIds: string[]) => {
    const orgId = getCurrentOrgId();
    if (!orgId) {
      throw new Error('No organization selected. Please select an organization.');
    }

    // First, get existing mappings for this role and these permissions
    const response = await rolePermissionMappingsApiRequests.getRolePermissionMappingsByRoleId(
      roleId,
      1,
      1000,
      { permissionId: permissionIds.join(',') } // Assuming API supports comma-separated values
    );

    if (response.status === 200 && response.payload) {
      const mappingIds = response.payload.docs
        .filter(mapping => permissionIds.includes(mapping.permissionId))
        .map(mapping => mapping.id);
      
      if (mappingIds.length > 0) {
        return rolePermissionMappingsApiRequests.bulkDeleteRolePermissionMappings(mappingIds);
      }
    }
    
    return Promise.resolve();
  },
};

export default rolePermissionMappingsApiRequests;
