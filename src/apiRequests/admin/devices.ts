import http, {PaginateResponse} from "@/lib/http";
import {Device} from "@/app/(DashboardLayout)/devices-manager/types";
import { getCurrentOrgId } from "@/lib/api-org-context";

const devicesApiRequests = {
  getDevices: (
    orgIdOrPage: string | number,
    pageOrLimit?: number,
    limitOrFilters?: number | Record<any, any>,
    filters: Record<any, any> = {},
  ) => {
    // Support both old signature (orgId, page, limit, filters) and new (page, limit, filters)
    let orgId: string;
    let page: number;
    let limit: number;
    let actualFilters: Record<any, any>;

    if (typeof orgIdOrPage === 'string') {
      // Old signature: (orgId: string, page: number, limit: number, filters?)
      orgId = orgIdOrPage;
      page = pageOrLimit as number;
      limit = limitOrFilters as number;
      actualFilters = filters;
    } else {
      // New signature: (page: number, limit: number, filters?)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      page = orgIdOrPage;
      limit = pageOrLimit as number;
      actualFilters = (limitOrFilters as Record<any, any>) || {};
    }

    return http.get<PaginateResponse<Device>>(`/api/v1/organizations/${orgId}/devices`, { 
      filters: actualFilters, 
      page, 
      limit 
    });
  },

  searchDevices: (
    orgIdOrKeyword: string,
    keywordOrLimit?: string | number,
    limit: number = 10,
  ) => {
    // Support both old signature (orgId, keyword, limit) and new (keyword, limit)
    let orgId: string;
    let keyword: string;
    let actualLimit: number;

    if (typeof keywordOrLimit === 'string') {
      // Old signature: (orgId: string, keyword: string, limit?: number)
      orgId = orgIdOrKeyword;
      keyword = keywordOrLimit;
      actualLimit = limit;
    } else {
      // New signature: (keyword: string, limit?: number)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      keyword = orgIdOrKeyword;
      actualLimit = (keywordOrLimit as number) || 10;
    }

    return http.get<PaginateResponse<Device>>(`/api/v1/organizations/${orgId}/devices`, {
      filters: { keyword },
      page: 1,
      limit: actualLimit
    });
  },

  getDevice: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.get<Device>(`/api/v1/organizations/${actualOrgId}/devices/${actualId}`);
  },

  deleteDevice: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.delete<any>(`/api/v1/organizations/${actualOrgId}/devices/${actualId}`);
  },

  updateDevice: (orgIdOrId: string, idOrBody: string | Partial<Device>, body?: Partial<Device>) => {
    // Support both old signature (orgId, id, body) and new (id, body)
    const actualOrgId = body ? orgIdOrId : getCurrentOrgId()!;
    const actualId = body ? (idOrBody as string) : orgIdOrId;
    const actualBody = body || (idOrBody as Partial<Device>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.patch<Device>(`/api/v1/organizations/${actualOrgId}/devices/${actualId}`, actualBody);
  },

  createDevice: (orgIdOrBody: string | Partial<Device>, body?: Partial<Device>) => {
    // Support both old signature (orgId, body) and new (body)
    const actualOrgId = body ? (orgIdOrBody as string) : getCurrentOrgId()!;
    const actualBody = body || (orgIdOrBody as Partial<Device>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post<Device>(`/api/v1/organizations/${actualOrgId}/devices`, actualBody);
  },

  overwriteValue: (deviceIds: string[], value: object) => {
    return http.post<any>("/api/v1/vernemq/alerts/parameters", {
      deviceIds,
      value,
    });
  },

  resetValue: (deviceIds: string[]) => {
    return http.post<any>("/api/v1/vernemq/alerts/parameters", {
      deviceIds,
      value: {},
    });
  },

  bulkUpdateFirmware: (orgIdOrUpdates: string | Array<{id: string, firmwareId: string}>, updates?: Array<{id: string, firmwareId: string}>) => {
    // Support both old signature (orgId, updates) and new (updates)
    const actualOrgId = updates ? (orgIdOrUpdates as string) : getCurrentOrgId()!;
    const actualUpdates = updates || (orgIdOrUpdates as Array<{id: string, firmwareId: string}>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post<any>(`/api/v1/organizations/${actualOrgId}/devices/bulk`, actualUpdates);
  },
};

export default devicesApiRequests;
