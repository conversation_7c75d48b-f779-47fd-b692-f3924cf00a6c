import http, {PaginateResponse} from "@/lib/http";
import {Role} from "@/app/(DashboardLayout)/settings/roles/types";
import { getCurrentOrgId } from "@/lib/api-org-context";

const rolesApiRequests = {
  getRoles: (
    orgIdOrPage: string | number,
    pageOrLimit?: number,
    limitOrFilters?: number | Record<any, any>,
    filters: Record<any, any> = {}
  ) => {
    // Support both old signature (orgId, page, limit, filters) and new (page, limit, filters)
    let orgId: string;
    let page: number;
    let limit: number;
    let actualFilters: Record<any, any>;

    if (typeof orgIdOrPage === 'string') {
      // Old signature: (orgId: string, page: number, limit: number, filters?)
      orgId = orgIdOrPage;
      page = pageOrLimit as number;
      limit = limitOrFilters as number;
      actualFilters = filters;
    } else {
      // New signature: (page: number, limit: number, filters?)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      page = orgIdOrPage;
      limit = pageOrLimit as number;
      actualFilters = (limitOrFilters as Record<any, any>) || {};
    }

    return http.get<PaginateResponse<Role>>(`/api/v1/organizations/${orgId}/roles`, { 
      filters: actualFilters, 
      page, 
      limit 
    });
  },

  getRole: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.get<any>(`/api/v1/organizations/${actualOrgId}/roles/${actualId}`);
  },

  deleteRole: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.delete<any>(`/api/v1/organizations/${actualOrgId}/roles/${actualId}`);
  },

  updateRole: (orgIdOrId: string, idOrBody: string | Partial<Role>, body?: Partial<Role>) => {
    // Support both old signature (orgId, id, body) and new (id, body)
    const actualOrgId = body ? orgIdOrId : getCurrentOrgId()!;
    const actualId = body ? (idOrBody as string) : orgIdOrId;
    const actualBody = body || (idOrBody as Partial<Role>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.patch<any>(`/api/v1/organizations/${actualOrgId}/roles/${actualId}`, actualBody);
  },

  createRole: (orgIdOrBody: string | Partial<Role>, body?: Partial<Role>) => {
    // Support both old signature (orgId, body) and new (body)
    const actualOrgId = body ? (orgIdOrBody as string) : getCurrentOrgId()!;
    const actualBody = body || (orgIdOrBody as Partial<Role>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post<any>(`/api/v1/organizations/${actualOrgId}/roles`, actualBody);
  },
};

export default rolesApiRequests;
