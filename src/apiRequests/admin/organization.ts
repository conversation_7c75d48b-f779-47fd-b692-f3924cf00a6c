import http, {PaginateResponse} from "@/lib/http";

// API Response type (what the API actually returns)
interface ApiOrganization {
  abbreviation: string;
  domain: string;
  orgName: string;
  id: string;
  createdAt: string;
  updatedAt: string;
}

const organizationsApiRequests = {
  getOrganizations: (
    page: number,
    limit: number,
    filters: Record<any, any> = {}
  ) => {
    return http.get<PaginateResponse<ApiOrganization>>(`/api/v1/organizations`, { filters, page, limit });
  },

  getOrganization: (id: string) => {
    return http.get(`/api/v1/organizations/${id}`);
  },

  updateOrganization: (id: string, data: any) => {
    return http.patch(`/api/v1/organizations/${id}`, data);
  },

  deleteOrganization: (id: string) => {
    return http.delete(`/api/v1/organizations/${id}`);
  },

  createOrganization: (data: any) => {
    return http.post(`/api/v1/organizations`, data);
  },
};

export default organizationsApiRequests;
