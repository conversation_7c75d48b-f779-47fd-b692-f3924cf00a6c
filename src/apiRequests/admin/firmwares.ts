import http, {PaginateResponse} from "@/lib/http";
import {Firmware} from "@/app/(DashboardLayout)/firmware/types";
import { getCurrentOrgId } from "@/lib/api-org-context";

const firmwaresApiRequests = {
  getFirmwares: (
    orgIdOrPage: string | number,
    pageOrLimit?: number,
    limitOrFilters?: number | Record<any, any>,
    filters: Record<any, any> = {},
  ) => {
    // Support both old signature (orgId, page, limit, filters) and new (page, limit, filters)
    let orgId: string;
    let page: number;
    let limit: number;
    let actualFilters: Record<any, any>;

    if (typeof orgIdOrPage === 'string') {
      // Old signature: (orgId: string, page: number, limit: number, filters?)
      orgId = orgIdOrPage;
      page = pageOrLimit as number;
      limit = limitOrFilters as number;
      actualFilters = filters;
    } else {
      // New signature: (page: number, limit: number, filters?)
      orgId = getCurrentOrgId()!;
      if (!orgId) {
        throw new Error('No organization selected. Please select an organization.');
      }
      page = orgIdOrPage;
      limit = pageOrLimit as number;
      actualFilters = (limitOrFilters as Record<any, any>) || {};
    }

    return http.get<PaginateResponse<Firmware>>(`/api/v1/organizations/${orgId}/firmwares`, { 
      filters: actualFilters, 
      page, 
      limit 
    });
  },

  getFirmware: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.get<Firmware>(`/api/v1/organizations/${actualOrgId}/firmwares/${actualId}`);
  },

  deleteFirmware: (orgIdOrId: string, id?: string) => {
    // Support both old signature (orgId, id) and new (id)
    const actualOrgId = id ? orgIdOrId : getCurrentOrgId()!;
    const actualId = id || orgIdOrId;
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.delete<any>(`/api/v1/organizations/${actualOrgId}/firmwares/${actualId}`);
  },

  updateFirmware: (orgIdOrId: string, idOrBody: string | Partial<Firmware>, body?: Partial<Firmware>) => {
    // Support both old signature (orgId, id, body) and new (id, body)
    const actualOrgId = body ? orgIdOrId : getCurrentOrgId()!;
    const actualId = body ? (idOrBody as string) : orgIdOrId;
    const actualBody = body || (idOrBody as Partial<Firmware>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.patch<Firmware>(`/api/v1/organizations/${actualOrgId}/firmwares/${actualId}`, actualBody);
  },

  createFirmware: (orgIdOrBody: string | Partial<Firmware>, body?: Partial<Firmware>) => {
    // Support both old signature (orgId, body) and new (body)
    const actualOrgId = body ? (orgIdOrBody as string) : getCurrentOrgId()!;
    const actualBody = body || (orgIdOrBody as Partial<Firmware>);
    
    if (!actualOrgId) {
      throw new Error('No organization selected. Please select an organization.');
    }
    
    return http.post<Firmware>(`/api/v1/organizations/${actualOrgId}/firmwares`, actualBody);
  },
};

export default firmwaresApiRequests;
