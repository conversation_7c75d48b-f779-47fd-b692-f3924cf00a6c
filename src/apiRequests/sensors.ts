import { API_URL } from "@/config/config";

const sensorsApiRequests = {
  export: async ({
    from,
    to,
    deviceIds,
  }: {
    from: string;
    to: string;
    deviceIds: string[];
  }, cancel?: (abortFn: () => void) => void) => {
    const deviceIdsQuery = deviceIds
      .map((device) => `deviceIds=${encodeURIComponent(device)}`)
      .join("&");

    const urlExport = `${API_URL}/api/v1/sensors/export?from=${encodeURIComponent(from)}&to=${encodeURIComponent(to)}&${deviceIdsQuery}`;

    const controller = new AbortController();
    if (cancel) {
      cancel(() => {
        console.log("Cancel");
        controller.abort();
      });
    }

    try {
      const response = await fetch(urlExport, {
        method: "GET",
        signal: controller.signal,
      });

      const contentDisposition = response.headers.get("content-disposition");
      let fileName = "Parameter.xlsx";
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        );
        if (filenameMatch && filenameMatch[1]) {
          fileName = filenameMatch[1].replace(/['"]/g, "");
        }
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
    } catch (err) {
      console.log(err);
    } finally {
    }
  },
};

export default sensorsApiRequests;
