// import http from "@/lib/http";
// import {
//   LoginBodyType,
//   RegisterBodyType,
//   LoginResType,
//   RegisterResType,
// } from "@/schemaValidations/auth.schema";
//
// const transactionsApiRequests = {
//   getTransactions: (
//     limit: number,
//     page: number,
//     status: string,
//     transactionType: string,
//     fromUser: string,
//     toUser: string
//   ) => {
//     return http.get<any>("/admin/transactions", {
//       search: {
//         limit,
//         page,
//         status,
//         transactionType,
//         fromUser,
//         toUser,
//       },
//     });
//   },
// };
//
// export default transactionsApiRequests;
