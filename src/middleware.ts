import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { cookies } from "next/headers";
import { isTokenValid, decodeJWT } from "@/lib/jwt";

const authPaths = ["/login", "/register"];

// Paths that require admin permissions (fallback for legacy compatibility)
const adminOnlyPaths = ["/settings"];

// Paths that are accessible to all authenticated users
const publicAuthenticatedPaths = ["/", "/permission-test"];

/**
 * Check if user has admin permissions from JWT token
 */
function hasAdminPermissions(token: string): boolean {
  try {
    const payload = decodeJWT(token);
    if (!payload || !payload.roles) {
      return false;
    }

    // Check for wildcard admin permissions
    return payload.roles.some(role =>
      role.permissions &&
      role.permissions['*'] &&
      role.permissions['*'].includes('*')
    );
  } catch (error) {
    console.log('Error checking admin permissions:', error);
    return false;
  }
}

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
  const cookieStore = await cookies();
  const sessionToken = cookieStore.get("sessionToken")?.value;
  const pathName = request.nextUrl.pathname;

  // Check if token exists and is valid
  const hasValidToken = sessionToken && isTokenValid(sessionToken);

  // Redirect to login if no valid token and not on auth paths
  if (!hasValidToken && !authPaths.includes(pathName)) {
    console.log('No valid token, redirecting to login');
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Redirect to dashboard if has valid token and on auth paths
  if (hasValidToken && authPaths.includes(pathName)) {
    console.log('Valid token found, redirecting to dashboard');
    return NextResponse.redirect(new URL("/", request.url));
  }

  // Check admin-only paths (legacy fallback)
  if (hasValidToken && adminOnlyPaths.includes(pathName)) {
    const isAdmin = hasAdminPermissions(sessionToken!);
    if (!isAdmin) {
      console.log('Admin access required, redirecting to dashboard');
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    "/about/:path*",
    "/dashboard/:path*",
    "/users/:path*",
    "/users",
    "/settings",
    "/devices/:path*",
    "/devices",
    "/devices-manager",
    "/firmware",
    "/code-push",
    "/policy",
    "/users",
    "/login",
    "/register",
    "/permission-test",
  ],
};
