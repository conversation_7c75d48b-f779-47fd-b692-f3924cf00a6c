/**
 * Utility for automatically using current organization ID in API calls
 * This allows existing API functions to work with the organization context
 */

// Helper function to get current org ID (for non-React contexts)
export function getCurrentOrgId(): string | null {
  // Try to get from localStorage first (most reliable)
  const storedOrgId = localStorage.getItem('currentOrgId');
  if (storedOrgId) {
    return storedOrgId;
  }

  // Fallback: try to get from JWT token directly
  try {
    // Dynamic import to avoid circular dependencies
    if (typeof window !== 'undefined') {
      const jwtPermissions = require('@/lib/jwt-permissions');
      const orgIds = jwtPermissions.getJWTUserOrgIds();
      return orgIds[0] || null;
    }
  } catch (error) {
    console.warn('Could not get current org ID:', error);
  }
  
  return null;
}

// Wrapper function that automatically injects current orgId for API calls
export function withCurrentOrgId<TArgs extends any[], TReturn>(
  apiFunction: (orgId: string, ...args: TArgs) => TReturn
) {
  return (...args: TArgs): TReturn | never => {
    const currentOrgId = getCurrentOrgId();
    if (!currentOrgId) {
      throw new Error('No organization selected. Please select an organization before making API calls.');
    }
    return apiFunction(currentOrgId, ...args);
  };
}

// Type-safe wrapper for API request objects
export function withOrgContext<T extends Record<string, any>>(apiRequests: T): T {
  const wrappedRequests = {} as T;
  
  for (const [key, func] of Object.entries(apiRequests)) {
    if (typeof func === 'function') {
      // Check if function already expects orgId as first parameter
      const funcString = func.toString();
      if (funcString.includes('orgId') && funcString.includes('orgId:')) {
        // Wrap the function to auto-inject orgId
        wrappedRequests[key as keyof T] = withCurrentOrgId(func) as T[keyof T];
      } else {
        // Function doesn't need orgId, keep as is
        wrappedRequests[key as keyof T] = func;
      }
    } else {
      wrappedRequests[key as keyof T] = func;
    }
  }
  
  return wrappedRequests;
}
