/**
 * JWT utilities for decoding and validating JWT tokens
 */

export interface JWTPayload {
  userId: string;
  roles: Array<{
    orgId: string;
    roleId: string;
    permissions: Record<string, string[]>;
  }>;
  sessionId: string;
  iat: number;
  exp: number;
}

/**
 * Decode JWT token without verification (client-side only)
 * @param token JWT token string
 * @returns Decoded payload or null if invalid
 */
export function decodeJWT(token: string): JWTPayload | null {
  try {
    if (!token || typeof token !== 'string') {
      return null;
    }

    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    const decoded = JSON.parse(
      Buffer.from(payload, 'base64').toString('utf-8')
    );

    // Validate required fields
    if (!decoded.userId || !decoded.roles || !Array.isArray(decoded.roles)) {
      return null;
    }

    return decoded as JWTPayload;
  } catch (error) {
    console.log('Error decoding JWT:', error);
    return null;
  }
}

/**
 * Check if JWT token is expired
 * @param token JWT token string
 * @returns true if expired, false if valid
 */
export function isTokenExpired(token: string): boolean {
  try {
    const payload = decodeJWT(token);
    if (!payload || !payload.exp) {
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.log('Error checking token expiration:', error);
    return true;
  }
}

/**
 * Get user ID from JWT token
 * @param token JWT token string
 * @returns User ID or null if invalid
 */
export function getUserIdFromToken(token: string): string | null {
  const payload = decodeJWT(token);
  return payload?.userId || null;
}

/**
 * Get user roles from JWT token
 * @param token JWT token string
 * @returns Array of roles or null if invalid
 */
export function getRolesFromToken(token: string): JWTPayload['roles'] | null {
  const payload = decodeJWT(token);
  return payload?.roles || null;
}

/**
 * Get session ID from JWT token
 * @param token JWT token string
 * @returns Session ID or null if invalid
 */
export function getSessionIdFromToken(token: string): string | null {
  const payload = decodeJWT(token);
  return payload?.sessionId || null;
}

/**
 * Get token expiration time
 * @param token JWT token string
 * @returns Expiration timestamp or null if invalid
 */
export function getTokenExpiration(token: string): number | null {
  const payload = decodeJWT(token);
  return payload?.exp || null;
}

/**
 * Check if token is valid (not expired and properly formatted)
 * @param token JWT token string
 * @returns true if valid, false otherwise
 */
export function isTokenValid(token: string): boolean {
  if (!token) return false;

  const payload = decodeJWT(token);
  if (!payload) return false;

  return !isTokenExpired(token);
}

/**
 * Sync JWT data with existing clientUserInfo structure
 * @param token JWT token string
 * @param existingUserData Existing user data from API
 * @returns Updated user info structure
 */
export function syncJWTWithUserInfo(token: string, existingUserData: any): any {
  try {
    const jwtPayload = decodeJWT(token);
    if (!jwtPayload) {
      return existingUserData;
    }

    // Extract roles and permissions from JWT
    const rolesFromJWT = jwtPayload.roles || [];

    // Update existing user data with JWT information
    const updatedUserData = {
      ...existingUserData,
      user: {
        ...existingUserData.user,
        id: jwtPayload.userId,
        // Add JWT-based roles if available
        roles: rolesFromJWT.map(role => ({
          orgId: role.orgId,
          permissions: role.permissions,
          // Keep existing role structure if available
          ...(existingUserData.user?.roles?.find((r: any) => r.orgId === role.orgId) || {}),
        })),
      },
      // Add JWT payload for direct access
      jwtPayload,
    };

    return updatedUserData;
  } catch (error) {
    console.log('Error syncing JWT with user info:', error);
    return existingUserData;
  }
}
