import type { ThemeColors } from "@/types/theme-types";

const themes = {
  // 🌊 Ocean Blue - Xanh đại dương hiện đại
  OceanBlue: {
    light: {
      background: "210 100% 99%",
      foreground: "210 22% 8%",
      card: "210 100% 98%",
      cardForeground: "210 22% 8%",
      popover: "0 0% 100%",
      popoverForeground: "210 22% 8%",
      primary: "210 100% 56%", // Xanh đại dương sáng
      primaryForeground: "0 0% 100%",
      secondary: "210 60% 96%",
      secondaryForeground: "210 22% 8%",
      muted: "210 40% 96%",
      mutedForeground: "210 16% 46%",
      accent: "210 80% 94%",
      accentForeground: "210 22% 8%",
      destructive: "0 84% 60%",
      destructiveForeground: "0 0% 100%",
      border: "210 40% 88%",
      input: "210 40% 88%",
      ring: "210 100% 56%",
      radius: "0.75rem",
      // IoT specific colors
      success: "142 76% 45%", // Xanh lá tươi
      warning: "38 100% 55%", // <PERSON> sáng
      info: "199 100% 50%", // <PERSON>anh thông tin
      device: "210 100% 56%", // M<PERSON>u thiết bị chính
      sensor: "142 76% 45%", // Màu cảm biến
      network: "199 100% 50%", // Màu mạng
      gradient: "linear-gradient(135deg, hsl(210, 100%, 56%) 0%, hsl(199, 100%, 50%) 100%)",
    },
    dark: {
      background: "210 50% 3%",
      foreground: "210 60% 98%",
      card: "210 50% 5%",
      cardForeground: "210 60% 98%",
      popover: "210 50% 5%",
      popoverForeground: "210 60% 98%",
      primary: "210 100% 65%",
      primaryForeground: "210 50% 3%",
      secondary: "210 30% 12%",
      secondaryForeground: "210 60% 98%",
      muted: "210 30% 12%",
      mutedForeground: "210 20% 65%",
      accent: "210 30% 15%",
      accentForeground: "210 60% 98%",
      destructive: "0 63% 50%",
      destructiveForeground: "210 60% 98%",
      border: "210 30% 15%",
      input: "210 30% 15%",
      ring: "210 100% 65%",
      radius: "0.75rem",
      // IoT specific colors
      success: "142 76% 50%",
      warning: "38 100% 60%",
      info: "199 100% 55%",
      device: "210 100% 65%",
      sensor: "142 76% 50%",
      network: "199 100% 55%",
      gradient: "linear-gradient(135deg, hsl(210, 100%, 65%) 0%, hsl(199, 100%, 55%) 100%)",
    },
  },

  // 🌿 Emerald Tech - Xanh ngọc lục bảo công nghệ
  EmeraldTech: {
    light: {
      background: "155 100% 98%",
      foreground: "155 25% 8%",
      card: "155 100% 97%",
      cardForeground: "155 25% 8%",
      popover: "0 0% 100%",
      popoverForeground: "155 25% 8%",
      primary: "155 100% 45%", // Xanh ngọc lục bảo
      primaryForeground: "0 0% 100%",
      secondary: "155 60% 95%",
      secondaryForeground: "155 25% 8%",
      muted: "155 40% 96%",
      mutedForeground: "155 16% 46%",
      accent: "155 80% 93%",
      accentForeground: "155 25% 8%",
      destructive: "0 84% 60%",
      destructiveForeground: "0 0% 100%",
      border: "155 40% 88%",
      input: "155 40% 88%",
      ring: "155 100% 45%",
      radius: "0.75rem",
      success: "155 100% 45%",
      warning: "38 100% 55%",
      info: "199 100% 50%",
      device: "155 100% 45%",
      sensor: "120 100% 35%",
      network: "199 100% 50%",
      gradient: "linear-gradient(135deg, hsl(155, 100%, 45%) 0%, hsl(120, 100%, 35%) 100%)",
    },
    dark: {
      background: "155 50% 3%",
      foreground: "155 60% 98%",
      card: "155 50% 5%",
      cardForeground: "155 60% 98%",
      popover: "155 50% 5%",
      popoverForeground: "155 60% 98%",
      primary: "155 100% 55%",
      primaryForeground: "155 50% 3%",
      secondary: "155 30% 12%",
      secondaryForeground: "155 60% 98%",
      muted: "155 30% 12%",
      mutedForeground: "155 20% 65%",
      accent: "155 30% 15%",
      accentForeground: "155 60% 98%",
      destructive: "0 63% 50%",
      destructiveForeground: "155 60% 98%",
      border: "155 30% 15%",
      input: "155 30% 15%",
      ring: "155 100% 55%",
      radius: "0.75rem",
      success: "155 100% 55%",
      warning: "38 100% 60%",
      info: "199 100% 55%",
      device: "155 100% 55%",
      sensor: "120 100% 40%",
      network: "199 100% 55%",
      gradient: "linear-gradient(135deg, hsl(155, 100%, 55%) 0%, hsl(120, 100%, 40%) 100%)",
    },
  },

  Red: {
    light: {
      background: "0 100% 95%",
      foreground: "0 20% 20%",
      card: "0 100% 98%",
      cardForeground: "0 20% 20%",
      popover: "0 100% 98%",
      popoverForeground: "0 20% 20%",
      primary: "0 90% 55%",
      primaryForeground: "0 0% 100%",
      secondary: "0 50% 85%",
      secondaryForeground: "0 20% 20%",
      muted: "0 50% 85%",
      mutedForeground: "0 30% 40%",
      accent: "0 50% 85%",
      accentForeground: "0 20% 20%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "0 30% 90%",
      input: "0 30% 90%",
      ring: "0 90% 55%",
      radius: "0.5rem",
    },
    dark: {
      background: "0 20% 10%",
      foreground: "0 40% 95%",
      card: "0 20% 10%",
      cardForeground: "0 40% 95%",
      popover: "0 20% 10%",
      popoverForeground: "0 40% 95%",
      primary: "0 90% 55%",
      primaryForeground: "0 0% 100%",
      secondary: "0 30% 15%",
      secondaryForeground: "0 40% 95%",
      muted: "0 30% 15%",
      mutedForeground: "0 40% 60%",
      accent: "0 30% 15%",
      accentForeground: "0 40% 95%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "0 30% 15%",
      input: "0 30% 15%",
      ring: "0 90% 55%",
    },
  },
  Amber: {
    light: {
      background: "45 100% 95%",
      foreground: "45 20% 20%",
      card: "45 100% 98%",
      cardForeground: "45 20% 20%",
      popover: "45 100% 98%",
      popoverForeground: "45 20% 20%",
      primary: "45 90% 60%",
      primaryForeground: "0 0% 100%",
      secondary: "45 50% 85%",
      secondaryForeground: "45 20% 20%",
      muted: "45 50% 85%",
      mutedForeground: "45 30% 40%",
      accent: "45 50% 85%",
      accentForeground: "45 20% 20%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "45 30% 90%",
      input: "45 30% 90%",
      ring: "45 90% 60%",
      radius: "0.5rem",
    },
    dark: {
      background: "45 20% 10%",
      foreground: "45 40% 95%",
      card: "45 20% 10%",
      cardForeground: "45 40% 95%",
      popover: "45 20% 10%",
      popoverForeground: "45 40% 95%",
      primary: "45 90% 60%",
      primaryForeground: "0 0% 100%",
      secondary: "45 30% 15%",
      secondaryForeground: "45 40% 95%",
      muted: "45 30% 15%",
      mutedForeground: "45 40% 60%",
      accent: "45 30% 15%",
      accentForeground: "45 40% 95%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "45 30% 15%",
      input: "45 30% 15%",
      ring: "45 90% 60%",
    },
  },
  Yellow: {
    light: {
      background: "50 100% 95%",
      foreground: "50 20% 20%",
      card: "50 100% 98%",
      cardForeground: "50 20% 20%",
      popover: "50 100% 98%",
      popoverForeground: "50 20% 20%",
      primary: "50 90% 55%",
      primaryForeground: "0 0% 100%",
      secondary: "50 50% 85%",
      secondaryForeground: "50 20% 20%",
      muted: "50 50% 85%",
      mutedForeground: "50 30% 40%",
      accent: "50 50% 85%",
      accentForeground: "50 20% 20%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "50 30% 90%",
      input: "50 30% 90%",
      ring: "50 90% 55%",
      radius: "0.5rem",
    },
    dark: {
      background: "50 20% 10%",
      foreground: "50 40% 95%",
      card: "50 20% 10%",
      cardForeground: "50 40% 95%",
      popover: "50 20% 10%",
      popoverForeground: "50 40% 95%",
      primary: "50 90% 55%",
      primaryForeground: "0 0% 100%",
      secondary: "50 30% 15%",
      secondaryForeground: "50 40% 95%",
      muted: "50 30% 15%",
      mutedForeground: "50 40% 60%",
      accent: "50 30% 15%",
      accentForeground: "50 40% 95%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "50 30% 15%",
      input: "50 30% 15%",
      ring: "50 90% 55%",
    },
  },
  Sky: {
    light: {
      background: "200 100% 95%",
      foreground: "200 20% 20%",
      card: "200 100% 98%",
      cardForeground: "200 20% 20%",
      popover: "200 100% 98%",
      popoverForeground: "200 20% 20%",
      primary: "200 90% 60%",
      primaryForeground: "0 0% 100%",
      secondary: "200 50% 85%",
      secondaryForeground: "200 20% 20%",
      muted: "200 50% 85%",
      mutedForeground: "200 30% 40%",
      accent: "200 50% 85%",
      accentForeground: "200 20% 20%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "200 30% 90%",
      input: "200 30% 90%",
      ring: "200 90% 60%",
      radius: "0.5rem",
    },
    dark: {
      background: "200 20% 10%",
      foreground: "200 40% 95%",
      card: "200 20% 10%",
      cardForeground: "200 40% 95%",
      popover: "200 20% 10%",
      popoverForeground: "200 40% 95%",
      primary: "200 90% 60%",
      primaryForeground: "0 0% 100%",
      secondary: "200 30% 15%",
      secondaryForeground: "200 40% 95%",
      muted: "200 30% 15%",
      mutedForeground: "200 40% 60%",
      accent: "200 30% 15%",
      accentForeground: "200 40% 95%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "200 30% 15%",
      input: "200 30% 15%",
      ring: "200 90% 60%",
    },
  },
  Green: {
    light: {
      background: "140 100% 97%",
      foreground: "140 20% 20%",
      card: "140 100% 98%",
      cardForeground: "140 20% 20%",
      popover: "140 100% 98%",
      popoverForeground: "140 20% 20%",
      primary: "140 70% 50%",
      primaryForeground: "0 0% 100%",
      secondary: "140 50% 85%",
      secondaryForeground: "140 20% 20%",
      muted: "140 50% 85%",
      mutedForeground: "140 30% 40%",
      accent: "140 50% 85%",
      accentForeground: "140 20% 20%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "140 30% 90%",
      input: "140 30% 90%",
      ring: "140 70% 50%",
      radius: "0.5rem",
    },
    dark: {
      background: "140 20% 10%",
      foreground: "140 40% 95%",
      card: "140 20% 10%",
      cardForeground: "140 40% 95%",
      popover: "140 20% 10%",
      popoverForeground: "140 40% 95%",
      primary: "140 70% 50%",
      primaryForeground: "0 0% 100%",
      secondary: "140 30% 15%",
      secondaryForeground: "140 40% 95%",
      muted: "140 30% 15%",
      mutedForeground: "140 40% 60%",
      accent: "140 30% 15%",
      accentForeground: "140 40% 95%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "140 30% 15%",
      input: "140 30% 15%",
      ring: "140 70% 50%",
    },
  },
  Rose: {
    light: {
      background: "340 100% 97%",
      foreground: "340 20% 20%",
      card: "340 100% 98%",
      cardForeground: "340 20% 20%",
      popover: "340 100% 98%",
      popoverForeground: "340 20% 20%",
      primary: "340 80% 60%",
      primaryForeground: "0 0% 100%",
      secondary: "340 50% 85%",
      secondaryForeground: "340 20% 20%",
      muted: "340 50% 85%",
      mutedForeground: "340 30% 40%",
      accent: "340 50% 85%",
      accentForeground: "340 20% 20%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "340 30% 90%",
      input: "340 30% 90%",
      ring: "340 80% 60%",
      radius: "0.5rem",
    },
    dark: {
      background: "340 20% 10%",
      foreground: "340 40% 95%",
      card: "340 20% 10%",
      cardForeground: "340 40% 95%",
      popover: "340 20% 10%",
      popoverForeground: "340 40% 95%",
      primary: "340 80% 60%",
      primaryForeground: "0 0% 100%",
      secondary: "340 30% 15%",
      secondaryForeground: "340 40% 95%",
      muted: "340 30% 15%",
      mutedForeground: "340 40% 60%",
      accent: "340 30% 15%",
      accentForeground: "340 40% 95%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "340 30% 15%",
      input: "340 30% 15%",
      ring: "340 80% 60%",
    },
  },
  Cyan: {
    light: {
      background: "190 60% 95%",
      foreground: "190 20% 20%",
      card: "190 60% 98%",
      cardForeground: "190 20% 20%",
      popover: "190 60% 98%",
      popoverForeground: "190 20% 20%",
      primary: "190 70% 50%",
      primaryForeground: "0 0% 100%",
      secondary: "190 40% 85%",
      secondaryForeground: "190 20% 20%",
      muted: "190 40% 85%",
      mutedForeground: "190 30% 40%",
      accent: "190 40% 85%",
      accentForeground: "190 20% 20%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "190 30% 90%",
      input: "190 30% 90%",
      ring: "190 70% 50%",
      radius: "0.5rem",
    },
    dark: {
      background: "190 20% 10%",
      foreground: "190 40% 95%",
      card: "190 20% 10%",
      cardForeground: "190 40% 95%",
      popover: "190 20% 10%",
      popoverForeground: "190 40% 95%",
      primary: "190 70% 50%",
      primaryForeground: "0 0% 100%",
      secondary: "190 30% 15%",
      secondaryForeground: "190 40% 95%",
      muted: "190 30% 15%",
      mutedForeground: "190 40% 60%",
      accent: "190 30% 15%",
      accentForeground: "190 40% 95%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "60 9.1% 97.8%",
      border: "190 30% 15%",
      input: "190 30% 15%",
      ring: "190 70% 50%",
    },
  },

  // 🌌 Blue Purple - Gradient xanh tím hiện đại
  BluePurple: {
    light: {
      background: "220 100% 99%",
      foreground: "220 25% 8%",
      card: "220 100% 98%",
      cardForeground: "220 25% 8%",
      popover: "0 0% 100%",
      popoverForeground: "220 25% 8%",
      primary: "220 100% 55%", // Xanh dương chính
      primaryForeground: "0 0% 100%",
      secondary: "220 60% 96%",
      secondaryForeground: "220 25% 8%",
      muted: "220 40% 96%",
      mutedForeground: "220 16% 46%",
      accent: "270 80% 94%", // Tím nhạt cho accent
      accentForeground: "270 25% 8%",
      destructive: "0 84% 60%",
      destructiveForeground: "0 0% 100%",
      border: "220 40% 88%",
      input: "220 40% 88%",
      ring: "220 100% 55%",
      radius: "0.75rem",
      success: "142 76% 45%",
      warning: "38 100% 55%",
      info: "199 100% 50%",
      device: "220 100% 55%",
      sensor: "270 100% 60%", // Tím cho sensor
      network: "199 100% 50%",
      gradient: "linear-gradient(135deg, hsl(220, 100%, 55%) 0%, hsl(270, 100%, 60%) 100%)",
    },
    dark: {
      background: "220 50% 3%",
      foreground: "220 60% 98%",
      card: "220 50% 5%",
      cardForeground: "220 60% 98%",
      popover: "220 50% 5%",
      popoverForeground: "220 60% 98%",
      primary: "220 100% 65%",
      primaryForeground: "220 50% 3%",
      secondary: "220 30% 12%",
      secondaryForeground: "220 60% 98%",
      muted: "220 30% 12%",
      mutedForeground: "220 20% 65%",
      accent: "270 30% 15%",
      accentForeground: "270 60% 98%",
      destructive: "0 63% 50%",
      destructiveForeground: "220 60% 98%",
      border: "220 30% 15%",
      input: "220 30% 15%",
      ring: "220 100% 65%",
      radius: "0.75rem",
      success: "142 76% 50%",
      warning: "38 100% 60%",
      info: "199 100% 55%",
      device: "220 100% 65%",
      sensor: "270 100% 70%",
      network: "199 100% 55%",
      gradient: "linear-gradient(135deg, hsl(220, 100%, 65%) 0%, hsl(270, 100%, 70%) 100%)",
    },
  },

  // 🔮 Cosmic Purple - Tím vũ trụ hiện đại
  CosmicPurple: {
    light: {
      background: "270 100% 99%",
      foreground: "270 25% 8%",
      card: "270 100% 98%",
      cardForeground: "270 25% 8%",
      popover: "0 0% 100%",
      popoverForeground: "270 25% 8%",
      primary: "270 100% 60%", // Tím vũ trụ
      primaryForeground: "0 0% 100%",
      secondary: "270 60% 96%",
      secondaryForeground: "270 25% 8%",
      muted: "270 40% 96%",
      mutedForeground: "270 16% 46%",
      accent: "270 80% 94%",
      accentForeground: "270 25% 8%",
      destructive: "0 84% 60%",
      destructiveForeground: "0 0% 100%",
      border: "270 40% 88%",
      input: "270 40% 88%",
      ring: "270 100% 60%",
      radius: "0.75rem",
      success: "142 76% 45%",
      warning: "38 100% 55%",
      info: "199 100% 50%",
      device: "270 100% 60%",
      sensor: "142 76% 45%",
      network: "199 100% 50%",
      gradient: "linear-gradient(135deg, hsl(270, 100%, 60%) 0%, hsl(300, 100%, 65%) 100%)",
    },
    dark: {
      background: "270 50% 3%",
      foreground: "270 60% 98%",
      card: "270 50% 5%",
      cardForeground: "270 60% 98%",
      popover: "270 50% 5%",
      popoverForeground: "270 60% 98%",
      primary: "270 100% 70%",
      primaryForeground: "270 50% 3%",
      secondary: "270 30% 12%",
      secondaryForeground: "270 60% 98%",
      muted: "270 30% 12%",
      mutedForeground: "270 20% 65%",
      accent: "270 30% 15%",
      accentForeground: "270 60% 98%",
      destructive: "0 63% 50%",
      destructiveForeground: "270 60% 98%",
      border: "270 30% 15%",
      input: "270 30% 15%",
      ring: "270 100% 70%",
      radius: "0.75rem",
      success: "142 76% 50%",
      warning: "38 100% 60%",
      info: "199 100% 55%",
      device: "270 100% 70%",
      sensor: "142 76% 50%",
      network: "199 100% 55%",
      gradient: "linear-gradient(135deg, hsl(270, 100%, 70%) 0%, hsl(300, 100%, 75%) 100%)",
    },
  },

  // 🌅 Sunset Glow - Hoàng hôn rực rỡ
  SunsetGlow: {
    light: {
      background: "25 100% 98%",
      foreground: "25 25% 8%",
      card: "25 100% 97%",
      cardForeground: "25 25% 8%",
      popover: "0 0% 100%",
      popoverForeground: "25 25% 8%",
      primary: "25 100% 55%", // Cam hoàng hôn
      primaryForeground: "0 0% 100%",
      secondary: "25 60% 95%",
      secondaryForeground: "25 25% 8%",
      muted: "25 40% 96%",
      mutedForeground: "25 16% 46%",
      accent: "25 80% 93%",
      accentForeground: "25 25% 8%",
      destructive: "0 84% 60%",
      destructiveForeground: "0 0% 100%",
      border: "25 40% 88%",
      input: "25 40% 88%",
      ring: "25 100% 55%",
      radius: "0.75rem",
      success: "142 76% 45%",
      warning: "38 100% 55%",
      info: "199 100% 50%",
      device: "25 100% 55%",
      sensor: "142 76% 45%",
      network: "199 100% 50%",
      gradient: "linear-gradient(135deg, hsl(25, 100%, 55%) 0%, hsl(340, 100%, 65%) 100%)",
    },
    dark: {
      background: "25 50% 3%",
      foreground: "25 60% 98%",
      card: "25 50% 5%",
      cardForeground: "25 60% 98%",
      popover: "25 50% 5%",
      popoverForeground: "25 60% 98%",
      primary: "25 100% 65%",
      primaryForeground: "25 50% 3%",
      secondary: "25 30% 12%",
      secondaryForeground: "25 60% 98%",
      muted: "25 30% 12%",
      mutedForeground: "25 20% 65%",
      accent: "25 30% 15%",
      accentForeground: "25 60% 98%",
      destructive: "0 63% 50%",
      destructiveForeground: "25 60% 98%",
      border: "25 30% 15%",
      input: "25 30% 15%",
      ring: "25 100% 65%",
      radius: "0.75rem",
      success: "142 76% 50%",
      warning: "38 100% 60%",
      info: "199 100% 55%",
      device: "25 100% 65%",
      sensor: "142 76% 50%",
      network: "199 100% 55%",
      gradient: "linear-gradient(135deg, hsl(25, 100%, 65%) 0%, hsl(340, 100%, 75%) 100%)",
    },
  },

  Violet: {
    light: {
      background: "250 100% 98%",
      foreground: "250 20% 20%",
      card: "250 100% 98%",
      cardForeground: "250 20% 20%",
      popover: "250 100% 98%",
      popoverForeground: "250 20% 20%",
      primary: "252 75% 60%",
      primaryForeground: "240 100% 98%",
      secondary: "250 50% 95%",
      secondaryForeground: "250 30% 30%",
      muted: "250 50% 95%",
      mutedForeground: "250 15% 50%",
      accent: "250 50% 95%",
      accentForeground: "250 30% 30%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "240 100% 98%",
      border: "250 20% 90%",
      input: "250 20% 90%",
      ring: "252 75% 60%",
      radius: "0.5rem",
    },
    dark: {
      background: "250 20% 10%",
      foreground: "240 100% 98%",
      card: "250 20% 10%",
      cardForeground: "240 100% 98%",
      popover: "250 20% 10%",
      popoverForeground: "240 100% 98%",
      primary: "252 75% 60%",
      primaryForeground: "240 100% 98%",
      secondary: "250 15% 20%",
      secondaryForeground: "240 100% 98%",
      muted: "250 15% 20%",
      mutedForeground: "250 10% 60%",
      accent: "250 15% 20%",
      accentForeground: "240 100% 98%",
      destructive: "0 72.2% 50.6%",
      destructiveForeground: "240 100% 98%",
      border: "250 15% 20%",
      input: "250 15% 20%",
      ring: "252 75% 60%",
    },
  },
  Zinc: {
    light: {
      background: "0 0% 100%",
      foreground: "240 10% 3.9%",
      card: "0 0% 100%",
      cardForeground: "240 10% 3.9%",
      popover: "0 0% 100%",
      popoverForeground: "240 10% 3.9%",
      primary: "240 5.9% 10%",
      primaryForeground: "0 0% 98%",
      secondary: "240 4.8% 95.9%",
      secondaryForeground: "240 5.9% 10%",
      muted: "240 4.8% 95.9%",
      mutedForeground: "240 3.8% 46.1%",
      accent: "240 4.8% 95.9%",
      accentForeground: "240 5.9% 10%",
      destructive: "0 84.2% 60.2%",
      destructiveForeground: "0 0% 98%",
      border: "240 5.9% 90%",
      input: "240 5.9% 90%",
      ring: "240 5.9% 10%",
      radius: "0.5rem",
    },
    dark: {
      background: "240 10% 3.9%",
      foreground: "0 0% 98%",
      card: "240 10% 3.9%",
      cardForeground: "0 0% 98%",
      popover: "240 10% 3.9%",
      popoverForeground: "0 0% 98%",
      primary: "0 0% 98%",
      primaryForeground: "240 5.9% 10%",
      secondary: "240 3.7% 15.9%",
      secondaryForeground: "0 0% 98%",
      muted: "240 3.7% 15.9%",
      mutedForeground: "240 5% 64.9%",
      accent: "240 3.7% 15.9%",
      accentForeground: "0 0% 98%",
      destructive: "0 62.8% 30.6%",
      destructiveForeground: "0 0% 98%",
      border: "240 3.7% 15.9%",
      input: "240 3.7% 15.9%",
      ring: "240 4.9% 83.9%",
    },
  },
};

export default function setGlobalColorTheme(
  themeMode: "light" | "dark",
  color: ThemeColors
) {
  const theme = themes[color][themeMode] as {
    [key: string]: string;
  };

  for (const key in theme) {
    document.documentElement.style.setProperty(`--${key}`, theme[key]);
  }
}
