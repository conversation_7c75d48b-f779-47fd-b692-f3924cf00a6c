/**
 * Clean JWT-based permission checking system
 * This replaces the complex legacy permission system with simple JWT token validation
 */

import { decodeJWT, type JWTPayload } from './jwt';
import { clientSessionToken, clientUserInfo } from './http';

export interface JWTPermissionCheck {
  resource: string;
  action?: string; // defaults to 'get'
  orgId?: string;
}

/**
 * Check if user has permission for a specific resource and action
 * Based on JWT token payload format: { roles: [{ orgId, permissions: { resource: [actions] } }] }
 */
export function checkJWTPermission(
  resource: string, 
  action: string = 'get', 
  orgId?: string
): boolean {
  try {
    const token = clientSessionToken.value;
    if (!token) return false;

    const payload = decodeJWT(token);
    if (!payload || !payload.roles) return false;

    // Get current user's orgId if not provided
    const userOrgId = orgId || clientUserInfo.value?.user?.orgId;

    // Check permissions for each role
    for (const role of payload.roles) {
      // If orgId is specified, only check roles for that org
      if (userOrgId && role.orgId !== userOrgId) {
        continue;
      }

      const permissions = role.permissions;
      if (!permissions) continue;

      // Check for global wildcard permissions (admin access)
      if (permissions['*'] && permissions['*'].includes('*')) {
        return true;
      }

      // Check for resource wildcard with specific action or wildcard action
      if (permissions['*'] && (permissions['*'].includes(action) || permissions['*'].includes('*'))) {
        return true;
      }

      // Check for specific resource permissions with wildcard action
      if (permissions[resource] && permissions[resource].includes('*')) {
        return true;
      }

      // Check for specific resource with specific action
      if (permissions[resource] && permissions[resource].includes(action)) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.log('Error checking JWT permission:', error);
    return false;
  }
}

/**
 * Check if user has any of the specified permissions
 */
export function checkAnyJWTPermission(permissions: JWTPermissionCheck[]): boolean {
  return permissions.some(({ resource, action = 'get', orgId }) =>
    checkJWTPermission(resource, action, orgId)
  );
}

/**
 * Check if user has all of the specified permissions
 */
export function checkAllJWTPermissions(permissions: JWTPermissionCheck[]): boolean {
  return permissions.every(({ resource, action = 'get', orgId }) =>
    checkJWTPermission(resource, action, orgId)
  );
}

/**
 * Check if user is admin (has wildcard permissions)
 */
export function isJWTAdmin(orgId?: string): boolean {
  return checkJWTPermission('*', '*', orgId);
}

/**
 * Get all user permissions from JWT token
 */
export function getJWTUserPermissions(orgId?: string): Record<string, string[]> {
  try {
    const token = clientSessionToken.value;
    if (!token) return {};

    const payload = decodeJWT(token);
    if (!payload || !payload.roles) return {};

    const userOrgId = orgId || clientUserInfo.value?.user?.orgId;
    const allPermissions: Record<string, Set<string>> = {};

    // Collect permissions from all roles
    for (const role of payload.roles) {
      // If orgId is specified, only include roles for that org
      if (userOrgId && role.orgId !== userOrgId) {
        continue;
      }

      const permissions = role.permissions;
      if (!permissions) continue;

      // Merge permissions
      for (const [resource, actions] of Object.entries(permissions)) {
        if (!allPermissions[resource]) {
          allPermissions[resource] = new Set();
        }
        actions.forEach(action => allPermissions[resource].add(action));
      }
    }

    // Convert Sets back to arrays
    const result: Record<string, string[]> = {};
    for (const [resource, actionsSet] of Object.entries(allPermissions)) {
      result[resource] = Array.from(actionsSet);
    }

    return result;
  } catch (error) {
    console.log('Error getting JWT user permissions:', error);
    return {};
  }
}

/**
 * Get user's organization IDs from JWT token
 */
export function getJWTUserOrgIds(): string[] {
  try {
    const token = clientSessionToken.value;
    if (!token) return [];

    const payload = decodeJWT(token);
    if (!payload || !payload.roles) return [];

    return payload.roles.map(role => role.orgId);
  } catch (error) {
    console.log('Error getting JWT user org IDs:', error);
    return [];
  }
}

/**
 * Check if user has access to specific organization
 */
export function hasJWTOrgAccess(orgId: string): boolean {
  const userOrgIds = getJWTUserOrgIds();
  return userOrgIds.includes(orgId);
}

/**
 * Hook for JWT permission checking in React components
 */
export function useJWTPermissions() {
  return {
    checkPermission: checkJWTPermission,
    checkAnyPermission: checkAnyJWTPermission,
    checkAllPermissions: checkAllJWTPermissions,
    isAdmin: isJWTAdmin,
    getUserPermissions: getJWTUserPermissions,
    getUserOrgIds: getJWTUserOrgIds,
    hasOrgAccess: hasJWTOrgAccess,
  };
}

// Export commonly used permission checks
export const PERMISSION_ACTIONS = {
  GET: 'get',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  ALL: '*'
};

export const RESOURCES = {
  ALL: '*',
  USER: 'user',
  ORGANIZATION: 'organization',
  DEVICE: 'device',
  FIRMWARE: 'firmware',
  CODE_PUSH: 'codePush',
  ROLE: 'role',
  PERMISSION: 'permission',
};
