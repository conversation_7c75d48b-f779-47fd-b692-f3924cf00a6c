// Device Status Color System for IoT Smart Devices

export const deviceStatusColors = {
  online: {
    bg: "bg-emerald-50 dark:bg-emerald-950/20",
    text: "text-emerald-700 dark:text-emerald-400",
    border: "border-emerald-200 dark:border-emerald-800",
    icon: "text-emerald-500",
    dot: "bg-emerald-500",
    gradient: "from-emerald-400 to-emerald-600"
  },
  offline: {
    bg: "bg-slate-50 dark:bg-slate-950/20",
    text: "text-slate-700 dark:text-slate-400",
    border: "border-slate-200 dark:border-slate-800",
    icon: "text-slate-500",
    dot: "bg-slate-500",
    gradient: "from-slate-400 to-slate-600"
  },
  warning: {
    bg: "bg-amber-50 dark:bg-amber-950/20",
    text: "text-amber-700 dark:text-amber-400",
    border: "border-amber-200 dark:border-amber-800",
    icon: "text-amber-500",
    dot: "bg-amber-500",
    gradient: "from-amber-400 to-amber-600"
  },
  error: {
    bg: "bg-red-50 dark:bg-red-950/20",
    text: "text-red-700 dark:text-red-400",
    border: "border-red-200 dark:border-red-800",
    icon: "text-red-500",
    dot: "bg-red-500",
    gradient: "from-red-400 to-red-600"
  },
  maintenance: {
    bg: "bg-blue-50 dark:bg-blue-950/20",
    text: "text-blue-700 dark:text-blue-400",
    border: "border-blue-200 dark:border-blue-800",
    icon: "text-blue-500",
    dot: "bg-blue-500",
    gradient: "from-blue-400 to-blue-600"
  }
};

export const deviceTypeColors = {
  light: {
    bg: "bg-yellow-50 dark:bg-yellow-950/20",
    text: "text-yellow-700 dark:text-yellow-400",
    border: "border-yellow-200 dark:border-yellow-800",
    icon: "text-yellow-500",
    gradient: "from-yellow-400 to-yellow-600"
  },
  sensor: {
    bg: "bg-green-50 dark:bg-green-950/20",
    text: "text-green-700 dark:text-green-400",
    border: "border-green-200 dark:border-green-800",
    icon: "text-green-500",
    gradient: "from-green-400 to-green-600"
  },
  camera: {
    bg: "bg-purple-50 dark:bg-purple-950/20",
    text: "text-purple-700 dark:text-purple-400",
    border: "border-purple-200 dark:border-purple-800",
    icon: "text-purple-500",
    gradient: "from-purple-400 to-purple-600"
  },
  thermostat: {
    bg: "bg-orange-50 dark:bg-orange-950/20",
    text: "text-orange-700 dark:text-orange-400",
    border: "border-orange-200 dark:border-orange-800",
    icon: "text-orange-500",
    gradient: "from-orange-400 to-orange-600"
  },
  lock: {
    bg: "bg-indigo-50 dark:bg-indigo-950/20",
    text: "text-indigo-700 dark:text-indigo-400",
    border: "border-indigo-200 dark:border-indigo-800",
    icon: "text-indigo-500",
    gradient: "from-indigo-400 to-indigo-600"
  },
  speaker: {
    bg: "bg-pink-50 dark:bg-pink-950/20",
    text: "text-pink-700 dark:text-pink-400",
    border: "border-pink-200 dark:border-pink-800",
    icon: "text-pink-500",
    gradient: "from-pink-400 to-pink-600"
  },
  switch: {
    bg: "bg-cyan-50 dark:bg-cyan-950/20",
    text: "text-cyan-700 dark:text-cyan-400",
    border: "border-cyan-200 dark:border-cyan-800",
    icon: "text-cyan-500",
    gradient: "from-cyan-400 to-cyan-600"
  },
  default: {
    bg: "bg-slate-50 dark:bg-slate-950/20",
    text: "text-slate-700 dark:text-slate-400",
    border: "border-slate-200 dark:border-slate-800",
    icon: "text-slate-500",
    gradient: "from-slate-400 to-slate-600"
  }
};

export const networkQualityColors = {
  excellent: {
    bg: "bg-emerald-50 dark:bg-emerald-950/20",
    text: "text-emerald-700 dark:text-emerald-400",
    icon: "text-emerald-500",
    bars: "text-emerald-500"
  },
  good: {
    bg: "bg-green-50 dark:bg-green-950/20",
    text: "text-green-700 dark:text-green-400",
    icon: "text-green-500",
    bars: "text-green-500"
  },
  fair: {
    bg: "bg-yellow-50 dark:bg-yellow-950/20",
    text: "text-yellow-700 dark:text-yellow-400",
    icon: "text-yellow-500",
    bars: "text-yellow-500"
  },
  poor: {
    bg: "bg-orange-50 dark:bg-orange-950/20",
    text: "text-orange-700 dark:text-orange-400",
    icon: "text-orange-500",
    bars: "text-orange-500"
  },
  disconnected: {
    bg: "bg-red-50 dark:bg-red-950/20",
    text: "text-red-700 dark:text-red-400",
    icon: "text-red-500",
    bars: "text-red-500"
  }
};

export const energyUsageColors = {
  low: {
    bg: "bg-green-50 dark:bg-green-950/20",
    text: "text-green-700 dark:text-green-400",
    icon: "text-green-500",
    progress: "bg-green-500"
  },
  medium: {
    bg: "bg-yellow-50 dark:bg-yellow-950/20",
    text: "text-yellow-700 dark:text-yellow-400",
    icon: "text-yellow-500",
    progress: "bg-yellow-500"
  },
  high: {
    bg: "bg-orange-50 dark:bg-orange-950/20",
    text: "text-orange-700 dark:text-orange-400",
    icon: "text-orange-500",
    progress: "bg-orange-500"
  },
  critical: {
    bg: "bg-red-50 dark:bg-red-950/20",
    text: "text-red-700 dark:text-red-400",
    icon: "text-red-500",
    progress: "bg-red-500"
  }
};

// Helper functions
export const getDeviceStatusColor = (status: string) => {
  const normalizedStatus = status.toLowerCase();
  return deviceStatusColors[normalizedStatus as keyof typeof deviceStatusColors] || deviceStatusColors.offline;
};

export const getDeviceTypeColor = (type: string) => {
  const normalizedType = type.toLowerCase().replace(/[_-]/g, '');
  
  // Map common device types
  if (normalizedType.includes('light') || normalizedType.includes('bulb')) return deviceTypeColors.light;
  if (normalizedType.includes('sensor') || normalizedType.includes('temperature') || normalizedType.includes('humidity')) return deviceTypeColors.sensor;
  if (normalizedType.includes('camera') || normalizedType.includes('security')) return deviceTypeColors.camera;
  if (normalizedType.includes('thermostat') || normalizedType.includes('hvac')) return deviceTypeColors.thermostat;
  if (normalizedType.includes('lock') || normalizedType.includes('door')) return deviceTypeColors.lock;
  if (normalizedType.includes('speaker') || normalizedType.includes('audio')) return deviceTypeColors.speaker;
  if (normalizedType.includes('switch') || normalizedType.includes('outlet')) return deviceTypeColors.switch;
  
  return deviceTypeColors.default;
};

export const getNetworkQualityColor = (signalStrength: number) => {
  if (signalStrength >= 80) return networkQualityColors.excellent;
  if (signalStrength >= 60) return networkQualityColors.good;
  if (signalStrength >= 40) return networkQualityColors.fair;
  if (signalStrength >= 20) return networkQualityColors.poor;
  return networkQualityColors.disconnected;
};

export const getEnergyUsageColor = (usage: number, maxUsage: number = 100) => {
  const percentage = (usage / maxUsage) * 100;
  
  if (percentage <= 25) return energyUsageColors.low;
  if (percentage <= 50) return energyUsageColors.medium;
  if (percentage <= 75) return energyUsageColors.high;
  return energyUsageColors.critical;
};

// Gradient backgrounds for cards
export const cardGradients = {
  primary: "bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950/20 dark:to-indigo-950/20",
  success: "bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-950/20 dark:to-emerald-950/20",
  warning: "bg-gradient-to-br from-yellow-50 to-amber-100 dark:from-yellow-950/20 dark:to-amber-950/20",
  error: "bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-950/20 dark:to-rose-950/20",
  info: "bg-gradient-to-br from-cyan-50 to-blue-100 dark:from-cyan-950/20 dark:to-blue-950/20",
  purple: "bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-950/20 dark:to-violet-950/20",
  glass: "bg-white/70 dark:bg-slate-900/70 backdrop-blur-sm",
  glassMorphism: "bg-white/10 dark:bg-slate-900/10 backdrop-blur-md border border-white/20 dark:border-slate-700/20"
};

// Animation classes for device states
export const deviceAnimations = {
  online: "animate-pulse",
  connecting: "animate-spin",
  updating: "animate-bounce",
  error: "animate-pulse",
  idle: ""
};
