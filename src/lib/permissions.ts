/**
 * @deprecated Use /lib/jwt-permissions.ts instead
 * This file is kept for backward compatibility and will be removed
 */

import {
  checkJWTPermission,
  checkAnyJWTPermission, 
  checkAllJWTPermissions,
  isJWTAdmin,
  getJWTUserPermissions,
  getJWTUserOrgIds,
  hasJWTOrgAccess,
  type J<PERSON><PERSON>er<PERSON>Check,
} from './jwt-permissions';

// Legacy function signatures maintained for compatibility
export function hasPermission(
  token: string,
  resource: string,
  action: string,
  orgId?: string
): boolean {
  return checkJWTPermission(resource, action, orgId);
}

export function hasAnyPermission(
  token: string,
  permissions: Array<{ resource: string; action: string }>,
  orgId?: string
): boolean {
  return checkAnyJWTPermission(permissions.map(p => ({ ...p, orgId })));
}

export function hasAllPermissions(
  token: string,
  permissions: Array<{ resource: string; action: string }>,
  orgId?: string
): boolean {
  return checkAllJWTPermissions(permissions.map(p => ({ ...p, orgId })));
}

export function getUserPermissions(token: string, orgId?: string): Record<string, string[]> {
  return getJWTUserPermissions(orgId);
}

export function isAdmin(token: string, orgId?: string): boolean {
  return isJWTAdmin(orgId);
}

export function getUserOrgIds(token: string): string[] {
  return getJWTUserOrgIds();
}

export function hasOrgAccess(token: string, orgId: string): boolean {
  return hasJWTOrgAccess(orgId);
}

// Export types for backward compatibility
export type PermissionCheck = {
  resource: string;
  action: string;
  orgId?: string;
};

export type EnhancedPermissionCheck = PermissionCheck & {
  permissionId?: string;
};