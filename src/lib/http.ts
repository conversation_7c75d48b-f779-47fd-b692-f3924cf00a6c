import authApiRequests from "@/apiRequests/user/auth";
import { API_URL } from "@/config/config";
import { isEmpty, isNil, omitBy } from "lodash";

export interface PaginateResponse<T> {
  docs: T[];
  totalDocs: number;
  limit: number;
  page?: number | undefined;
  totalPages: number;
  nextPage?: number | null | undefined;
  prevPage?: number | null | undefined;
  pagingCounter: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  meta?: any;
  [customLabel: string]: T[] | number | boolean | null | undefined;
}

type CustomOptions = Omit<RequestInit, "method"> & {
  baseUrl?: string | undefined;
  filters?: object | undefined;
  page?: number | undefined;
  limit?: number | undefined;
  token?: string | undefined;
  skipRefreshToken?: boolean;
};

interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}

interface UploadResponse {
  id: string;
  path: string;
}

interface UploadOptions {
  onProgress?: (progress: number) => void;
  headers?: Record<string, string>;
  baseUrl?: string;
  token?: string;
  skipRefreshToken?: boolean;
}

interface HttpErrorPayload {
  message?: string;
  errors?: Record<string, string>;
  [key: string]: any;
}

class HttpError extends Error {
  status: number;
  payload: HttpErrorPayload;
  constructor(
    status: number,
    payload: HttpErrorPayload,
    message: string = "Unknown error"
  ) {
    super(`Http Error ${status}: ${message}`);
    this.status = status;
    this.payload = payload;
  }
}

class SessionToken {
  private token = "";
  private refreshToken = "";
  private refreshPromise: Promise<RefreshTokenResponse> | null = null;
  // Note: userMode removed - using JWT-only permissions

  get value() {
    return this.token;
  }

  get refreshTokenValue() {
    return this.refreshToken;
  }

  set value(token: string) {
    if (typeof window === "undefined") {
      throw new Error("Can not set token in server side");
    }
    this.token = token;
  }

  set refreshTokenValue(token: string) {
    if (typeof window === "undefined") {
      throw new Error("Can not set refresh token in server side");
    }
    this.refreshToken = token;
  }

  clearTokens() {
    this.token = "";
    this.refreshToken = "";
    this.refreshPromise = null;
  }

  setRefreshPromise(promise: Promise<RefreshTokenResponse> | null) {
    this.refreshPromise = promise;
  }

  getRefreshPromise() {
    return this.refreshPromise;
  }
}

class UserInfo {
  public userInfo: any = null;

  get value() {
    return this.userInfo;
  }

  set value(user: any) {
    if (typeof window === "undefined") {
      throw new Error("Can not set user in server side");
    }
    this.userInfo = user;
  }

  // Clear user info
  clear() {
    this.userInfo = null;
  }

  // Check if user is authenticated
  get isAuthenticated() {
    return (
      this.userInfo !== null &&
      this.userInfo?.user?.id &&
      this.userInfo?.user?.email &&
      clientSessionToken.value
    );
  }
}

// Note: UserModeType and UserMode removed - using JWT-only permissions

export const clientSessionToken = new SessionToken();
export const clientUserInfo = new UserInfo();
// Note: clientUserMode removed - using JWT-only permissions

// Utility function to clear all authentication data
export const clearAuthData = () => {
  clientSessionToken.clearTokens();
  clientUserInfo.clear();
  // Note: userMode clearing removed - using JWT-only permissions
};

// Utility function to check if user is authenticated
export const isUserAuthenticated = () => {
  return clientUserInfo.isAuthenticated;
};

const redirectToLogin = async () => {
  if (typeof window !== "undefined") {
    // Store the current URL to redirect back after login
    const currentPath = window.location.pathname + window.location.search;
    if (currentPath !== "/login") {
      // Clear all authentication data
      clearAuthData();
      await authApiRequests.logout({});
      sessionStorage.setItem("redirectAfterLogin", currentPath);
      window.location.href = "/login";
    }
  }
};

const refreshAccessToken = async (): Promise<RefreshTokenResponse> => {
  const authToken = clientSessionToken.refreshTokenValue;
  const response = await fetch(`${API_URL}/api/v1/auth/refresh`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${authToken}`,
    },
  });

  if (!response.ok) {
    clearAuthData();
    redirectToLogin();
    // throw new HttpError(response.status, await response.json());
  }

  const data = await response.json();
  return data;
};

const handleResponse = async <T>(
  response: Response
): Promise<{ status: number; payload: T | null }> => {
  let payload: T | null = null;
  if (response.headers.get("Content-Type")?.includes("application/json")) {
    payload = await response.json();
  }
  if (!response.ok) {
    console.log('response error: ', response);
    throw new HttpError(
      response.status,
      payload as HttpErrorPayload,
      (payload as HttpErrorPayload)?.message || response.statusText
    );
  }
  return { status: response.status, payload };
};

const request = async <Response>(
  method: string,
  url: string,
  options: RequestInit & { token?: string; baseUrl?: string } = {}
): Promise<{ status: number; payload: Response | null }> => {
  const authToken = options?.token || clientSessionToken.value;
  const baseUrl = options.baseUrl ?? API_URL;
  const fullUrl = url.startsWith("/") ? baseUrl + url : `${baseUrl}/${url}`;

  const response = await fetch(fullUrl, {
    ...options,
    method,
    headers: {
      "Content-Type": "application/json",
      ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
      ...options.headers,
    },
    body: options.body ? JSON.stringify(options.body) : undefined,
  });

  return handleResponse<Response>(response);
};

// fileUploadService.ts

const executeUpload = async (
  file: File,
  uploadUrl: string,
  options?: UploadOptions,
  retryWithNewToken = true
): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append("file", file);

  const controller = new AbortController();
  const authToken = options?.token || clientSessionToken.value;
  const baseUrl = options?.baseUrl === undefined ? API_URL : options.baseUrl;
  const fullUrl = uploadUrl.startsWith("/")
    ? baseUrl + uploadUrl
    : baseUrl + "/" + uploadUrl;

  try {
    const response = await fetch(fullUrl, {
      method: "POST",
      body: formData,
      headers: {
        ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
        ...(options?.headers || {}),
      },
      signal: controller.signal,
      // onUploadProgress: (progressEvent: any) => {
      //   if (options?.onProgress && progressEvent.total) {
      //     const progress = (progressEvent.loaded * 100) / progressEvent.total;
      //     options.onProgress(Math.round(progress));
      //   }
      // },
    });

    // Handle token expiration
    if (
      response.status === 401 &&
      retryWithNewToken &&
      !options?.skipRefreshToken
    ) {
      let refreshResponse: RefreshTokenResponse;

      try {
        // Check if refresh token request is already in progress
        const existingRefreshPromise = clientSessionToken.getRefreshPromise();
        if (existingRefreshPromise) {
          refreshResponse = await existingRefreshPromise;
        } else {
          const newRefreshPromise = refreshAccessToken();
          clientSessionToken.setRefreshPromise(newRefreshPromise);

          try {
            refreshResponse = await newRefreshPromise;
          } finally {
            clientSessionToken.setRefreshPromise(null);
          }
        }

        // Update tokens
        clientSessionToken.value = refreshResponse.token;
        clientSessionToken.refreshTokenValue = refreshResponse.refreshToken;

        // Retry the upload with new token
        return executeUpload(file, uploadUrl, options, false);
      } catch (error) {
        // If refresh token fails, redirect to login
        redirectToLogin();
        throw error;
      }
    }

    if (!response.ok) {
      if (response.status === 401) {
        clearAuthData();
        redirectToLogin();
      }
      throw new Error(`Upload failed with status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`File upload failed: ${error.message}`);
    }
    throw error;
  }
};

export const uploadFile = async (
  file: File,
  uploadUrl: string,
  options?: UploadOptions
): Promise<UploadResponse> => {
  return executeUpload(file, uploadUrl, options);
};

// Helper function để xử lý nhiều files
export const uploadMultipleFiles = async (
  files: File[],
  uploadUrl: string,
  options?: UploadOptions
): Promise<UploadResponse[]> => {
  const uploadPromises = files.map((file) =>
    uploadFile(file, uploadUrl, options)
  );
  return Promise.all(uploadPromises);
};

const http = {
  get: <Response>(
    url: string,
    options?: Omit<RequestInit, "body"> & {
      page?: number;
      limit?: number;
      filters?: object;
      baseUrl?: string;
    }
  ) => {
    const { page, limit, filters } = options || {};
    if (page && limit) url += `?page=${page}&limit=${limit}`;
    const queryFilters = omitBy(
      filters,
      (value) => isNil(value) || isEmpty(value)
    );
    if (!isEmpty(queryFilters)) {
      const searchParams = new URLSearchParams({
        filters: JSON.stringify(queryFilters),
      });
      url += url.includes("?") ? "&" : "?";
      url += searchParams.toString();
    }
    return request<Response>("GET", url, options);
  },
  post: <Response>(
    url: string,
    body: any,
    options?: Omit<RequestInit, "body"> & { baseUrl?: string }
  ) => request<Response>("POST", url, { ...options, body }),
  put: <Response>(
    url: string,
    body: any,
    options?: Omit<RequestInit, "body"> & { baseUrl?: string }
  ) => request<Response>("PUT", url, { ...options, body }),
  patch: <Response>(
    url: string,
    body: any,
    options?: Omit<RequestInit, "body"> & { baseUrl?: string }
  ) => request<Response>("PATCH", url, { ...options, body }),
  delete: <Response>(
    url: string,
    body?: any,
    options?: Omit<RequestInit, "body"> & { baseUrl?: string }
  ) => request<Response>("DELETE", url, { ...options, body }),
};

export default http;
