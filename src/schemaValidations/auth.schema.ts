import z from "zod";

export const RegisterBody = z
  .object({
    firstName: z.string().trim().min(2).max(256),
    lastName: z.string().trim().min(2).max(256),
    email: z.string().trim().email(),
    // password: z.string().trim().min(8).max(100),
    // confirmPassword: z.string().trim().min(8).max(100),
  })
  .strict()
  // .superRefine(({ password, confirmPassword }, ctx) => {
  //   if (password !== confirmPassword) {
  //     ctx.addIssue({
  //       code: "custom",
  //       message: "Passwords do not match",
  //       path: ["confirmPassword"],
  //     });
  //   }
  // });

export type RegisterBodyType = z.TypeOf<typeof RegisterBody>;

export const LoginRes = z.object({
  token: z.string(),
  refreshToken: z.string(),
  tokenExpires: z.number(),
  user: z.object({
    id: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string(),
    provider: z.string(),
    // viewMode: z.string(),
    orgId: z.string(),
    org: z.object({
      domain: z.string(),
      orgName: z.string(),
      id: z.string(),
      createdAt: z.string(),
      updatedAt: z.string(),
    }),
    roles: z.array(
      z.object({
        permissionIds: z.array(z.string()),
        permissions: z.array(z.object({})),
        orgId: z.string(),
        id: z.string(),
        name: z.string(),
        createdAt: z.string(),
        updatedAt: z.string(),
      })
    ),
    status: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
  }),
});

export const RegisterRes = z.object({});

export type RegisterResType = z.TypeOf<typeof RegisterRes>;

export const LoginBody = z
  .object({
    email: z.string().trim().email(),
    password: z.string().trim().min(8).max(100),
  })
  .strict();

export type LoginBodyType = z.TypeOf<typeof LoginBody>;

export type LoginResType = z.TypeOf<typeof LoginRes>;

export const authBody = z.object({
  sessionToken: z.string(),
  refreshToken: z.string(),
  tokenExpires: z.number(),
  user: z.object({
    id: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string(),
    provider: z.string(),
    // viewMode: z.string(),
    orgId: z.string(),
    org: z.object({
      domain: z.string(),
      orgName: z.string(),
      id: z.string(),
      createdAt: z.string(),
      updatedAt: z.string(),
    }),
    roles: z.array(
      z.object({
        permissionIds: z.array(z.string()),
        permissions: z.array(z.object({})),
        orgId: z.string(),
        id: z.string(),
        name: z.string(),
        createdAt: z.string(),
        updatedAt: z.string(),
      })
    ),
    status: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
  }),
});

export type AuthBodyType = z.TypeOf<typeof authBody>;

const ForgotPasswordBodyType = z.object({
  email: z.string().trim().email(),
});

export type ForgotPasswordBodyType = z.TypeOf<typeof ForgotPasswordBodyType>;
