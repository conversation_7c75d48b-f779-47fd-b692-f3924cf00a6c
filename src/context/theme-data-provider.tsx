"use client";

import { redirect } from "next/navigation";
import { clientSessionToken, clientUserInfo } from "@/lib/http";
import setGlobalColorTheme from "@/lib/theme-colors";
import { useTheme } from "next-themes";
import type { ThemeColors, ThemeColorStateParams } from "@/types/theme-types";

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";

// interface AuthContextType {
//   sessionToken: string | null;
//   user: User | null;
//   setSessionToken: React.Dispatch<React.SetStateAction<string | null>>;
//   setUser: React.Dispatch<React.SetStateAction<User | null>>;
//   login: () => void;
//   logout: () => void;
// }

const ThemeContext = createContext<ThemeColorStateParams>(
  {} as ThemeColorStateParams
);

// Tạo AuthContext
// const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface ProviderProps {
  children: ReactNode;
  initialSessionToken: string;
  initialRefreshToken: string;
  initialUserInfo: string;
}

export const ProviderContext: React.FC<ProviderProps> = ({
  children,
  initialSessionToken,
  initialRefreshToken,
  initialUserInfo,
}) => {
  useState(() => {
    if (typeof window !== "undefined") {
      clientSessionToken.value = initialSessionToken;
      clientSessionToken.refreshTokenValue = initialRefreshToken;
      if (initialUserInfo) clientUserInfo.value = JSON.parse(initialUserInfo);
    }
  });
  const getSavedThemeColor = () => {
    try {
      return (localStorage.getItem("themeColor") as ThemeColors) || "OceanBlue";
    } catch (error) {
      return "OceanBlue" as ThemeColors;
    }
  };

  const [themeColor, setThemeColor] = useState<ThemeColors>(
    getSavedThemeColor() as ThemeColors
  );

  const [isMounted, setIsMounted] = useState(false);
  const { theme } = useTheme();
  useEffect(() => {
    localStorage.setItem("themeColor", themeColor);
    const systemPreferenceIsDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;
    const newTheme =
      theme === "system" ? (systemPreferenceIsDark ? "dark" : "light") : theme;

    setGlobalColorTheme(newTheme as "light" | "dark", themeColor);

    if (!isMounted) {
      setIsMounted(true);
    }
  }, [themeColor, theme]);

  if (!isMounted) {
    return null;
  }

  return (
    <ThemeContext.Provider
      value={{
        themeColor,
        setThemeColor,
      }}
    >
      <>{children}</>
    </ThemeContext.Provider>
  );
};

export function useThemeContext() {
  return useContext(ThemeContext);
}
// export const useAuth = (): AuthContextType => {
//   const context = useContext(AuthContext);
//   if (!context) {
//     throw new Error("useAuth must be used within a ThemeProvider");
//   }
//   return context;
// };
