"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface IframeDialogConfig {
  title: string;
  iframeSrc: string;
  className?: string;
}

interface IframeDialogContextType {
  isOpen: boolean;
  config: IframeDialogConfig | null;
  openIframeDialog: (config: IframeDialogConfig) => void;
  closeIframeDialog: () => void;
  toggleIframeDialog: (config?: IframeDialogConfig) => void;
}

const IframeDialogContext = createContext<IframeDialogContextType | null>(null);

interface IframeDialogProviderProps {
  children: ReactNode;
}

export const IframeDialogProvider: React.FC<IframeDialogProviderProps> = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<IframeDialogConfig | null>(null);

  const openIframeDialog = (newConfig: IframeDialogConfig) => {
    setConfig(newConfig);
    setIsOpen(true);
  };

  const closeIframeDialog = () => {
    setIsOpen(false);
    // Delay clearing config to allow for smooth closing animation
    setTimeout(() => setConfig(null), 300);
  };

  const toggleIframeDialog = (newConfig?: IframeDialogConfig) => {
    if (isOpen) {
      closeIframeDialog();
    } else if (newConfig) {
      openIframeDialog(newConfig);
    } else if (config) {
      setIsOpen(true);
    }
  };

  const contextValue: IframeDialogContextType = {
    isOpen,
    config,
    openIframeDialog,
    closeIframeDialog,
    toggleIframeDialog,
  };

  return (
    <IframeDialogContext.Provider value={contextValue}>
      {children}
    </IframeDialogContext.Provider>
  );
};

export const useIframeDialog = (): IframeDialogContextType => {
  const context = useContext(IframeDialogContext);
  if (!context) {
    throw new Error('useIframeDialog must be used within an IframeDialogProvider');
  }
  return context;
};

// Predefined configurations for common use cases
export const IFRAME_CONFIGS = {
  GRAFANA_DASHBOARD: {
    title: "Analytics Dashboard",
    iframeSrc: "https://grafana.homicen.com/public-dashboards/7156cf0bf0dd41e5af5bd195b3334efa",
    className: "analytics-dashboard"
  },
  DEVICE_MONITORING: {
    title: "Device Monitoring",
    iframeSrc: "https://grafana.homicen.com/public-dashboards/7156cf0bf0dd41e5af5bd195b3334efa",
    className: "device-monitoring"
  },
  SYSTEM_LOGS: {
    title: "System Logs",
    iframeSrc: "https://grafana.homicen.com/public-dashboards/7156cf0bf0dd41e5af5bd195b3334efa",
    className: "system-logs"
  },
  PERFORMANCE_METRICS: {
    title: "Performance Metrics",
    iframeSrc: "https://grafana.homicen.com/public-dashboards/7156cf0bf0dd41e5af5bd195b3334efa",
    className: "performance-metrics"
  },
} as const;
