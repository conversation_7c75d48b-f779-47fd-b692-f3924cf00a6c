"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getJWTUserOrgIds, checkJWTPermission, RESOURCES } from '@/lib/jwt-permissions';
import { clientSessionToken, clientUserInfo } from '@/lib/http';
import { decodeJWT } from '@/lib/jwt';
import organizationsApiRequests from '@/apiRequests/admin/organization';
import { Organization } from '@/app/(DashboardLayout)/organizations/types';

// API Response type (what the API actually returns)
interface ApiOrganization {
  abbreviation: string;
  domain: string;
  orgName: string;
  id: string;
  createdAt: string;
  updatedAt: string;
}
interface OrganizationContextType {
  currentOrgId: string | null;
  availableOrganizations: Organization[];
  switchOrganization: (orgId: string) => void;
  isLoading: boolean;
  hasMultipleOrgs: boolean;
  canAccessAllOrgs: boolean;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: React.ReactNode }) {
  const [currentOrgId, setCurrentOrgId] = useState<string | null>(null);
  const [availableOrganizations, setAvailableOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [canAccessAllOrgs, setCanAccessAllOrgs] = useState(false);

  // Initialize organization data from JWT token
  useEffect(() => {
    const initializeOrganizations = async () => {
      try {
        const token = clientSessionToken.value;
        if (!token) {
          setIsLoading(false);
          return;
        }

        const payload = decodeJWT(token);
        if (!payload || !payload.roles) {
          setIsLoading(false);
          return;
        }

        // Check if user has admin permissions (can access all organizations)
        const hasAdminAccess = checkJWTPermission(RESOURCES.ALL) || 
                              checkJWTPermission(RESOURCES.ORGANIZATION, 'get');
        
        setCanAccessAllOrgs(hasAdminAccess);

        let organizations: Organization[] = [];

        if (hasAdminAccess) {
          // Fetch all organizations from API for admin users
          try {
            const response = await organizationsApiRequests.getOrganizations(1, 1000); // Get all orgs
            if (response.status === 200 && response.payload?.docs) {
              organizations = (response.payload.docs as ApiOrganization[]).map(org => ({
                id: org.id,
                abbreviation: org.abbreviation,
                domain: org.domain,
                orgName: org.orgName,
                createdAt: org.createdAt,
                updatedAt: org.updatedAt,
              }));
            }
          } catch (error) {
            console.error('Error fetching all organizations:', error);
            // Fallback to JWT-based organizations on API error
            hasAdminAccess && console.log('Falling back to JWT-based organizations');
          }
        }

        // If not admin or API failed, use JWT-based organizations
        if (!hasAdminAccess || organizations.length === 0) {
          const orgIds = getJWTUserOrgIds();
          organizations = orgIds.map(orgId => {
            // Try to get organization name from user info or other sources
            const role = payload.roles.find(r => r.orgId === orgId);
            
            // Try to get name from user info if available
            let orgName = `Organization ${orgId.slice(-8)}`; // Fallback name
            
            // Check if userInfo has organization details
            const userInfoValue = clientUserInfo.value;
            const userOrgData = userInfoValue?.user?.org;
            if (userOrgData?.orgName && userOrgData.id === orgId) {
              orgName = userOrgData.orgName;
            }
            
            return {
              id: orgId,
              abbreviation: userOrgData?.abbreviation || '',
              domain: userOrgData?.domain || '',
              orgName: userOrgData?.orgName || orgName,
              createdAt: userOrgData?.createdAt || new Date().toISOString(),
              updatedAt: userOrgData?.updatedAt || new Date().toISOString(),
            };
          });
        }

        setAvailableOrganizations(organizations);

        // Set default organization (first available or from stored preference)
        const storedOrgId = localStorage.getItem('currentOrgId');
        const orgIds = organizations.map(org => org.id);
        const defaultOrgId = storedOrgId && orgIds.includes(storedOrgId) 
          ? storedOrgId 
          : orgIds[0] || null;

        setCurrentOrgId(defaultOrgId);
        
        if (defaultOrgId) {
          localStorage.setItem('currentOrgId', defaultOrgId);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing organizations:', error);
        setIsLoading(false);
      }
    };

    // Only initialize once when component mounts or token changes
    if (clientSessionToken.value) {
      initializeOrganizations();
    }
  }, [clientSessionToken.value]); // Removed clientUserInfo.value dependency to prevent redundant calls

  const switchOrganization = (orgId: string) => {
    if (availableOrganizations.some(org => org.id === orgId)) {
      setCurrentOrgId(orgId);
      localStorage.setItem('currentOrgId', orgId);
      // Components will react to currentOrgId change via useEffect dependencies
    }
  };

  const hasMultipleOrgs = availableOrganizations.length > 1;

  const value: OrganizationContextType = {
    currentOrgId,
    availableOrganizations,
    switchOrganization,
    isLoading,
    hasMultipleOrgs,
    canAccessAllOrgs,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
}

// Hook to get current organization ID (for use in API calls)
export function useCurrentOrgId(): string | null {
  const { currentOrgId } = useOrganization();
  return currentOrgId;
}
