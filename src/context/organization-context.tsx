"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getJWTUserOrgIds } from '@/lib/jwt-permissions';
import { clientSessionToken, clientUserInfo } from '@/lib/http';
import { decodeJWT } from '@/lib/jwt';

export interface Organization {
  orgId: string;
  name?: string;
  // Add other org properties as needed
}

interface OrganizationContextType {
  currentOrgId: string | null;
  availableOrganizations: Organization[];
  switchOrganization: (orgId: string) => void;
  isLoading: boolean;
  hasMultipleOrgs: boolean;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: React.ReactNode }) {
  const [currentOrgId, setCurrentOrgId] = useState<string | null>(null);
  const [availableOrganizations, setAvailableOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize organization data from JWT token
  useEffect(() => {
    const initializeOrganizations = () => {
      try {
        const token = clientSessionToken.value;
        if (!token) {
          setIsLoading(false);
          return;
        }

        const payload = decodeJWT(token);
        if (!payload || !payload.roles) {
          setIsLoading(false);
          return;
        }

        // Extract unique organizations from JWT roles
        const orgIds = getJWTUserOrgIds();
        const organizations: Organization[] = orgIds.map(orgId => {
          // Try to get organization name from user info or other sources
          const role = payload.roles.find(r => r.orgId === orgId);
          return {
            orgId,
            name: `Organization ${orgId.slice(-8)}`, // Fallback name using last 8 chars
          };
        });

        setAvailableOrganizations(organizations);

        // Set default organization (first available or from stored preference)
        const storedOrgId = localStorage.getItem('currentOrgId');
        const defaultOrgId = storedOrgId && orgIds.includes(storedOrgId) 
          ? storedOrgId 
          : orgIds[0] || null;

        setCurrentOrgId(defaultOrgId);
        
        if (defaultOrgId) {
          localStorage.setItem('currentOrgId', defaultOrgId);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing organizations:', error);
        setIsLoading(false);
      }
    };

    initializeOrganizations();

    // Re-initialize when user info or token changes
    const userInfo = clientUserInfo.value;
    if (userInfo) {
      initializeOrganizations();
    }
  }, [clientSessionToken.value, clientUserInfo.value]);

  const switchOrganization = (orgId: string) => {
    if (availableOrganizations.some(org => org.orgId === orgId)) {
      setCurrentOrgId(orgId);
      localStorage.setItem('currentOrgId', orgId);
    }
  };

  const hasMultipleOrgs = availableOrganizations.length > 1;

  const value: OrganizationContextType = {
    currentOrgId,
    availableOrganizations,
    switchOrganization,
    isLoading,
    hasMultipleOrgs,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
}

// Hook to get current organization ID (for use in API calls)
export function useCurrentOrgId(): string | null {
  const { currentOrgId } = useOrganization();
  return currentOrgId;
}
