/**
 * JWT-based Permission Status Component
 * Shows current user permissions from <PERSON>W<PERSON> token
 */

"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Shield, 
  Crown, 
  User, 
  Eye, 
  EyeOff, 
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/hooks/useAuth';
import { getJWTUserPermissions, getJWTUserOrgIds, isJWTAdmin } from '@/lib/jwt-permissions';

export interface PermissionStatusProps {
  showDetails?: boolean;
  compact?: boolean;
  className?: string;
}

export function PermissionStatus({ 
  showDetails = false, 
  compact = false,
  className = ""
}: PermissionStatusProps) {
  const { user, isAuthenticated } = useAuth();
  const { 
    userPermissions,
    isAdmin,
    userOrgIds,
    isLoading
  } = usePermissions();

  const [showDetailedView, setShowDetailedView] = React.useState(showDetails);

  if (!isAuthenticated) {
    return (
      <Card className={`border-red-200 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">Not authenticated</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span className="text-sm">Loading permissions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate permission statistics from JWT token
  const allPermissions = Object.entries(userPermissions).flatMap(([resource, actions]) =>
    actions.map(action => ({ resource, action }))
  );

  const hasWildcardPermission = userPermissions['*']?.includes('*') || false;
  const uniqueResources = Object.keys(userPermissions).length;
  const totalActions = allPermissions.length;
  const isUserAdmin = isAdmin();

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge variant={hasWildcardPermission ? "default" : "secondary"} className="text-xs">
          {hasWildcardPermission && <Crown className="h-3 w-3 mr-1" />}
          {totalActions} permissions
        </Badge>
        {isUserAdmin && (
          <Badge variant="default" className="text-xs">
            <Shield className="h-3 w-3 mr-1" />
            Admin
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Shield className="h-4 w-4" />
            Permission Status
            {hasWildcardPermission && (
              <Crown className="h-4 w-4 text-yellow-500" />
            )}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetailedView(!showDetailedView)}
          >
            {showDetailedView ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        </div>
        <CardDescription className="text-xs">
          Current user permissions from JWT token
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Permission Summary */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-3 w-3 text-green-500" />
            <span>{totalActions} total permissions</span>
          </div>
          <div className="flex items-center gap-2">
            <User className="h-3 w-3 text-blue-500" />
            <span>{uniqueResources} resources</span>
          </div>
        </div>

        {/* Admin Status */}
        {isUserAdmin && (
          <div className="p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <Crown className="h-3 w-3" />
              <span className="text-xs font-medium">Administrator Access</span>
            </div>
          </div>
        )}

        {/* Detailed View */}
        {showDetailedView && (
          <div className="space-y-3 pt-2 border-t">
            {/* User Info */}
            <div>
              <h4 className="text-xs font-medium mb-1">User Information</h4>
              <div className="text-xs text-muted-foreground space-y-1">
                <p>ID: {user?.userId || 'N/A'}</p>
                <p>Organizations: {userOrgIds.length}</p>
                {userOrgIds.length > 0 && (
                  <p>Org IDs: {userOrgIds.join(', ')}</p>
                )}
              </div>
            </div>

            {/* Permission Breakdown */}
            <div>
              <h4 className="text-xs font-medium mb-1">Permission Breakdown</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className="text-muted-foreground">Resources:</span>
                  <span className="ml-1 font-medium">{uniqueResources}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">Actions:</span>
                  <span className="ml-1 font-medium">{totalActions}</span>
                </div>
              </div>
            </div>

            {/* Resource Permissions */}
            {Object.keys(userPermissions).length > 0 && (
              <div>
                <h4 className="text-xs font-medium mb-1">Resource Permissions</h4>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {Object.entries(userPermissions).map(([resource, actions]) => (
                    <div key={resource} className="flex items-center justify-between text-xs">
                      <span className="font-medium">
                        {resource === '*' ? 'All Resources' : resource}:
                      </span>
                      <div className="flex flex-wrap gap-1">
                        {actions.slice(0, 3).map(action => (
                          <Badge 
                            key={action} 
                            variant="outline" 
                            className="text-xs px-1 py-0"
                          >
                            {action === '*' ? 'All' : action}
                          </Badge>
                        ))}
                        {actions.length > 3 && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            +{actions.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Permission Summary */}
            {totalActions > 0 && (
              <div>
                <h4 className="text-xs font-medium mb-1">Quick Summary</h4>
                <div className="flex flex-wrap gap-1">
                  {allPermissions.slice(0, 6).map((permission, index) => (
                    <Badge 
                      key={`${permission.resource}-${permission.action}-${index}`}
                      variant="outline" 
                      className="text-xs px-1 py-0"
                    >
                      {permission.resource === '*' ? 'All' : permission.resource}:
                      {permission.action === '*' ? 'All' : permission.action}
                    </Badge>
                  ))}
                  {allPermissions.length > 6 && (
                    <Badge variant="secondary" className="text-xs px-1 py-0">
                      +{allPermissions.length - 6} more
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Compact permission indicator for headers/toolbars
 */
export function PermissionIndicator({ className = "" }: { className?: string }) {
  return (
    <PermissionStatus 
      compact={true} 
      className={className}
    />
  );
}

/**
 * Detailed permission panel for dashboards
 */
export function PermissionPanel({ className = "" }: { className?: string }) {
  return (
    <PermissionStatus 
      showDetails={true} 
      className={className}
    />
  );
}

export default PermissionStatus;