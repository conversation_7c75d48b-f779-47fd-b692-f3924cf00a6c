/**
 * Component for managing user-role assignments using UserRolesMappings API
 */

import React, { useState, useEffect } from 'react';
import { User } from "@/app/(DashboardLayout)/users/types";
import { Role } from "@/app/(DashboardLayout)/roles/types";
import { UserRoleMapping } from "@/types/mappings";
import userRolesMappingsApiRequests from "@/apiRequests/admin/userRolesMappings";
import rolesApiRequests from "@/apiRequests/admin/roles";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Plus, Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface UserRoleManagerProps {
  user: User;
  onUserRolesChanged?: (userRoleMappings: UserRoleMapping[]) => void;
  disabled?: boolean;
  showTitle?: boolean;
}

export const UserRoleManager: React.FC<UserRoleManagerProps> = ({
  user,
  onUserRolesChanged,
  disabled = false,
  showTitle = true,
}) => {
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [userRoleMappings, setUserRoleMappings] = useState<UserRoleMapping[]>([]);
  const [selectedRoleId, setSelectedRoleId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const { toast } = useToast();

  // Load available roles and current user role mappings
  useEffect(() => {
    const loadData = async () => {
      if (!user.id) return;

      try {
        setInitialLoading(true);

        // Load available roles
        const rolesResponse = await rolesApiRequests.getRoles(1, 100);
        if (rolesResponse.status === 200 && rolesResponse.payload) {
          setAvailableRoles(rolesResponse.payload.docs || []);
        }

        // Load current user role mappings
        const mappingsResponse = await userRolesMappingsApiRequests.getUserRolesMappingsByUserId(
          user.id,
          1,
          100,
          { isActive: true }
        );
        if (mappingsResponse.status === 200 && mappingsResponse.payload) {
          setUserRoleMappings(mappingsResponse.payload.docs || []);
        }
      } catch (error) {
        console.error('Error loading user role data:', error);
        toast({
          title: "Lỗi",
          description: "Không thể tải dữ liệu vai trò người dùng",
          variant: "destructive",
        });
      } finally {
        setInitialLoading(false);
      }
    };

    loadData();
  }, [user.id, toast]);

  // Get currently assigned role IDs
  const assignedRoleIds = userRoleMappings
    .filter(mapping => mapping.isActive !== false)
    .map(mapping => mapping.roleId);

  // Get available roles that are not yet assigned
  const unassignedRoles = availableRoles.filter(
    role => !assignedRoleIds.includes(role.id)
  );

  // Assign a role to the user
  const handleAssignRole = async (roleId: string) => {
    if (!user.id || !roleId) return;

    try {
      setLoading(true);

      // const response = await userRolesMappingsApiRequests.createUserRoleMapping({
      //   userId: user.id,
      //   roleId,
      //   isActive: true,
      // });

      // if (response.status === 200 || response.status === 201) {
      //   const newMapping = response.payload;
      //   const updatedMappings = [...userRoleMappings, newMapping];
      //   setUserRoleMappings(updatedMappings);
      //   setSelectedRoleId('');

      //   // Notify parent component
      //   if (onUserRolesChanged) {
      //     onUserRolesChanged(updatedMappings);
      //   }

      //   toast({
      //     title: "Thành công",
      //     description: "Đã gán vai trò thành công",
      //   });
      // }
    } catch (error) {
      console.error('Error assigning role:', error);
      toast({
        title: "Lỗi",
        description: "Không thể gán vai trò",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Remove a role from the user
  const handleRemoveRole = async (mappingId: string) => {
    try {
      setLoading(true);

      const response = await userRolesMappingsApiRequests.deleteUserRoleMapping(mappingId);

      if (response.status === 200 || response.status === 204) {
        const updatedMappings = userRoleMappings.filter(
          mapping => mapping.id !== mappingId
        );
        setUserRoleMappings(updatedMappings);

        // Notify parent component
        if (onUserRolesChanged) {
          onUserRolesChanged(updatedMappings);
        }

        toast({
          title: "Thành công",
          description: "Đã xóa vai trò thành công",
        });
      }
    } catch (error) {
      console.error('Error removing role:', error);
      toast({
        title: "Lỗi",
        description: "Không thể xóa vai trò",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Đang tải...</span>
      </div>
    );
  }

  const content = (
    <div className="space-y-4">
      {/* Current Roles */}
      <div>
        <h4 className="text-sm font-medium mb-2">Vai trò đã gán</h4>
        <div className="flex flex-wrap gap-2">
          {userRoleMappings
            .filter(mapping => mapping.isActive !== false)
            .map((mapping) => {
              const role = availableRoles.find(r => r.id === mapping.roleId);
              return (
                <Badge
                  key={mapping.id}
                  variant="secondary"
                  className="flex items-center gap-1 bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400"
                >
                  {role?.name || mapping.roleId}
                  {!disabled && (
                    <button
                      onClick={() => handleRemoveRole(mapping.id)}
                      disabled={loading}
                      className="ml-1 hover:bg-purple-200 dark:hover:bg-purple-800 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  )}
                </Badge>
              );
            })}
          {assignedRoleIds.length === 0 && (
            <span className="text-sm text-muted-foreground">Chưa có vai trò nào được gán</span>
          )}
        </div>
      </div>

      {/* Add New Role */}
      {!disabled && unassignedRoles.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Gán vai trò mới</h4>
          <div className="flex gap-2">
            <Select
              value={selectedRoleId}
              onValueChange={setSelectedRoleId}
              disabled={loading}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Chọn vai trò để gán" />
              </SelectTrigger>
              <SelectContent>
                {unassignedRoles.map((role) => (
                  <SelectItem key={role.id} value={role.id}>
                    {role.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              onClick={() => handleAssignRole(selectedRoleId)}
              disabled={!selectedRoleId || loading}
              size="sm"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      )}

      {!disabled && unassignedRoles.length === 0 && assignedRoleIds.length > 0 && (
        <div className="text-sm text-muted-foreground">
          Tất cả vai trò có sẵn đã được gán
        </div>
      )}
    </div>
  );

  if (showTitle) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Vai trò người dùng</CardTitle>
        </CardHeader>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }

  return content;
};

export default UserRoleManager;
