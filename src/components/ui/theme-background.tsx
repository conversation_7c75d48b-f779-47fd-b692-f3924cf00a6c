"use client";

import { useThemeContext } from "@/context/theme-data-provider";
import { cn } from "@/lib/utils";

interface ThemeBackgroundProps {
  children: React.ReactNode;
  className?: string;
  variant?: "subtle" | "vibrant" | "minimal";
}

const themeGradients = {
  OceanBlue: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  EmeraldTech: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  BluePurple: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  CosmicPurple: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  SunsetGlow: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Zinc: {
    subtle: "bg-gradient-to-br from-muted/50 via-accent/30 to-muted/70",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/30 to-accent/20",
  },
  Rose: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Sky: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Green: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Amber: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Yellow: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Cyan: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Violet: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
  Red: {
    subtle: "bg-gradient-to-br from-primary/5 via-accent/3 to-primary/8",
    vibrant: "bg-gradient-to-br from-primary/10 via-accent/5 to-primary/15",
    minimal: "bg-gradient-to-br from-muted/50 to-accent/30",
  },
};

export function ThemeBackground({ 
  children, 
  className, 
  variant = "subtle" 
}: ThemeBackgroundProps) {
  const { themeColor } = useThemeContext();
  
  const gradientClass = themeGradients[themeColor as keyof typeof themeGradients]?.[variant] || themeGradients.OceanBlue[variant];
  
  return (
    <div className={cn(gradientClass, className)}>
      {children}
    </div>
  );
}

export default ThemeBackground;
