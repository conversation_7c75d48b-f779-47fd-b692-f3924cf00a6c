"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  variant?: "default" | "page" | "card" | "form";
  padding?: "none" | "sm" | "md" | "lg";
}

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  variant = "default",
  padding = "md",
}) => {
  const baseClasses = "w-full";
  
  const variantClasses = {
    default: "container mx-auto",
    page: "min-h-screen",
    card: "max-w-4xl mx-auto",
    form: "max-w-2xl mx-auto",
  };

  const paddingClasses = {
    none: "",
    sm: "p-2 sm:p-4",
    md: "p-4 md:p-6 lg:p-8",
    lg: "p-6 md:p-8 lg:p-12",
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </div>
  );
};

interface ResponsiveHeaderProps {
  title: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
}

const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({
  title,
  description,
  children,
  className,
}) => {
  return (
    <div className={cn("flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center mb-6", className)}>
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold text-foreground">{title}</h1>
        {description && (
          <p className="text-muted-foreground mt-1 text-sm sm:text-base">{description}</p>
        )}
      </div>
      {children && (
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {children}
        </div>
      )}
    </div>
  );
};

interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: "sm" | "md" | "lg";
  className?: string;
}

const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { default: 1, sm: 2, lg: 3, xl: 4 },
  gap = "md",
  className,
}) => {
  const gapClasses = {
    sm: "gap-2 sm:gap-3",
    md: "gap-4 md:gap-6",
    lg: "gap-6 md:gap-8",
  };

  const getGridCols = () => {
    const { default: def = 1, sm = def, md = sm, lg = md, xl = lg } = columns;
    return `grid-cols-${def} sm:grid-cols-${sm} md:grid-cols-${md} lg:grid-cols-${lg} xl:grid-cols-${xl}`;
  };

  return (
    <div className={cn("grid", getGridCols(), gapClasses[gap], className)}>
      {children}
    </div>
  );
};

interface ResponsiveStackProps {
  children: React.ReactNode;
  direction?: "vertical" | "horizontal" | "responsive";
  gap?: "sm" | "md" | "lg";
  align?: "start" | "center" | "end" | "stretch";
  justify?: "start" | "center" | "end" | "between" | "around";
  className?: string;
}

const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  direction = "responsive",
  gap = "md",
  align = "start",
  justify = "start",
  className,
}) => {
  const directionClasses = {
    vertical: "flex flex-col",
    horizontal: "flex flex-row",
    responsive: "flex flex-col sm:flex-row",
  };

  const gapClasses = {
    sm: "gap-2",
    md: "gap-4",
    lg: "gap-6",
  };

  const alignClasses = {
    start: "items-start",
    center: "items-center",
    end: "items-end",
    stretch: "items-stretch",
  };

  const justifyClasses = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    between: "justify-between",
    around: "justify-around",
  };

  return (
    <div
      className={cn(
        directionClasses[direction],
        gapClasses[gap],
        alignClasses[align],
        justifyClasses[justify],
        className
      )}
    >
      {children}
    </div>
  );
};

export { ResponsiveContainer, ResponsiveHeader, ResponsiveGrid, ResponsiveStack };
