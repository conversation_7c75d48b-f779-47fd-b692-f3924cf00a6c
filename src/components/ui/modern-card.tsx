import * as React from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cardGradients } from "@/lib/device-colors";
import { motion } from "framer-motion";

interface ModernCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "glass" | "gradient" | "glassMorphism";
  gradientType?: keyof typeof cardGradients;
  hover?: boolean;
  glow?: boolean;
}

const ModernCard = React.forwardRef<HTMLDivElement, ModernCardProps>(
  ({ className, variant = "default", gradientType, hover = true, glow = false, ...props }, ref) => {
    const getVariantClasses = () => {
      switch (variant) {
        case "glass":
          return cardGradients.glass;
        case "glassMorphism":
          return cardGradients.glassMorphism;
        case "gradient":
          return gradientType ? cardGradients[gradientType] : cardGradients.primary;
        default:
          return "";
      }
    };

    const hoverClasses = hover ? "hover:shadow-lg hover:-translate-y-1 transition-all duration-300" : "";
    const glowClasses = glow ? "shadow-lg shadow-primary/20 dark:shadow-primary/10" : "";

    return (
      <Card
        ref={ref}
        className={cn(
          "relative overflow-hidden border-0 shadow-sm",
          getVariantClasses(),
          hoverClasses,
          glowClasses,
          className
        )}
        {...props}
      />
    );
  }
);

ModernCard.displayName = "ModernCard";

interface DeviceCardProps {
  device: {
    id: string;
    name: string;
    type: string;
    status: string;
    location?: string;
    lastSeen?: string;
    batteryLevel?: number;
    signalStrength?: number;
    energyUsage?: number;
  };
  onClick?: () => void;
  className?: string;
}

const DeviceCard: React.FC<DeviceCardProps> = ({ device, onClick, className }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "online":
        return "bg-emerald-500";
      case "offline":
        return "bg-slate-500";
      case "warning":
        return "bg-amber-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-slate-500";
    }
  };

  const getDeviceIcon = (type: string) => {
    // You can replace these with actual icons
    switch (type.toLowerCase()) {
      case "light":
        return "💡";
      case "sensor":
        return "🌡️";
      case "camera":
        return "📷";
      case "lock":
        return "🔒";
      case "speaker":
        return "🔊";
      default:
        return "📱";
    }
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
    >
      <ModernCard
        variant="glassMorphism"
        className={cn("cursor-pointer group", className)}
        onClick={onClick}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="text-2xl">{getDeviceIcon(device.type)}</div>
              <div>
                <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors">
                  {device.name}
                </CardTitle>
                <CardDescription className="text-sm">
                  {device.type} • {device.location}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className={cn("w-2 h-2 rounded-full", getStatusColor(device.status))} />
              <Badge variant={device.status === "online" ? "default" : "secondary"} className="text-xs">
                {device.status}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {/* Device Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            {device.batteryLevel !== undefined && (
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Battery</span>
                <span className="font-medium">{device.batteryLevel}%</span>
              </div>
            )}
            
            {device.signalStrength !== undefined && (
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Signal</span>
                <span className="font-medium">{device.signalStrength}%</span>
              </div>
            )}
            
            {device.energyUsage !== undefined && (
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Energy</span>
                <span className="font-medium">{device.energyUsage}W</span>
              </div>
            )}
            
            {device.lastSeen && (
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Last Seen</span>
                <span className="font-medium text-xs">{device.lastSeen}</span>
              </div>
            )}
          </div>

          {/* Progress Bars */}
          {device.batteryLevel !== undefined && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>Battery Level</span>
                <span>{device.batteryLevel}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-1.5">
                <div
                  className={cn(
                    "h-1.5 rounded-full transition-all duration-300",
                    device.batteryLevel > 50 ? "bg-emerald-500" :
                    device.batteryLevel > 20 ? "bg-amber-500" : "bg-red-500"
                  )}
                  style={{ width: `${device.batteryLevel}%` }}
                />
              </div>
            </div>
          )}
        </CardContent>
      </ModernCard>
    </motion.div>
  );
};

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: "primary" | "success" | "warning" | "error" | "info";
  className?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = "primary",
  className
}) => {
  const colorMap = {
    primary: "text-blue-600 dark:text-blue-400",
    success: "text-emerald-600 dark:text-emerald-400",
    warning: "text-amber-600 dark:text-amber-400",
    error: "text-red-600 dark:text-red-400",
    info: "text-cyan-600 dark:text-cyan-400"
  };

  return (
    <ModernCard variant="glass" className={cn("", className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {subtitle && (
              <p className="text-xs text-muted-foreground">{subtitle}</p>
            )}
          </div>
          
          <div className="flex flex-col items-end gap-2">
            {icon && (
              <div className={cn("p-2 rounded-lg bg-muted/50", colorMap[color])}>
                {icon}
              </div>
            )}
            
            {trend && (
              <div className={cn(
                "flex items-center gap-1 text-xs font-medium",
                trend.isPositive ? "text-emerald-600" : "text-red-600"
              )}>
                <span>{trend.isPositive ? "↗" : "↘"}</span>
                <span>{Math.abs(trend.value)}%</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </ModernCard>
  );
};

interface ActionCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  variant?: "default" | "featured";
  className?: string;
}

const ActionCard: React.FC<ActionCardProps> = ({
  title,
  description,
  icon,
  actions,
  variant = "default",
  className
}) => {
  return (
    <ModernCard
      variant={variant === "featured" ? "gradient" : "glass"}
      gradientType={variant === "featured" ? "primary" : undefined}
      className={cn("group", className)}
    >
      <CardHeader>
        <div className="flex items-start gap-4">
          {icon && (
            <div className="p-3 rounded-lg bg-primary/10 text-primary">
              {icon}
            </div>
          )}
          <div className="flex-1">
            <CardTitle className="group-hover:text-primary transition-colors">
              {title}
            </CardTitle>
            <CardDescription className="mt-1">
              {description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      {actions && (
        <CardContent className="pt-0">
          <div className="flex gap-2">
            {actions}
          </div>
        </CardContent>
      )}
    </ModernCard>
  );
};

export { ModernCard, DeviceCard, StatsCard, ActionCard };
