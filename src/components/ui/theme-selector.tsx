"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useThemeContext } from "@/context/theme-data-provider";
import type { ThemeColors } from "@/types/theme-types";

const themeOptions: Array<{
  name: ThemeColors;
  label: string;
  description: string;
  emoji: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
}> = [
  {
    name: "OceanBlue",
    label: "Ocean Blue",
    description: "Modern ocean blue for smart devices",
    emoji: "🌊",
    colors: {
      primary: "hsl(210, 100%, 56%)",
      secondary: "hsl(210, 60%, 96%)",
      accent: "hsl(199, 100%, 50%)"
    }
  },
  {
    name: "EmeraldTech",
    label: "Emerald Tech",
    description: "Vibrant emerald for eco-tech",
    emoji: "🌿",
    colors: {
      primary: "hsl(155, 100%, 45%)",
      secondary: "hsl(155, 60%, 95%)",
      accent: "hsl(120, 100%, 35%)"
    }
  },
  {
    name: "BluePurple",
    label: "Blue Purple",
    description: "Modern blue to purple gradient",
    emoji: "🌌",
    colors: {
      primary: "hsl(220, 100%, 55%)",
      secondary: "hsl(220, 60%, 96%)",
      accent: "hsl(270, 80%, 94%)"
    }
  },
  {
    name: "CosmicPurple",
    label: "Cosmic Purple",
    description: "Futuristic purple for AI/ML",
    emoji: "🔮",
    colors: {
      primary: "hsl(270, 100%, 60%)",
      secondary: "hsl(270, 60%, 96%)",
      accent: "hsl(300, 100%, 65%)"
    }
  },
  {
    name: "SunsetGlow",
    label: "Sunset Glow",
    description: "Warm sunset colors for comfort",
    emoji: "🌅",
    colors: {
      primary: "hsl(25, 100%, 55%)",
      secondary: "hsl(25, 60%, 95%)",
      accent: "hsl(340, 100%, 65%)"
    }
  },
  {
    name: "Zinc",
    label: "Minimal Zinc",
    description: "Clean neutral theme",
    emoji: "⚪",
    colors: {
      primary: "hsl(240, 5.9%, 10%)",
      secondary: "hsl(240, 4.8%, 95.9%)",
      accent: "hsl(240, 4.8%, 95.9%)"
    }
  },
  {
    name: "Rose",
    label: "Rose Pink",
    description: "Elegant rose theme",
    emoji: "🌹",
    colors: {
      primary: "hsl(346.8, 77.2%, 49.8%)",
      secondary: "hsl(355, 100%, 97%)",
      accent: "hsl(355, 100%, 97%)"
    }
  }
];

interface ThemeSelectorProps {
  variant?: "button" | "compact";
  showLabel?: boolean;
}

export function ThemeSelector({ variant = "button", showLabel = true }: ThemeSelectorProps) {
  const { theme, setTheme } = useTheme();
  const { themeColor, setThemeColor } = useThemeContext();

  const currentThemeOption = themeOptions.find(option => option.name === themeColor);

  const ColorPreview = ({ colors }: { colors: { primary: string; secondary: string; accent: string } }) => (
    <div className="flex gap-1.5">
      <div
        className="w-4 h-4 rounded-full border-2 border-white shadow-sm ring-1 ring-black/10"
        style={{ backgroundColor: colors.primary }}
      />
      <div
        className="w-4 h-4 rounded-full border-2 border-white shadow-sm ring-1 ring-black/10"
        style={{ backgroundColor: colors.secondary }}
      />
      <div
        className="w-4 h-4 rounded-full border-2 border-white shadow-sm ring-1 ring-black/10"
        style={{ backgroundColor: colors.accent }}
      />
    </div>
  );

  if (variant === "compact") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 px-0">
            <Palette className="h-4 w-4" />
            <span className="sr-only">Toggle theme</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Color Theme</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {themeOptions.map((option) => (
            <DropdownMenuItem
              key={option.name}
              onClick={() => setThemeColor(option.name)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-3">
                <span className="text-lg">{option.emoji}</span>
                <ColorPreview colors={option.colors} />
                <span className="text-sm font-medium">{option.label}</span>
              </div>
              {themeColor === option.name && <Check className="h-4 w-4 text-primary" />}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className="space-y-4">
      {/* Dark/Light Mode Toggle */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h4 className="text-sm font-medium">Appearance</h4>
          <p className="text-xs text-muted-foreground">
            Choose light or dark mode
          </p>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              {theme === "light" ? (
                <Sun className="h-4 w-4" />
              ) : theme === "dark" ? (
                <Moon className="h-4 w-4" />
              ) : (
                <Monitor className="h-4 w-4" />
              )}
              {showLabel && (
                <span className="capitalize">{theme || "system"}</span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setTheme("light")}>
              <Sun className="mr-2 h-4 w-4" />
              Light
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("dark")}>
              <Moon className="mr-2 h-4 w-4" />
              Dark
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("system")}>
              <Monitor className="mr-2 h-4 w-4" />
              System
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Color Theme Selector */}
      <div className="space-y-3">
        <div className="space-y-1">
          <h4 className="text-sm font-medium">Color Theme</h4>
          <p className="text-xs text-muted-foreground">
            Choose your preferred color scheme
          </p>
        </div>
        
        <div className="grid grid-cols-1 gap-2">
          {themeOptions.map((option) => (
            <div
              key={option.name}
              className={cn(
                "flex items-center justify-between p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:bg-accent/50 hover:border-primary/30 hover:shadow-md",
                themeColor === option.name && "border-primary bg-accent shadow-lg ring-2 ring-primary/20"
              )}
              onClick={() => setThemeColor(option.name)}
            >
              <div className="flex items-center gap-4">
                <div className="text-2xl">{option.emoji}</div>
                <ColorPreview colors={option.colors} />
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-semibold">{option.label}</span>
                    {option.name === "OceanBlue" && (
                      <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">
                        Recommended
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {option.description}
                  </p>
                </div>
              </div>
              {themeColor === option.name && (
                <Check className="h-5 w-5 text-primary" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Current Theme Preview */}
      {currentThemeOption && (
        <div className="p-4 rounded-xl border-2 bg-gradient-to-r from-card to-accent/20 shadow-sm">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-2xl">{currentThemeOption.emoji}</span>
                <span className="text-sm font-semibold">Current Theme</span>
              </div>
              <ColorPreview colors={currentThemeOption.colors} />
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-primary">
                {currentThemeOption.label}
              </p>
              <p className="text-xs text-muted-foreground">
                {currentThemeOption.description}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ThemeSelector;
