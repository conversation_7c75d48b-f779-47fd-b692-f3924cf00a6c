import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  WifiOff, 
  ServerCrash,
  FileX,
  ShieldAlert,
  Clock
} from "lucide-react";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.log('Error caught by boundary:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error?: Error;
  resetError: () => void;
}

const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetError }) => (
  <ErrorDisplay
    type="generic"
    title="Something went wrong"
    description={error?.message || "An unexpected error occurred"}
    action={
      <Button onClick={resetError} variant="outline">
        <RefreshCw className="w-4 h-4 mr-2" />
        Try Again
      </Button>
    }
  />
);

interface ErrorDisplayProps {
  type?: "network" | "notFound" | "unauthorized" | "server" | "timeout" | "generic";
  title?: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg";
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  type = "generic",
  title,
  description,
  action,
  className,
  size = "md"
}) => {
  const getErrorConfig = () => {
    switch (type) {
      case "network":
        return {
          icon: <WifiOff className="w-12 h-12 text-destructive" />,
          title: title || "Network Error",
          description: description || "Please check your internet connection and try again.",
        };
      case "notFound":
        return {
          icon: <FileX className="w-12 h-12 text-muted-foreground" />,
          title: title || "Page Not Found",
          description: description || "The page you're looking for doesn't exist.",
        };
      case "unauthorized":
        return {
          icon: <ShieldAlert className="w-12 h-12 text-warning" />,
          title: title || "Access Denied",
          description: description || "You don't have permission to access this resource.",
        };
      case "server":
        return {
          icon: <ServerCrash className="w-12 h-12 text-destructive" />,
          title: title || "Server Error",
          description: description || "Something went wrong on our end. Please try again later.",
        };
      case "timeout":
        return {
          icon: <Clock className="w-12 h-12 text-warning" />,
          title: title || "Request Timeout",
          description: description || "The request took too long to complete. Please try again.",
        };
      default:
        return {
          icon: <AlertTriangle className="w-12 h-12 text-destructive" />,
          title: title || "Something went wrong",
          description: description || "An unexpected error occurred. Please try again.",
        };
    }
  };

  const config = getErrorConfig();
  const sizeClasses = {
    sm: "p-4",
    md: "p-6",
    lg: "p-8"
  };

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardContent className={cn("text-center", sizeClasses[size])}>
        <div className="flex justify-center mb-4">
          {config.icon}
        </div>
        <CardTitle className="mb-2">{config.title}</CardTitle>
        <CardDescription className="mb-4">{config.description}</CardDescription>
        {action}
      </CardContent>
    </Card>
  );
};

interface InlineErrorProps {
  message: string;
  variant?: "destructive" | "warning";
  className?: string;
  onDismiss?: () => void;
}

const InlineError: React.FC<InlineErrorProps> = ({
  message,
  variant = "destructive",
  className,
  onDismiss
}) => (
  <Alert variant={variant === "warning" ? "destructive" : variant} className={cn("mb-4", className)}>
    <AlertTriangle className="h-4 w-4" />
    <AlertDescription className="flex justify-between items-center">
      {message}
      {onDismiss && (
        <Button variant="ghost" size="sm" onClick={onDismiss}>
          ×
        </Button>
      )}
    </AlertDescription>
  </Alert>
);

interface RetryableErrorProps {
  error: Error;
  onRetry: () => void;
  retryCount?: number;
  maxRetries?: number;
  className?: string;
}

const RetryableError: React.FC<RetryableErrorProps> = ({
  error,
  onRetry,
  retryCount = 0,
  maxRetries = 3,
  className
}) => {
  const canRetry = retryCount < maxRetries;

  return (
    <ErrorDisplay
      type="generic"
      title="Operation Failed"
      description={error.message}
      className={className}
      action={
        <div className="space-y-2">
          {canRetry ? (
            <Button onClick={onRetry} variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry ({retryCount + 1}/{maxRetries})
            </Button>
          ) : (
            <div className="text-sm text-muted-foreground">
              Maximum retry attempts reached
            </div>
          )}
          <div className="flex gap-2">
            <Button variant="ghost" size="sm" onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
            <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
              Go Back
            </Button>
          </div>
        </div>
      }
    />
  );
};

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  className
}) => (
  <Card className={cn("w-full max-w-md mx-auto", className)}>
    <CardContent className="text-center p-6">
      {icon && (
        <div className="flex justify-center mb-4">
          {icon}
        </div>
      )}
      <CardTitle className="mb-2">{title}</CardTitle>
      {description && (
        <CardDescription className="mb-4">{description}</CardDescription>
      )}
      {action}
    </CardContent>
  </Card>
);

// Hook for error handling
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);
  const [retryCount, setRetryCount] = React.useState(0);

  const handleError = React.useCallback((error: Error) => {
    console.log('Error handled:', error);
    setError(error);
  }, []);

  const retry = React.useCallback(() => {
    setRetryCount(prev => prev + 1);
    setError(null);
  }, []);

  const reset = React.useCallback(() => {
    setError(null);
    setRetryCount(0);
  }, []);

  return {
    error,
    retryCount,
    handleError,
    retry,
    reset
  };
}

export {
  ErrorBoundary,
  ErrorDisplay,
  InlineError,
  RetryableError,
  EmptyState
};
