import * as React from "react";
import { cn } from "@/lib/utils";

interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    "2xl"?: number;
  };
  gap?: number | string;
  children: React.ReactNode;
}

const ResponsiveGrid = React.forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ className, cols = { default: 1, md: 2, lg: 3 }, gap = 4, children, ...props }, ref) => {
    const gridClasses = React.useMemo(() => {
      const classes = [];
      
      // Default columns
      if (cols.default) {
        classes.push(`grid-cols-${cols.default}`);
      }
      
      // Responsive columns
      if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`);
      if (cols.md) classes.push(`md:grid-cols-${cols.md}`);
      if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`);
      if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`);
      if (cols["2xl"]) classes.push(`2xl:grid-cols-${cols["2xl"]}`);
      
      // Gap
      classes.push(`gap-${gap}`);
      
      return classes.join(" ");
    }, [cols, gap]);

    return (
      <div
        ref={ref}
        className={cn("grid", gridClasses, className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ResponsiveGrid.displayName = "ResponsiveGrid";

interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full";
  padding?: boolean;
  children: React.ReactNode;
}

const ResponsiveContainer = React.forwardRef<HTMLDivElement, ResponsiveContainerProps>(
  ({ className, maxWidth = "6xl", padding = true, children, ...props }, ref) => {
    const containerClasses = React.useMemo(() => {
      const classes = ["mx-auto"];
      
      // Max width
      if (maxWidth !== "full") {
        classes.push(`max-w-${maxWidth}`);
      } else {
        classes.push("w-full");
      }
      
      // Padding
      if (padding) {
        classes.push("px-4 sm:px-6 lg:px-8");
      }
      
      return classes.join(" ");
    }, [maxWidth, padding]);

    return (
      <div
        ref={ref}
        className={cn(containerClasses, className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ResponsiveContainer.displayName = "ResponsiveContainer";

interface ResponsiveStackProps extends React.HTMLAttributes<HTMLDivElement> {
  direction?: "row" | "col";
  responsive?: {
    sm?: "row" | "col";
    md?: "row" | "col";
    lg?: "row" | "col";
  };
  gap?: number;
  align?: "start" | "center" | "end" | "stretch";
  justify?: "start" | "center" | "end" | "between" | "around" | "evenly";
  children: React.ReactNode;
}

const ResponsiveStack = React.forwardRef<HTMLDivElement, ResponsiveStackProps>(
  ({ 
    className, 
    direction = "col", 
    responsive = {},
    gap = 4,
    align = "stretch",
    justify = "start",
    children, 
    ...props 
  }, ref) => {
    const stackClasses = React.useMemo(() => {
      const classes = ["flex"];
      
      // Base direction
      classes.push(direction === "row" ? "flex-row" : "flex-col");
      
      // Responsive directions
      if (responsive.sm) {
        classes.push(responsive.sm === "row" ? "sm:flex-row" : "sm:flex-col");
      }
      if (responsive.md) {
        classes.push(responsive.md === "row" ? "md:flex-row" : "md:flex-col");
      }
      if (responsive.lg) {
        classes.push(responsive.lg === "row" ? "lg:flex-row" : "lg:flex-col");
      }
      
      // Gap
      classes.push(`gap-${gap}`);
      
      // Alignment
      const alignMap = {
        start: "items-start",
        center: "items-center",
        end: "items-end",
        stretch: "items-stretch"
      };
      classes.push(alignMap[align]);
      
      // Justify
      const justifyMap = {
        start: "justify-start",
        center: "justify-center",
        end: "justify-end",
        between: "justify-between",
        around: "justify-around",
        evenly: "justify-evenly"
      };
      classes.push(justifyMap[justify]);
      
      return classes.join(" ");
    }, [direction, responsive, gap, align, justify]);

    return (
      <div
        ref={ref}
        className={cn(stackClasses, className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

ResponsiveStack.displayName = "ResponsiveStack";

// Responsive breakpoint hook
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = React.useState<string>("sm");

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width >= 1536) setBreakpoint("2xl");
      else if (width >= 1280) setBreakpoint("xl");
      else if (width >= 1024) setBreakpoint("lg");
      else if (width >= 768) setBreakpoint("md");
      else if (width >= 640) setBreakpoint("sm");
      else setBreakpoint("xs");
    };

    updateBreakpoint();
    window.addEventListener("resize", updateBreakpoint);
    return () => window.removeEventListener("resize", updateBreakpoint);
  }, []);

  return breakpoint;
}

export { ResponsiveGrid, ResponsiveContainer, ResponsiveStack };
