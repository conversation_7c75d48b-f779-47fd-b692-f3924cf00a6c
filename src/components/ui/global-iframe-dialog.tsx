"use client";

import React from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Maximize2, Minimize2, X, RefreshCw, ExternalLink, Loader2 } from 'lucide-react';
import { useIframeDialog } from '@/context/iframe-dialog-provider';

const GlobalIframeDialog: React.FC = () => {
  const { isOpen, config, closeIframeDialog } = useIframeDialog();
  const [isFullscreen, setIsFullscreen] = React.useState(true);
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);
  const [iframeKey, setIframeKey] = React.useState(0);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const refreshIframe = () => {
    setIsLoading(true);
    setHasError(false);
    setIframeKey(prev => prev + 1);
  };

  const openInNewTab = () => {
    if (config?.iframeSrc) {
      window.open(config.iframeSrc, '_blank');
    }
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  // Reset states when dialog opens
  React.useEffect(() => {
    if (isOpen && config) {
      setIsLoading(true);
      setHasError(false);
      setIsFullscreen(true); // Always start in fullscreen mode
    }
  }, [isOpen, config]);

  if (!config) return null;

  return (
    <TooltipProvider>
      <Dialog open={isOpen} onOpenChange={closeIframeDialog}>
        <DialogContent className={`
          ${isFullscreen
            ? 'w-screen h-screen max-w-none m-0 rounded-none p-0 border-0'
            : 'w-full max-w-6xl h-[95vh] sm:h-[95vh] p-0'
          }
          transition-all duration-500 ease-in-out
          bg-gradient-to-br from-background via-background to-muted/20
          border border-border/50 shadow-2xl
          [&>button]:hidden
          ${config.className || ''}
        `}>
          {/* Enhanced Header */}
          <DialogHeader className={`
            relative flex flex-row items-center justify-between
            ${isFullscreen ? 'p-4' : 'p-4 sm:p-6'}
            bg-gradient-to-r from-primary/5 via-accent/5 to-primary/5
            backdrop-blur-sm border-b border-border/30
          `}>
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05] pointer-events-none" />

            {/* Title Section */}
            <div className="flex items-center gap-3 min-w-0 flex-1 relative z-10">
              <div className="p-2 bg-primary/10 rounded-lg border border-primary/20">
                <ExternalLink className="h-4 w-4 text-primary" />
              </div>
              <div className="min-w-0 flex-1">
                <DialogTitle className="text-lg font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent truncate">
                  {config.title}
                </DialogTitle>
                <p className="text-xs text-muted-foreground truncate">
                  {new URL(config.iframeSrc).hostname}
                </p>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex items-center gap-1 flex-shrink-0 relative z-10">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={refreshIframe}
                    className="h-9 w-9 hover:bg-primary/10 hover:text-primary transition-all duration-200"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Refresh</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={openInNewTab}
                    className="h-9 w-9 hover:bg-primary/10 hover:text-primary transition-all duration-200"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Open in new tab</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleFullscreen}
                    className="h-9 w-9 hover:bg-primary/10 hover:text-primary transition-all duration-200"
                  >
                    {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={closeIframeDialog}
                    className="h-9 w-9 hover:bg-destructive/10 hover:text-destructive transition-all duration-200"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Close</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </DialogHeader>

          {/* Content Area */}
          <div className="relative w-full h-full overflow-hidden">
            {/* Loading Overlay */}
            {isLoading && (
              <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10">
                <div className="flex flex-col items-center gap-3">
                  <div className="p-4 bg-primary/10 rounded-full border border-primary/20">
                    <Loader2 className="h-8 w-8 text-primary animate-spin" />
                  </div>
                  <p className="text-sm text-muted-foreground">Loading content...</p>
                </div>
              </div>
            )}

            {/* Error State */}
            {hasError && (
              <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10">
                <div className="flex flex-col items-center gap-4 text-center max-w-md">
                  <div className="p-4 bg-destructive/10 rounded-full border border-destructive/20">
                    <X className="h-8 w-8 text-destructive" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">Failed to load content</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      The content could not be loaded. Please check your connection and try again.
                    </p>
                    <Button onClick={refreshIframe} className="gap-2">
                      <RefreshCw className="h-4 w-4" />
                      Try Again
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Iframe */}
            <iframe
              key={iframeKey}
              src={config.iframeSrc}
              className={`
                w-full border-0 transition-opacity duration-300
                ${isFullscreen
                  ? 'h-[calc(100vh-64px)]'
                  : 'h-[calc(95vh-80px)]'
                }
                ${isLoading ? 'opacity-0' : 'opacity-100'}
              `}
              title={config.title}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              onLoad={handleIframeLoad}
              onError={handleIframeError}
            />
          </div>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
};

export default GlobalIframeDialog;
