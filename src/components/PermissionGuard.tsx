/**
 * Simplified Permission Guard Component using JWT-based permission checking
 */

"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Shield, Lock, ArrowLeft, Home, AlertTriangle } from 'lucide-react';
import { usePermissions } from '@/hooks/usePermissions';
import { checkJWTPermission, checkAnyJWTPermission, checkAllJWTPermissions } from '@/lib/jwt-permissions';
import type { JWTPermissionCheck } from '@/types/permissions';

export interface PermissionGuardProps {
  children: React.ReactNode;
  resource?: string;
  action?: string;
  orgId?: string;
  adminOnly?: boolean;
  showAccessDenied?: boolean;
  fallbackComponent?: React.ReactNode;
  requireAny?: JWTPermissionCheck[];
  requireAll?: JWTPermissionCheck[];
}

export function PermissionGuard({
  children,
  resource,
  action = 'get',
  orgId,
  adminOnly = false,
  showAccessDenied = true,
  fallbackComponent,
  requireAny,
  requireAll,
}: PermissionGuardProps) {
  const { 
    hasPermission, 
    isAdmin,
    isLoading,
    isAuthenticated 
  } = usePermissions();

  // Show loading state while permissions are being fetched
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          <span className="text-sm text-muted-foreground">Checking permissions...</span>
        </div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated) {
    if (showAccessDenied) {
      return (
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <Card className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-amber-100 dark:bg-amber-900 rounded-full flex items-center justify-center mb-4">
                <Lock className="w-6 h-6 text-amber-600 dark:text-amber-400" />
              </div>
              <CardTitle className="text-amber-800 dark:text-amber-200">Authentication Required</CardTitle>
              <CardDescription className="text-amber-700 dark:text-amber-300">
                You need to be logged in to access this content.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <Button onClick={() => window.location.href = '/login'} className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go to Login
              </Button>
            </CardContent>
          </Card>
        </div>
      );
    }
    return fallbackComponent || null;
  }

  // Check admin-only access
  if (adminOnly && !isAdmin(orgId)) {
    if (showAccessDenied) {
      return (
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
                <Shield className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-red-800 dark:text-red-200">Admin Access Required</CardTitle>
              <CardDescription className="text-red-700 dark:text-red-300">
                This area is restricted to administrators only.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      );
    }
    return fallbackComponent || null;
  }

  // Check specific resource permission
  if (resource && !hasPermission(resource, action, orgId)) {
    if (showAccessDenied) {
      return (
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
                <Lock className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-red-800 dark:text-red-200">Access Denied</CardTitle>
              <CardDescription className="text-red-700 dark:text-red-300">
                You don't have permission to access this content.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <div className="flex items-center justify-center space-x-2">
                <Badge variant="outline" className="text-red-600 border-red-300">
                  Required: {resource}:{action}
                </Badge>
              </div>
              <Button 
                variant="outline" 
                onClick={() => window.history.back()}
                className="w-full"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </Button>
            </CardContent>
          </Card>
        </div>
      );
    }
    return fallbackComponent || null;
  }

  // Check requireAny permissions
  if (requireAny && !checkAnyJWTPermission(requireAny)) {
    if (showAccessDenied) {
      return (
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
                <Lock className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-red-800 dark:text-red-200">Access Denied</CardTitle>
              <CardDescription className="text-red-700 dark:text-red-300">
                You need at least one of the required permissions.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <div className="flex flex-wrap items-center justify-center gap-2">
                {requireAny.map((perm, index) => (
                  <Badge key={index} variant="outline" className="text-red-600 border-red-300">
                    {perm.resource}:{perm.action || 'get'}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }
    return fallbackComponent || null;
  }

  // Check requireAll permissions
  if (requireAll && !checkAllJWTPermissions(requireAll)) {
    if (showAccessDenied) {
      return (
        <div className="container mx-auto px-4 py-8 max-w-2xl">
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/50">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4">
                <Lock className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-red-800 dark:text-red-200">Access Denied</CardTitle>
              <CardDescription className="text-red-700 dark:text-red-300">
                You need all of the required permissions.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <div className="flex flex-wrap items-center justify-center gap-2">
                {requireAll.map((perm, index) => (
                  <Badge key={index} variant="outline" className="text-red-600 border-red-300">
                    {perm.resource}:{perm.action || 'get'}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }
    return fallbackComponent || null;
  }

  // User has permission, render children
  return <>{children}</>;
}

/**
 * HOC wrapper for permission checking
 */
export function withPermissionGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<PermissionGuardProps, 'children'>
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionGuard {...guardProps}>
        <Component {...props} />
      </PermissionGuard>
    );
  };
}

/**
 * Hook for checking permissions in components
 */
export function usePagePermissions(
  resource?: string,
  action: string = 'get',
  orgId?: string
) {
  const { hasPermission, isLoading, isAuthenticated } = usePermissions();
  
  const hasAccess = React.useMemo(() => {
    if (isLoading || !isAuthenticated) return false;
    if (!resource) return true; // No specific permission required
    
    return hasPermission(resource, action, orgId);
  }, [resource, action, orgId, hasPermission, isLoading, isAuthenticated]);

  return {
    hasAccess,
    isLoading,
    isAuthenticated,
  };
}

export default PermissionGuard;