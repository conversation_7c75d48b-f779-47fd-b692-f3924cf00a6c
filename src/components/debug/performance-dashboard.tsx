"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  usePerformanceMonitor, 
  useWebVitals, 
  useNetworkMonitor, 
  useFrameRate,
  useBundleMonitor,
  useLongTaskMonitor 
} from '@/hooks/use-performance-monitor';
import { Activity, Wifi, Package, Clock, Zap, AlertTriangle } from 'lucide-react';

interface PerformanceDashboardProps {
  componentName?: string;
  showInProduction?: boolean;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  componentName = 'App',
  showInProduction = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  
  // Don't show in production unless explicitly enabled
  if (process.env.NODE_ENV === 'production' && !showInProduction) {
    return null;
  }

  const { metrics } = usePerformanceMonitor(componentName);
  const vitals = useWebVitals();
  const networkInfo = useNetworkMonitor();
  const fps = useFrameRate();
  const bundleInfo = useBundleMonitor();
  const longTasks = useLongTaskMonitor();

  const getPerformanceScore = () => {
    let score = 100;
    
    // Deduct points for slow metrics
    if (metrics.renderTime > 16) score -= 20;
    if (vitals.LCP && vitals.LCP > 2500) score -= 15;
    if (vitals.FID && vitals.FID > 100) score -= 15;
    if (vitals.CLS && vitals.CLS > 0.1) score -= 15;
    if (fps < 50) score -= 10;
    if (longTasks.length > 5) score -= 10;
    
    return Math.max(0, score);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50"
        size="sm"
        variant="outline"
      >
        <Activity className="w-4 h-4 mr-2" />
        Performance
      </Button>
    );
  }

  const performanceScore = getPerformanceScore();

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-auto">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Performance Monitor</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
            >
              ×
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Score:</span>
            <span className={`font-bold ${getScoreColor(performanceScore)}`}>
              {performanceScore}/100
            </span>
            <Progress value={performanceScore} className="flex-1" />
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="vitals" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="vitals">Vitals</TabsTrigger>
              <TabsTrigger value="render">Render</TabsTrigger>
              <TabsTrigger value="network">Network</TabsTrigger>
              <TabsTrigger value="bundle">Bundle</TabsTrigger>
            </TabsList>
            
            <TabsContent value="vitals" className="space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <div className="text-xs text-muted-foreground">LCP</div>
                  <div className="font-mono text-sm">
                    {vitals.LCP ? `${vitals.LCP.toFixed(0)}ms` : 'N/A'}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-muted-foreground">FID</div>
                  <div className="font-mono text-sm">
                    {vitals.FID ? `${vitals.FID.toFixed(0)}ms` : 'N/A'}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-muted-foreground">CLS</div>
                  <div className="font-mono text-sm">
                    {vitals.CLS ? vitals.CLS.toFixed(3) : 'N/A'}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-muted-foreground">FCP</div>
                  <div className="font-mono text-sm">
                    {vitals.FCP ? `${vitals.FCP.toFixed(0)}ms` : 'N/A'}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4" />
                <span className="text-sm">FPS: {fps}</span>
                <Badge variant={fps >= 55 ? 'default' : 'destructive'}>
                  {fps >= 55 ? 'Good' : 'Poor'}
                </Badge>
              </div>
            </TabsContent>
            
            <TabsContent value="render" className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Render Time</span>
                  <span className="font-mono text-sm">
                    {metrics.renderTime.toFixed(2)}ms
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Mount Time</span>
                  <span className="font-mono text-sm">
                    {metrics.componentMountTime.toFixed(2)}ms
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Re-renders</span>
                  <span className="font-mono text-sm">
                    {metrics.rerenderCount}
                  </span>
                </div>
              </div>
              
              {metrics.memoryUsage && (
                <div className="space-y-1">
                  <div className="text-xs text-muted-foreground">Memory Usage</div>
                  <div className="text-sm">
                    {metrics.memoryUsage.used}MB / {metrics.memoryUsage.total}MB
                  </div>
                  <Progress 
                    value={(metrics.memoryUsage.used / metrics.memoryUsage.total) * 100} 
                  />
                </div>
              )}
              
              {longTasks.length > 0 && (
                <div className="flex items-center gap-2 text-warning">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-sm">{longTasks.length} long tasks</span>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="network" className="space-y-3">
              <div className="flex items-center gap-2">
                <Wifi className="w-4 h-4" />
                <span className="text-sm">
                  {networkInfo.online ? 'Online' : 'Offline'}
                </span>
                <Badge variant={networkInfo.online ? 'default' : 'destructive'}>
                  {networkInfo.effectiveType}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Downlink</span>
                  <span className="font-mono text-sm">
                    {networkInfo.downlink} Mbps
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">RTT</span>
                  <span className="font-mono text-sm">
                    {networkInfo.rtt}ms
                  </span>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="bundle" className="space-y-3">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4" />
                <span className="text-sm">Bundle Info</span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Load Time</span>
                  <span className="font-mono text-sm">
                    {bundleInfo.loadTime.toFixed(0)}ms
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Resources</span>
                  <span className="font-mono text-sm">
                    {bundleInfo.resourceCount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Total Size</span>
                  <span className="font-mono text-sm">
                    {bundleInfo.totalSize}KB
                  </span>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceDashboard;
