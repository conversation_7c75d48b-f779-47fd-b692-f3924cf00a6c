"use client";
import * as React from "react";

import { useThemeContext } from "@/context/theme-data-provider";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";
import type { ThemeColors } from "@/types/theme-types";

const availableThemeColors = [
  { name: "Zinc", light: "bg-zinc-900", dark: "bg-zinc-700" },
  { name: "Red", light: "bg-red-600", dark: "bg-red-700" },
  // { name: "<PERSON>", light: "bg-rose-600", dark: "bg-rose-700" },
  { name: "<PERSON>", light: "bg-amber-500", dark: "bg-amber-700" },
  { name: "Yellow", light: "bg-yellow-500", dark: "bg-yellow-700" },
  { name: "Green", light: "bg-green-600", dark: "bg-green-500" },
  { name: "<PERSON><PERSON>", light: "bg-cyan-500", dark: "bg-cyan-700" },
  { name: "Sky", light: "bg-sky-600", dark: "bg-sky-700" },
  { name: "Violet", light: "bg-violet-500", dark: "bg-violet-700" },
];

export function ThemeColorToggle() {
  const { setThemeColor } = useThemeContext();
  const { theme } = useTheme();

  const systemPreferenceIsDark = window.matchMedia(
    "(prefers-color-scheme: dark)"
  ).matches;
  const newTheme =
    theme === "system" ? (systemPreferenceIsDark ? "dark" : "light") : theme;

  const createSelectItems = () => {
    return availableThemeColors.map(({ name, light, dark }) => (
      <div
        key={name}
        onClick={() => setThemeColor(name as ThemeColors)}
        className="flex item-center space-x-3 p-1 rounded-full cursor-pointer border border-secondary-foreground hover:animate-bounce"
      >
        <div
          className={cn(
            "rounded-full",
            "w-[20px]",
            "h-[20px]",
            newTheme === "light" ? light : dark
          )}
        ></div>
        {/* <div className="text-sm">{name}</div> */}
      </div>
    ));
  };

  return (
    <>
      <span className="text-sm text-secondary-foreground">
        Pick Color Theme
      </span>
      <div className="flex items-center space-x-3 py-2 mb-10">
        {createSelectItems()}
      </div>
    </>
  );
}
