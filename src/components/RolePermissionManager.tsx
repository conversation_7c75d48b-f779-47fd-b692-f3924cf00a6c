/**
 * Component for managing role-permission assignments using RolePermissionMappings API
 */

import React, { useState, useEffect } from 'react';
import { Role } from "@/app/(DashboardLayout)/roles/types";
import { Permission } from "@/app/(DashboardLayout)/permissions/types";
import { RolePermissionMapping } from "@/types/mappings";
import rolePermissionMappingsApiRequests from "@/apiRequests/admin/rolePermissionMappings";
import permissionsApiRequests from "@/apiRequests/admin/permissions";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Plus, Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface RolePermissionManagerProps {
  role: Role;
  onRolePermissionsChanged?: (rolePermissionMappings: RolePermissionMapping[]) => void;
  disabled?: boolean;
  showTitle?: boolean;
}

export const RolePermissionManager: React.FC<RolePermissionManagerProps> = ({
  role,
  onRolePermissionsChanged,
  disabled = false,
  showTitle = true,
}) => {
  const [availablePermissions, setAvailablePermissions] = useState<Permission[]>([]);
  const [rolePermissionMappings, setRolePermissionMappings] = useState<RolePermissionMapping[]>([]);
  const [selectedPermissionId, setSelectedPermissionId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const { toast } = useToast();

  // Load available permissions and current role permission mappings
  useEffect(() => {
    const loadData = async () => {
      if (!role.id) return;

      try {
        setInitialLoading(true);

        // Load available permissions
        const permissionsResponse = await permissionsApiRequests.getPermissions(role.orgId, 1, 100);
        if (permissionsResponse.status === 200 && permissionsResponse.payload) {
          setAvailablePermissions(permissionsResponse.payload.docs || []);
        }

        // Load current role permission mappings
        const mappingsResponse = await rolePermissionMappingsApiRequests.getRolePermissionMappingsByRoleId(
          role.id,
          1,
          100,
          { isActive: true }
        );
        if (mappingsResponse.status === 200 && mappingsResponse.payload) {
          setRolePermissionMappings(mappingsResponse.payload.docs || []);
        }
      } catch (error) {
        console.error('Error loading role permission data:', error);
        toast({
          title: "Lỗi",
          description: "Không thể tải dữ liệu quyền vai trò",
          variant: "destructive",
        });
      } finally {
        setInitialLoading(false);
      }
    };

    loadData();
  }, [role.id, role.orgId, toast]);

  // Get currently assigned permission IDs
  const assignedPermissionIds = rolePermissionMappings
    .filter(mapping => mapping.isActive !== false)
    .map(mapping => mapping.permissionId);

  // Get available permissions that are not yet assigned
  const unassignedPermissions = availablePermissions.filter(
    permission => !assignedPermissionIds.includes(permission.id)
  );

  // Assign a permission to the role
  const handleAssignPermission = async (permissionId: string) => {
    if (!role.id || !permissionId) return;

    try {
      setLoading(true);

      // const response = await rolePermissionMappingsApiRequests.createRolePermissionMapping({
      //   roleId: role.id,
      //   permissionId,
      //   isActive: true,
      // });

      // if (response.status === 200 || response.status === 201) {
      //   const newMapping = response.payload;
      //   const updatedMappings = [...rolePermissionMappings, newMapping];
      //   setRolePermissionMappings(updatedMappings);
      //   setSelectedPermissionId('');

      //   // Notify parent component
      //   if (onRolePermissionsChanged) {
      //     onRolePermissionsChanged(updatedMappings);
      //   }

      //   toast({
      //     title: "Thành công",
      //     description: "Đã gán quyền thành công",
      //   });
      // }
    } catch (error) {
      console.error('Error assigning permission:', error);
      toast({
        title: "Lỗi",
        description: "Không thể gán quyền",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Remove a permission from the role
  const handleRemovePermission = async (mappingId: string) => {
    try {
      setLoading(true);

      const response = await rolePermissionMappingsApiRequests.deleteRolePermissionMapping(mappingId);

      if (response.status === 200 || response.status === 204) {
        const updatedMappings = rolePermissionMappings.filter(
          mapping => mapping.id !== mappingId
        );
        setRolePermissionMappings(updatedMappings);

        // Notify parent component
        if (onRolePermissionsChanged) {
          onRolePermissionsChanged(updatedMappings);
        }

        toast({
          title: "Thành công",
          description: "Đã xóa quyền thành công",
        });
      }
    } catch (error) {
      console.error('Error removing permission:', error);
      toast({
        title: "Lỗi",
        description: "Không thể xóa quyền",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Đang tải...</span>
      </div>
    );
  }

  const content = (
    <div className="space-y-4">
      {/* Current Permissions */}
      <div>
        <h4 className="text-sm font-medium mb-2">Quyền đã gán</h4>
        <div className="flex flex-wrap gap-2">
          {rolePermissionMappings
            .filter(mapping => mapping.isActive !== false)
            .map((mapping) => {
              const permission = availablePermissions.find(p => p.id === mapping.permissionId);
              return (
                <Badge
                  key={mapping.id}
                  variant="secondary"
                  className="flex items-center gap-1 bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                >
                  {permission?.name || `${permission?.resource}:${permission?.action}` || mapping.permissionId}
                  {!disabled && (
                    <button
                      onClick={() => handleRemovePermission(mapping.id)}
                      disabled={loading}
                      className="ml-1 hover:bg-green-200 dark:hover:bg-green-800 rounded-full p-0.5"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  )}
                </Badge>
              );
            })}
          {assignedPermissionIds.length === 0 && (
            <span className="text-sm text-muted-foreground">Chưa có quyền nào được gán</span>
          )}
        </div>
      </div>

      {/* Add New Permission */}
      {!disabled && unassignedPermissions.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Gán quyền mới</h4>
          <div className="flex gap-2">
            <Select
              value={selectedPermissionId}
              onValueChange={setSelectedPermissionId}
              disabled={loading}
            >
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Chọn quyền để gán" />
              </SelectTrigger>
              <SelectContent>
                {unassignedPermissions.map((permission) => (
                  <SelectItem key={permission.id} value={permission.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{permission.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {permission.resource}:{permission.action}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              onClick={() => handleAssignPermission(selectedPermissionId)}
              disabled={!selectedPermissionId || loading}
              size="sm"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      {!disabled && (
        <div className="pt-2 border-t">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                if (unassignedPermissions.length === 0) return;
                try {
                  setLoading(true);
                  await rolePermissionMappingsApiRequests.assignPermissionsToRole(
                    role.id,
                    unassignedPermissions.map(p => p.id)
                  );
                  // Reload mappings
                  const mappingsResponse = await rolePermissionMappingsApiRequests.getRolePermissionMappingsByRoleId(
                    role.id,
                    1,
                    100,
                    { isActive: true }
                  );
                  if (mappingsResponse.status === 200 && mappingsResponse.payload) {
                    const updatedMappings = mappingsResponse.payload.docs || [];
                    setRolePermissionMappings(updatedMappings);
                    if (onRolePermissionsChanged) {
                      onRolePermissionsChanged(updatedMappings);
                    }
                  }
                  toast({ title: "Thành công", description: "Đã gán tất cả quyền" });
                } catch (error) {
                  toast({ title: "Lỗi", description: "Không thể gán tất cả quyền", variant: "destructive" });
                } finally {
                  setLoading(false);
                }
              }}
              disabled={loading || unassignedPermissions.length === 0}
            >
              Gán tất cả
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                if (rolePermissionMappings.length === 0) return;
                try {
                  setLoading(true);
                  await rolePermissionMappingsApiRequests.bulkDeleteRolePermissionMappings(
                    rolePermissionMappings.map(mapping => mapping.id)
                  );
                  setRolePermissionMappings([]);
                  if (onRolePermissionsChanged) {
                    onRolePermissionsChanged([]);
                  }
                  toast({ title: "Thành công", description: "Đã xóa tất cả quyền" });
                } catch (error) {
                  toast({ title: "Lỗi", description: "Không thể xóa tất cả quyền", variant: "destructive" });
                } finally {
                  setLoading(false);
                }
              }}
              disabled={loading || rolePermissionMappings.length === 0}
            >
              Xóa tất cả
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  if (showTitle) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Quyền vai trò</CardTitle>
        </CardHeader>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }

  return content;
};

export default RolePermissionManager;
