import { ReactNode } from 'react';

// ==========================================================================
// RESPONSIVE TYPES
// ==========================================================================

export interface ResponsiveBreakpoints {
  xs?: number | string;
  sm?: number | string;
  md?: number | string;
  lg?: number | string;
  xl?: number | string;
  '2xl'?: number | string;
}

export interface ResponsiveValue<T> {
  default?: T;
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}

// ==========================================================================
// COMPONENT PROPS TYPES
// ==========================================================================

export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  id?: string;
  'data-testid'?: string;
}

export interface LoadingProps extends BaseComponentProps {
  isLoading?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'skeleton' | 'dots' | 'pulse';
  message?: string;
}

export interface ErrorProps extends BaseComponentProps {
  error?: Error | string;
  type?: 'network' | 'notFound' | 'unauthorized' | 'server' | 'timeout' | 'generic';
  title?: string;
  description?: string;
  onRetry?: () => void;
  retryCount?: number;
  maxRetries?: number;
}

export interface AnimationProps extends BaseComponentProps {
  variant?: 'fadeIn' | 'slideIn' | 'scaleIn' | 'bounceIn';
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  duration?: number;
  stagger?: boolean;
}

// ==========================================================================
// LAYOUT TYPES
// ==========================================================================

export interface GridProps extends BaseComponentProps {
  cols?: ResponsiveValue<number>;
  gap?: number | string;
  autoFit?: boolean;
  minItemWidth?: string;
}

export interface StackProps extends BaseComponentProps {
  direction?: 'row' | 'col';
  responsive?: ResponsiveValue<'row' | 'col'>;
  gap?: number;
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: boolean;
}

export interface ContainerProps extends BaseComponentProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: boolean | ResponsiveValue<boolean>;
  centered?: boolean;
}

// ==========================================================================
// FORM TYPES
// ==========================================================================

export interface FormFieldProps extends BaseComponentProps {
  label?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  helperText?: string;
}

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  icon?: ReactNode;
}

export interface FormSelectProps extends FormFieldProps {
  options: SelectOption[];
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  value?: string | number | (string | number)[];
  onChange?: (value: string | number | (string | number)[]) => void;
}

// ==========================================================================
// DATA DISPLAY TYPES
// ==========================================================================

export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: any, record: T, index: number) => ReactNode;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  fixed?: 'left' | 'right';
}

export interface TableProps<T = any> extends BaseComponentProps {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: PaginationProps;
  selection?: {
    type: 'checkbox' | 'radio';
    selectedKeys?: (string | number)[];
    onChange?: (selectedKeys: (string | number)[]) => void;
  };
  sorting?: {
    sortKey?: string;
    sortOrder?: 'asc' | 'desc';
    onChange?: (sortKey: string, sortOrder: 'asc' | 'desc') => void;
  };
  filtering?: {
    filters?: Record<string, any>;
    onChange?: (filters: Record<string, any>) => void;
  };
}

export interface PaginationProps {
  current: number;
  total: number;
  pageSize: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
  onChange?: (page: number, pageSize: number) => void;
}

// ==========================================================================
// CARD TYPES
// ==========================================================================

export interface CardProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  actions?: ReactNode;
  cover?: ReactNode;
  hoverable?: boolean;
  loading?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export interface StatsCardProps extends CardProps {
  value: string | number;
  label: string;
  icon?: ReactNode;
  trend?: {
    value: number;
    type: 'up' | 'down' | 'neutral';
  };
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}

// ==========================================================================
// NAVIGATION TYPES
// ==========================================================================

export interface NavigationItem {
  key: string;
  label: string;
  icon?: ReactNode;
  href?: string;
  onClick?: () => void;
  children?: NavigationItem[];
  disabled?: boolean;
  badge?: string | number;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: ReactNode;
}

// ==========================================================================
// THEME TYPES
// ==========================================================================

export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  borderRadius: number;
  fontFamily: string;
  fontSize: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
  };
}

// ==========================================================================
// UTILITY TYPES
// ==========================================================================

export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type Color = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
export type Variant = 'solid' | 'outline' | 'ghost' | 'link';
export type Position = 'top' | 'bottom' | 'left' | 'right';
export type Alignment = 'start' | 'center' | 'end';

// Generic API Response
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

// Pagination Response
export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter and Sort
export interface FilterOption {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'number' | 'boolean';
  options?: SelectOption[];
}

export interface SortOption {
  key: string;
  label: string;
  direction: 'asc' | 'desc';
}
