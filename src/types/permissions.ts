/**
 * Simplified JWT-based permission types
 * This replaces the complex system permission types with simple JWT token-based checking
 */

// JWT Permission Check interface
export interface JWTPermissionCheck {
  resource: string;
  action?: string; // defaults to 'get'
  orgId?: string;
}

// Legacy compatibility type
export interface PermissionCheck {
  resource: string;
  action: string;
  permissionId?: string;
}

// Known resources (from JWT token payload)
export const RESOURCES = {
  ALL: '*',
  USER: 'user',
  ORGANIZATION: 'organization', 
  DEVICE: 'device',
  FIRMWARE: 'firmware',
  ROLE: 'role',
  CODE_PUSH: 'codePush',
} as const;

// Known actions (from JWT token payload)
export const ACTIONS = {
  ALL: '*',
  GET: 'get',
  CREATE: 'create',
  UPDATE: 'update', 
  DELETE: 'delete'
} as const;

export type Resource = typeof RESOURCES[keyof typeof RESOURCES];
export type Action = typeof ACTIONS[keyof typeof ACTIONS];

// Keep legacy exports for backward compatibility
export const SYSTEM_RESOURCES = Object.values(RESOURCES);
export const SYSTEM_ACTIONS = Object.values(ACTIONS);
export type SystemResource = Resource;
export type SystemAction = Action;

// User permissions from JWT token
export interface UserPermissions {
  [resource: string]: string[]; // resource -> actions
}

// JWT Token payload structure (for reference)
export interface JWTRole {
  orgId: string;
  permissions: {
    [resource: string]: string[]; // resource -> actions array
  };
}

export interface JWTTokenPayload {
  userId: string;
  roles: JWTRole[];
  sessionId: string;
  iat: number;
  exp: number;
}

// Utility type for permission-protected components
export interface PermissionGuardProps {
  children: React.ReactNode;
  resource: string;
  action?: string;
  orgId?: string;
  fallback?: React.ReactNode;
  showAccessDenied?: boolean;
}

// Hook return types
export interface UsePermissionResult {
  hasPermission: boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface UsePermissionsResult {
  hasPermission: (resource: string, action?: string, orgId?: string) => boolean;
  hasAnyPermission: (permissions: JWTPermissionCheck[]) => boolean;
  hasAllPermissions: (permissions: JWTPermissionCheck[]) => boolean;
  isAdmin: (orgId?: string) => boolean;
  userPermissions: UserPermissions;
  userOrgIds: string[];
  hasOrgAccess: (orgId: string) => boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
}