import { User } from "@/app/(DashboardLayout)/users/types";
import { Role } from "@/app/(DashboardLayout)/settings/roles/types";
import { Permission } from "@/app/(DashboardLayout)/settings/permissions/types";
import { Organization } from "@/app/(DashboardLayout)/organizations/types";

/**
 * UserRoleMapping - Mapping between User and Role
 */
export interface UserRoleMapping {
  id: string;
  userId: string;
  roleId: string;
  orgId: string;
  
  // Related entities (populated when needed)
  user?: User;
  role?: Role;
  org?: Organization;
  
  // Metadata
  isActive?: boolean;
  expiresAt?: string | null;
  assignedBy?: string; // userId who assigned this role
  assignedAt?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

/**
 * RolePermissionMapping - Mapping between Role and Permission
 */
export interface RolePermissionMapping {
  id: string;
  roleId: string;
  permissionId: string;
  orgId: string;
  
  // Related entities (populated when needed)
  role?: Role;
  permission?: Permission;
  org?: Organization;
  
  // Metadata
  isActive?: boolean;
  grantedBy?: string; // userId who granted this permission
  grantedAt?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

/**
 * Request/Response types for UserRoleMapping APIs
 */
export interface CreateUserRoleMappingRequest {
  userId: string;
  roleId: string;
  // Note: orgId is not needed in request body for UserRoles API (it's in the path)
  isActive?: boolean;
  expiresAt?: string | null;
}

export interface UpdateUserRoleMappingRequest {
  isActive?: boolean;
  expiresAt?: string | null;
}

export interface UserRoleMappingFilters {
  userId?: string;
  roleId?: string;
  orgId?: string;
  isActive?: boolean;
  search?: string;
}

/**
 * Request/Response types for RolePermissionMapping APIs
 */
export interface CreateRolePermissionMappingRequest {
  roleId: string;
  permissionId: string;
  orgId: string; // Required in request body for RolePermissions API
  isActive?: boolean;
}

export interface UpdateRolePermissionMappingRequest {
  isActive?: boolean;
}

export interface RolePermissionMappingFilters {
  roleId?: string;
  permissionId?: string;
  orgId?: string;
  isActive?: boolean;
  search?: string;
}

/**
 * Bulk operations types
 */
export interface BulkCreateUserRoleMappingsRequest {
  mappings: CreateUserRoleMappingRequest[];
}

export interface BulkCreateRolePermissionMappingsRequest {
  mappings: CreateRolePermissionMappingRequest[];
}

/**
 * Response types for listing APIs (based on actual API structure)
 */
export interface UserRoleMappingListResponse {
  docs: UserRoleMapping[];
  totalDocs: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
  page: number;
  totalPages: number;
  nextPage: object;
  prevPage: object;
  pagingCounter: number;
  meta: object;
}

export interface RolePermissionMappingListResponse {
  docs: RolePermissionMapping[];
  totalDocs: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
  page: number;
  totalPages: number;
  nextPage: object;
  prevPage: object;
  pagingCounter: number;
  meta: object;
}

/**
 * API Response types based on actual API structure
 */
export interface ApiRolePermissionMappingResponse {
  id: string;
  roleId: string;
  role: {
    id: string;
    orgId: string;
    org: {
      abbreviation: string;
      domain: string;
      orgName: string;
      id: string;
      createdAt: string;
      updatedAt: string;
    };
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  permissionId: string;
  permission: {
    id: string;
    resource: string;
    action: string;
    createdAt: string;
    updatedAt: string;
  };
  createdAt: string;
  updatedAt: string;
}
