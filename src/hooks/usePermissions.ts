/**
 * Simplified JWT-based permissions hook
 */

import { useMemo } from 'react';
import { useAuth } from './useAuth';
import {
  checkJWTPermission,
  checkAnyJWTPermission,
  checkAllJWTPermissions,
  isJWTAdmin,
  getJWTUserPermissions,
  getJWTUserOrgIds,
  hasJWTOrgAccess,
  type J<PERSON><PERSON>ermissionCheck,
} from '@/lib/jwt-permissions';

export interface UsePermissionsReturn {
  // JWT-based permissions
  hasPermission: (resource: string, action?: string, orgId?: string) => boolean;
  hasAnyPermission: (permissions: JWTPermissionCheck[]) => boolean;
  hasAllPermissions: (permissions: JWTPermissionCheck[]) => boolean;
  userPermissions: Record<string, string[]>;

  // Common utilities
  isAdmin: (orgId?: string) => boolean;
  userOrgIds: string[];
  hasOrgAccess: (orgId: string) => boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export function usePermissions(): UsePermissionsReturn {
  const { isLoading, isAuthenticated } = useAuth();

  // Memoize permission functions to avoid unnecessary re-renders
  const permissionFunctions = useMemo(() => {
    if (!isAuthenticated) {
      return {
        hasPermission: () => false,
        hasAnyPermission: () => false,
        hasAllPermissions: () => false,
        userPermissions: {},
        isAdmin: () => false,
        userOrgIds: [],
        hasOrgAccess: () => false,
      };
    }

    return {
      hasPermission: (resource: string, action: string = 'get', orgId?: string) =>
        checkJWTPermission(resource, action, orgId),

      hasAnyPermission: (permissions: JWTPermissionCheck[]) =>
        checkAnyJWTPermission(permissions),

      hasAllPermissions: (permissions: JWTPermissionCheck[]) =>
        checkAllJWTPermissions(permissions),

      userPermissions: getJWTUserPermissions(),

      isAdmin: (orgId?: string) => isJWTAdmin(orgId),

      userOrgIds: getJWTUserOrgIds(),

      hasOrgAccess: (orgId: string) => hasJWTOrgAccess(orgId),
    };
  }, [isAuthenticated]);

  return {
    ...permissionFunctions,
    isLoading,
    isAuthenticated,
  };
}

/**
 * Hook for checking a specific permission
 * @param resource Resource name
 * @param action Action name (defaults to 'get')
 * @param orgId Optional organization ID
 * @returns Object with permission status and loading state
 */
export function usePermission(
  resource: string,
  action: string = 'get',
  orgId?: string
): {
  hasPermission: boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
} {
  const { hasPermission, isLoading, isAuthenticated } = usePermissions();

  const hasPermissionResult = useMemo(() => {
    return hasPermission(resource, action, orgId);
  }, [hasPermission, resource, action, orgId]);

  return {
    hasPermission: hasPermissionResult,
    isLoading,
    isAuthenticated,
  };
}

/**
 * Hook for checking admin status
 * @param orgId Optional organization ID
 * @returns Object with admin status and loading state
 */
export function useIsAdmin(orgId?: string): {
  isAdmin: boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
} {
  const { isAdmin, isLoading, isAuthenticated } = usePermissions();

  const isAdminResult = useMemo(() => {
    return isAdmin(orgId);
  }, [isAdmin, orgId]);

  return {
    isAdmin: isAdminResult,
    isLoading,
    isAuthenticated,
  };
}
