import { useEffect, useRef, useState, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentMountTime: number;
  memoryUsage?: number;
  rerenderCount: number;
}

interface WebVitals {
  CLS?: number;
  FID?: number;
  FCP?: number;
  LCP?: number;
  TTFB?: number;
}

/**
 * Hook for monitoring component performance
 */
export function usePerformanceMonitor(componentName: string) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentMountTime: 0,
    rerenderCount: 0
  });

  const mountTimeRef = useRef<number>(0);
  const renderStartRef = useRef<number>(0);
  const rerenderCountRef = useRef<number>(0);

  // Track component mount time
  useEffect(() => {
    mountTimeRef.current = performance.now();
    
    return () => {
      const mountTime = performance.now() - mountTimeRef.current;
      setMetrics(prev => ({
        ...prev,
        componentMountTime: mountTime
      }));
    };
  }, []);

  // Track render time
  useEffect(() => {
    renderStartRef.current = performance.now();
    rerenderCountRef.current += 1;

    const renderTime = performance.now() - renderStartRef.current;
    
    setMetrics(prev => ({
      ...prev,
      renderTime,
      rerenderCount: rerenderCountRef.current
    }));

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName}:`, {
        renderTime: `${renderTime.toFixed(2)}ms`,
        rerenderCount: rerenderCountRef.current,
        memoryUsage: getMemoryUsage()
      });
    }
  });

  const getMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
      };
    }
    return null;
  }, []);

  return {
    metrics: {
      ...metrics,
      memoryUsage: getMemoryUsage()
    }
  };
}

/**
 * Hook for monitoring Web Vitals
 */
export function useWebVitals() {
  const [vitals, setVitals] = useState<WebVitals>({});

  useEffect(() => {
    // Dynamic import to avoid SSR issues
    // Simplified web vitals tracking - just track what's available
    try {
      // Basic performance metrics
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          setVitals(prev => ({
            ...prev,
            FCP: navigation.loadEventEnd - navigation.loadEventStart,
            LCP: navigation.loadEventEnd - navigation.fetchStart,
            TTFB: navigation.responseStart - navigation.requestStart
          }));
        }
      }
    } catch (error) {
      console.warn('Performance monitoring not available:', error);
    }
  }, []);

  return vitals;
}

/**
 * Hook for monitoring network performance
 */
export function useNetworkMonitor() {
  const [networkInfo, setNetworkInfo] = useState({
    online: navigator.onLine,
    effectiveType: '4g',
    downlink: 10,
    rtt: 100
  });

  useEffect(() => {
    const updateOnlineStatus = () => {
      setNetworkInfo(prev => ({ ...prev, online: navigator.onLine }));
    };

    const updateNetworkInfo = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        setNetworkInfo(prev => ({
          ...prev,
          effectiveType: connection.effectiveType || '4g',
          downlink: connection.downlink || 10,
          rtt: connection.rtt || 100
        }));
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', updateNetworkInfo);
      updateNetworkInfo(); // Initial call
    }

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
      
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        connection.removeEventListener('change', updateNetworkInfo);
      }
    };
  }, []);

  return networkInfo;
}

/**
 * Hook for monitoring frame rate
 */
export function useFrameRate() {
  const [fps, setFps] = useState(60);
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const animationIdRef = useRef<number>();

  useEffect(() => {
    const measureFPS = () => {
      frameCountRef.current++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTimeRef.current + 1000) {
        setFps(Math.round((frameCountRef.current * 1000) / (currentTime - lastTimeRef.current)));
        frameCountRef.current = 0;
        lastTimeRef.current = currentTime;
      }
      
      animationIdRef.current = requestAnimationFrame(measureFPS);
    };

    animationIdRef.current = requestAnimationFrame(measureFPS);

    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
    };
  }, []);

  return fps;
}

/**
 * Hook for monitoring bundle size and loading performance
 */
export function useBundleMonitor() {
  const [bundleInfo, setBundleInfo] = useState({
    loadTime: 0,
    resourceCount: 0,
    totalSize: 0
  });

  useEffect(() => {
    const measureBundlePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];

      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      const totalSize = resources.reduce((total, resource) => {
        return total + (resource.transferSize || 0);
      }, 0);

      setBundleInfo({
        loadTime,
        resourceCount: resources.length,
        totalSize: Math.round(totalSize / 1024) // KB
      });
    };

    // Wait for page load to complete
    if (document.readyState === 'complete') {
      measureBundlePerformance();
    } else {
      window.addEventListener('load', measureBundlePerformance);
      return () => window.removeEventListener('load', measureBundlePerformance);
    }
  }, []);

  return bundleInfo;
}

/**
 * Hook for performance profiling with React DevTools
 */
export function useProfiler(id: string, onRender?: (id: string, phase: string, actualDuration: number) => void) {
  const profileData = useRef<Array<{
    id: string;
    phase: string;
    actualDuration: number;
    baseDuration: number;
    startTime: number;
    commitTime: number;
  }>>([]);

  const handleRender = useCallback((
    id: string,
    phase: 'mount' | 'update',
    actualDuration: number,
    baseDuration: number,
    startTime: number,
    commitTime: number
  ) => {
    const data = {
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime
    };

    profileData.current.push(data);
    onRender?.(id, phase, actualDuration);

    // Log slow renders in development
    if (process.env.NODE_ENV === 'development' && actualDuration > 16) {
      console.warn(`[Slow Render] ${id} took ${actualDuration.toFixed(2)}ms to ${phase}`);
    }
  }, [onRender]);

  const getProfileData = useCallback(() => {
    return profileData.current;
  }, []);

  const clearProfileData = useCallback(() => {
    profileData.current = [];
  }, []);

  return {
    handleRender,
    getProfileData,
    clearProfileData
  };
}

/**
 * Hook for monitoring long tasks
 */
export function useLongTaskMonitor() {
  const [longTasks, setLongTasks] = useState<Array<{
    duration: number;
    startTime: number;
  }>>([]);

  useEffect(() => {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const tasks = entries.map(entry => ({
          duration: entry.duration,
          startTime: entry.startTime
        }));
        
        setLongTasks(prev => [...prev, ...tasks]);

        // Log long tasks in development
        if (process.env.NODE_ENV === 'development') {
          tasks.forEach(task => {
            console.warn(`[Long Task] Duration: ${task.duration.toFixed(2)}ms`);
          });
        }
      });

      try {
        observer.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        console.warn('Long task monitoring not supported');
      }

      return () => observer.disconnect();
    }
  }, []);

  return longTasks;
}
