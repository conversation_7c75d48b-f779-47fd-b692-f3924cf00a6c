/**
 * Auth hook for managing authentication state using JWT
 */

import { useState, useEffect, useCallback } from 'react';
import { decodeJWT, isTokenValid, type JWTPayload } from '@/lib/jwt';
import { clientSessionToken, clientUserInfo, clearAuthData } from '@/lib/http';
import authApiRequests from '@/apiRequests/user/auth';

export interface UseAuthReturn {
  user: JWTPayload | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;
  logout: () => Promise<void>;
  refreshAuth: () => void;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<JWTPayload | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [token, setToken] = useState<string | null>(null);

  // Refresh auth state from current token
  const refreshAuth = useCallback(() => {
    try {
      const currentToken = clientSessionToken.value;
      setToken(currentToken);

      if (!currentToken) {
        setUser(null);
        setIsLoading(false);
        return;
      }

      if (!isTokenValid(currentToken)) {
        setUser(null);
        setToken(null);
        clientSessionToken.clearTokens();
        setIsLoading(false);
        return;
      }

      const decodedUser = decodeJWT(currentToken);
      setUser(decodedUser);
      setIsLoading(false);
    } catch (error) {
      console.log('Error refreshing auth:', error);
      setUser(null);
      setToken(null);
      setIsLoading(false);
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    refreshAuth();
  }, [refreshAuth]);

  // Listen for token changes
  useEffect(() => {
    const checkTokenChanges = () => {
      const currentToken = clientSessionToken.value;
      if (currentToken !== token) {
        refreshAuth();
      }
    };

    // Check for token changes periodically
    const interval = setInterval(checkTokenChanges, 1000);

    return () => clearInterval(interval);
  }, [token, refreshAuth]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Call logout API
      await authApiRequests.logout({});
      
      // Clear all authentication data
      clearAuthData();
      
      // Update state
      setUser(null);
      setToken(null);
      
      // Redirect to login
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    } catch (error) {
      console.log('Error during logout:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const isAuthenticated = !!user && !!token && isTokenValid(token);

  return {
    user,
    isAuthenticated,
    isLoading,
    token,
    logout,
    refreshAuth,
  };
}
