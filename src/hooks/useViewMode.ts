"use client";

import { useState, useEffect } from "react";

export type ViewMode = "table" | "grid" | "list" | "card";

interface UseViewModeOptions {
  defaultMode?: ViewMode;
  storageKey?: string;
  syncAcrossPages?: boolean;
}

interface UseViewModeReturn {
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode | string) => void;
  toggleViewMode: () => void;
}

/**
 * Custom hook để quản lý view mode với localStorage persistence
 * và đồng bộ hóa trạng thái giữa các trang
 */
export const useViewMode = (options: UseViewModeOptions = {}): UseViewModeReturn => {
  const {
    defaultMode = "table",
    storageKey = "app-view-mode",
    syncAcrossPages = true
  } = options;

  // State để lưu view mode hiện tại
  const [viewMode, setViewModeState] = useState<ViewMode>(defaultMode);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load view mode từ localStorage khi component mount
  useEffect(() => {
    if (typeof window === "undefined") return;

    try {
      const savedMode = localStorage.getItem(storageKey);
      if (savedMode && isValidViewMode(savedMode)) {
        setViewModeState(savedMode as ViewMode);
      }
    } catch (error) {
      console.warn("Failed to load view mode from localStorage:", error);
    } finally {
      setIsInitialized(true);
    }
  }, [storageKey]);

  // Lắng nghe thay đổi localStorage từ các tab/page khác
  useEffect(() => {
    if (!syncAcrossPages || typeof window === "undefined") return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === storageKey && e.newValue && isValidViewMode(e.newValue)) {
        setViewModeState(e.newValue as ViewMode);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [storageKey, syncAcrossPages]);

  // Function để set view mode và lưu vào localStorage
  const setViewMode = (mode: ViewMode | string) => {
    if (!isValidViewMode(mode)) {
      console.warn(`Invalid view mode: ${mode}`);
      return;
    }

    const validMode = mode as ViewMode;
    setViewModeState(validMode);

    // Lưu vào localStorage
    try {
      localStorage.setItem(storageKey, validMode);

      // Dispatch custom event để thông báo cho các component khác
      if (syncAcrossPages) {
        window.dispatchEvent(new CustomEvent("viewModeChange", {
          detail: { mode: validMode, storageKey }
        }));
      }
    } catch (error) {
      console.warn("Failed to save view mode to localStorage:", error);
    }
  };

  // Function để toggle giữa các view mode
  const toggleViewMode = () => {
    const modes: ViewMode[] = ["table", "grid"];
    const currentIndex = modes.indexOf(viewMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setViewMode(modes[nextIndex]);
  };

  return {
    viewMode: isInitialized ? viewMode : defaultMode,
    setViewMode,
    toggleViewMode
  };
};

// Helper function để validate view mode
const isValidViewMode = (mode: string): mode is ViewMode => {
  return ["table", "grid", "list", "card"].includes(mode);
};

// Hook cho các page cụ thể với storage key riêng
export const useDevicesViewMode = () => useViewMode({
  defaultMode: "grid",
  storageKey: "devices-view-mode"
});

export const useUsersViewMode = () => useViewMode({
  defaultMode: "table",
  storageKey: "users-view-mode"
});

export const useOrganizationsViewMode = () => useViewMode({
  defaultMode: "table",
  storageKey: "organizations-view-mode"
});

export const usePolicyViewMode = () => useViewMode({
  defaultMode: "table",
  storageKey: "policy-view-mode"
});

export const useFirmwareViewMode = () => useViewMode({
  defaultMode: "table",
  storageKey: "firmware-view-mode"
});

export const useCodePushViewMode = () => useViewMode({
  defaultMode: "table",
  storageKey: "code-push-view-mode"
});

export const useDevicesManagerViewMode = () => useViewMode({
  defaultMode: "grid",
  storageKey: "devices-manager-view-mode"
});

// Hook chung cho tất cả các page (sync across pages)
export const useGlobalViewMode = () => useViewMode({
  defaultMode: "table",
  storageKey: "global-view-mode",
  syncAcrossPages: true
});
