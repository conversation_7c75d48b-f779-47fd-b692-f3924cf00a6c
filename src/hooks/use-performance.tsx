import { useCallback, useMemo, useRef } from 'react';
import { debounce } from 'lodash';

/**
 * Hook for performance optimizations
 */
export function usePerformance() {
  // Debounced search
  const useDebouncedSearch = (callback: (value: string) => void, delay = 300) => {
    const debouncedCallback = useRef(debounce(callback, delay)).current;
    
    return useCallback((value: string) => {
      debouncedCallback(value);
    }, [debouncedCallback]);
  };

  // Memoized filter
  const useMemoizedFilter = <T,>(
    items: T[],
    filterFn: (item: T) => boolean,
    dependencies: any[]
  ) => {
    return useMemo(() => {
      return items.filter(filterFn);
    }, [items, ...dependencies]);
  };

  // Virtualized list helper
  const useVirtualization = (
    itemCount: number,
    itemHeight: number,
    containerHeight: number
  ) => {
    return useMemo(() => {
      const visibleCount = Math.ceil(containerHeight / itemHeight);
      const bufferSize = Math.min(5, Math.floor(visibleCount / 2));
      
      return {
        visibleCount,
        bufferSize,
        totalHeight: itemCount * itemHeight
      };
    }, [itemCount, itemHeight, containerHeight]);
  };

  // Intersection Observer for lazy loading
  const useIntersectionObserver = (
    callback: (entries: IntersectionObserverEntry[]) => void,
    options?: IntersectionObserverInit
  ) => {
    const observer = useRef<IntersectionObserver | null>(null);
    
    const observe = useCallback((element: Element) => {
      if (observer.current) observer.current.disconnect();
      
      observer.current = new IntersectionObserver(callback, {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      });
      
      observer.current.observe(element);
    }, [callback, options]);

    const disconnect = useCallback(() => {
      if (observer.current) {
        observer.current.disconnect();
        observer.current = null;
      }
    }, []);

    return { observe, disconnect };
  };

  return {
    useDebouncedSearch,
    useMemoizedFilter,
    useVirtualization,
    useIntersectionObserver
  };
}

/**
 * Hook for optimized device filtering
 */
export function useOptimizedDeviceFilter<T extends { deviceType: string; status: string }>(
  devices: T[],
  searchTerm: string,
  selectedTypes: string[],
  statusFilter: string
) {
  return useMemo(() => {
    return devices.filter(device => {
      const matchesSearch = !searchTerm || 
        device.deviceType.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = selectedTypes.length === 0 || 
        selectedTypes.includes(device.deviceType);
      
      const matchesStatus = !statusFilter || 
        device.status === statusFilter;

      return matchesSearch && matchesType && matchesStatus;
    });
  }, [devices, searchTerm, selectedTypes, statusFilter]);
}

/**
 * Hook for optimized pagination
 */
export function useOptimizedPagination<T>(
  items: T[],
  itemsPerPage: number,
  currentPage: number
) {
  return useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedItems = items.slice(startIndex, endIndex);
    const totalPages = Math.ceil(items.length / itemsPerPage);
    
    return {
      items: paginatedItems,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
      totalItems: items.length
    };
  }, [items, itemsPerPage, currentPage]);
}
