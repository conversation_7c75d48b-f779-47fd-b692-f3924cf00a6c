import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { cookies } from "next/headers";

// Kiểm tra có đang chạy trên localhost không
const isLocalhost = process.env.NODE_ENV !== "production";

// Cấu hình để xóa cookie an toàn (hỗ trợ tốt trên <PERSON>)
const COOKIE_CLEAR_CONFIG = {
  httpOnly: true,
  secure: true, // Chỉ bật Secure khi không phải localhost
  sameSite: "strict" as const, // Giúp Safari không chặn xóa cookie
  path: "/",
  expires: new Date(0), // Đặt thời gian hết hạn về quá khứ để xóa cookie
  maxAge: 0, // Đảm bảo cookie hết hạn ngay lập tức
};

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get("sessionToken");
    // console.log('sessionToken: ', sessionToken)

    // Kiểm tra nếu user đã đăng nhập
    // if (sessionToken === undefined) {
    //   return NextResponse.json(
    //     { error: "No active session found" },
    //     { status: 401 }
    //   );
    // }

    // Xóa cookie an toàn
    cookieStore.set("sessionToken", "", COOKIE_CLEAR_CONFIG);
    cookieStore.set("refreshToken", "", COOKIE_CLEAR_CONFIG);
    cookieStore.set("user", "", COOKIE_CLEAR_CONFIG);

    return NextResponse.json(
      { message: "Logout successful" },
      { status: 200 }
    );
  } catch (error: any) {
    console.log("Logout error:", error);
    return NextResponse.json(
      {
        error: "An unexpected error occurred during logout",
        details:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      },
      { status: 500 }
    );
  }
}
