import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { authBody, AuthBodyType } from "@/schemaValidations/auth.schema";
import { z } from "zod";
import { USER_COOKIE_MAX_AGE } from "@/lib/cookie";
import { cookies } from "next/headers";

// Types for API responses
interface CookieConfig {
  httpOnly: boolean;
  secure: boolean;
  sameSite: "strict" | "lax" | "none";
  path: string;
  expires?: Date;
}

// Xác định có đang chạy trên localhost không
const isLocalhost = process.env.NODE_ENV !== "production";

// Cấu hình cookie (Hỗ trợ tốt Safari)
const COOKIE_CONFIG: CookieConfig = {
  httpOnly: true,
  secure: !isLocalhost, // Không cần Secure trên localhost
  sameSite: "strict",
  path: "/",
  expires: new Date(Date.now() + USER_COOKIE_MAX_AGE),
};

const COOKIE_CONFIG_REFRESH: CookieConfig = {
  httpOnly: true,
  secure: true,
  sameSite: "strict",
  path: "/",
};

// Helper function để set cookie an toàn
const setSecureCookies = async (data: AuthBodyType, sessionToken: string) => {
  const cookieStore = await cookies();

  // Set sessionToken cookie
  cookieStore.set("sessionToken", sessionToken, COOKIE_CONFIG);

  // Set refreshToken cookie
  cookieStore.set("refreshToken", data.refreshToken, COOKIE_CONFIG_REFRESH);

  // Set user data cookie
  cookieStore.set("user", JSON.stringify(data), COOKIE_CONFIG);
};

export async function POST(request: NextRequest) {
  try {
    // Lấy request body
    const rawBody = await request.json();
    const { tokenExpires, sessionToken } = rawBody;

    if (!sessionToken) {
      return NextResponse.json({ error: "Missing session token" }, { status: 400 });
    }

    COOKIE_CONFIG.expires = new Date(tokenExpires);

    // Kiểm tra nếu user đã đăng nhập
    const cookieStore = await cookies();
    // if (cookieStore.get("sessionToken")) {
    //   return NextResponse.json({ error: "User already logged in" }, { status: 409 });
    // }

    // Set cookies an toàn
    setSecureCookies(rawBody, sessionToken);

    return NextResponse.json({ data: rawBody, message: "Login successful" }, { status: 200 });

    // return NextResponse.json({ data: rawBody, message: "Login successful" }, { status: 200 });
  } catch (error: any) {
    console.log("Unexpected error during login:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred", details: process.env.NODE_ENV === "development" ? error.message : null },
      { status: 500 }
    );
  }
}
