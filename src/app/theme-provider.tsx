"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { Theme } from "@/types/theme";
// import { cookies } from "next/headers";

export function ThemeProvider({
  children,
  ...props
}: React.ComponentProps<typeof NextThemesProvider>) {
  // const cookieStore = await cookies();
  return (
    <NextThemesProvider {...props}>
        {children}
    </NextThemesProvider>
  );
}
