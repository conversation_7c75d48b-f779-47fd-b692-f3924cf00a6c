"use client";

import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { motion } from "@/components/client";
import { LoginBodyType, LoginResType } from "@/schemaValidations/auth.schema";
import { useToast } from "@/hooks/use-toast";
import { ToastAction } from "@/components/ui/toast";
import { clientUserInfo, clientSessionToken } from "@/lib/http";
import authApiRequests from "@/apiRequests/user/auth";
import { Eye, EyeOff, CheckCircle, XCircle } from "lucide-react";
import { validateEmail, validatePassword } from "@/common/validation";
import { syncJWTWithUserInfo } from "@/lib/jwt";

interface Particle {
  x: number;
  y: number;
  size: number;
  originX: number;
  originY: number;
  color: string;
  vx: number;
  vy: number;
  angle: number;
  speed: number;
  oscillationRadius: number;
}

interface CircuitNodeProps {
  className?: string;
}

interface CircuitLineProps {
  className?: string;
}

const CircuitNode: React.FC<CircuitNodeProps> = ({ className = "" }) => (
  <div
    className={`absolute w-2 h-2 bg-indigo-500 rounded-full shadow-lg shadow-indigo-500/50 ${className}`}
  />
);

const CircuitLine: React.FC<CircuitLineProps> = ({ className = "" }) => (
  <div
    className={`absolute bg-gradient-to-r from-indigo-500/50 to-blue-500/50 ${className}`}
  />
);

const Login: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();
  const particlesRef = useRef<Particle[]>([]);
  const mouseRef = useRef({ x: 0, y: 0, radius: 150 });
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const [showPassword, setShowPassword] = useState(false); // New state for password visibility

  // Handle mouse movement
  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      });
    }
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const createParticle = (width: number, height: number): Particle => {
      const x = Math.random() * width;
      const y = Math.random() * height;
      return {
        x,
        y,
        originX: x,
        originY: y,
        size: Math.random() * 2 + 1,
        color: `rgba(99, 102, 241, ${Math.random() * 0.5 + 0.2})`,
        vx: 0,
        vy: 0,
        angle: Math.random() * Math.PI * 2,
        speed: Math.random() * 0.5 + 0.2,
        oscillationRadius: Math.random() * 50 + 20,
      };
    };

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      particlesRef.current = Array.from({ length: 50 }, () =>
        createParticle(canvas.width, canvas.height)
      );
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    const detectCollision = (p1: Particle, p2: Particle) => {
      const dx = p1.x - p2.x;
      const dy = p1.y - p2.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      return distance < p1.size * 4; // Ngưỡng va chạm
    };

    const animate = () => {
      ctx.fillStyle = "rgba(15, 23, 42, 0.1)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      particlesRef.current.forEach((particle, index) => {
        particle.angle += particle.speed * 0.02;
        const autonomousX =
          particle.originX +
          Math.cos(particle.angle) * particle.oscillationRadius;
        const autonomousY =
          particle.originY +
          Math.sin(particle.angle) * particle.oscillationRadius;

        const dx = autonomousX - particle.x;
        const dy = autonomousY - particle.y;
        particle.vx = (particle.vx + dx * 0.05) * 0.95;
        particle.vy = (particle.vy + dy * 0.05) * 0.95;

        particle.x += particle.vx;
        particle.y += particle.vy;

        // Kiểm tra va chạm với các particle khác
        particlesRef.current.forEach((other, otherIndex) => {
          if (index !== otherIndex && detectCollision(particle, other)) {
            // Đổi hướng vận tốc
            const tempVx = particle.vx;
            const tempVy = particle.vy;
            particle.vx = other.vx;
            particle.vy = other.vy;
            other.vx = tempVx;
            other.vy = tempVy;

            // Hiệu ứng màu khi va chạm
            particle.color = `rgba(255, 99, 71, ${Math.random() * 0.5 + 0.5})`;
            other.color = `rgba(255, 99, 71, ${Math.random() * 0.5 + 0.5})`;
          }
        });

        // Vẽ hạt
        ctx.beginPath();
        const gradient = ctx.createRadialGradient(
          particle.x,
          particle.y,
          0,
          particle.x,
          particle.y,
          particle.size * 2
        );
        gradient.addColorStop(0, particle.color);
        gradient.addColorStop(1, "rgba(99, 102, 241, 0)");
        ctx.fillStyle = gradient;
        ctx.arc(particle.x, particle.y, particle.size * 2, 0, Math.PI * 2);
        ctx.fill();
      });

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener("resize", handleResize);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  const navigate = useRouter();
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [emailError, setEmailError] = React.useState("");
  const [passwordError, setPasswordError] = React.useState("");
  const [isEmailValid, setIsEmailValid] = React.useState(false);
  const [isPasswordValid, setIsPasswordValid] = React.useState(false);
  const [emailTouched, setEmailTouched] = React.useState(false);
  const [passwordTouched, setPasswordTouched] = React.useState(false);
  const { toast } = useToast();

  // Validation helper functions
  const validateEmailInput = (email: string) => {
    if (!email.trim()) {
      setEmailError("Email is required");
      setIsEmailValid(false);
      return false;
    }
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email address");
      setIsEmailValid(false);
      return false;
    }
    setEmailError("");
    setIsEmailValid(true);
    return true;
  };

  const validatePasswordInput = (password: string) => {
    if (!password.trim()) {
      setPasswordError("Password is required");
      setIsPasswordValid(false);
      return false;
    }
    if (!validatePassword(password)) {
      setPasswordError("Password must be at least 8 characters with uppercase, lowercase, and number");
      setIsPasswordValid(false);
      return false;
    }
    setPasswordError("");
    setIsPasswordValid(true);
    return true;
  };

  const validateForm = () => {
    const emailValid = validateEmailInput(email);
    const passwordValid = validatePasswordInput(password);
    return emailValid && passwordValid;
  };

  const login = async (value: LoginBodyType) => {
    try {
      // Validate form before submitting
      if (!validateForm()) {
        setEmailTouched(true);
        setPasswordTouched(true);
        return;
      }

      const result = await authApiRequests.login(value);
      const { token, refreshToken, tokenExpires, user } =
        result.payload as LoginResType;
      if (result.status === 200) {
        clientSessionToken.value = token;
        clientSessionToken.refreshTokenValue = refreshToken;
        const resultFromNextServer: any = await authApiRequests.auth({
          sessionToken: token,
          refreshToken,
          tokenExpires,
          user,
        });
        if (resultFromNextServer.status === 200) {
          // Sync JWT data with existing user info structure
          const syncedUserData = syncJWTWithUserInfo(token, resultFromNextServer.payload.data);
          clientUserInfo.value = syncedUserData;

          toast({
            title: "Login success",
            description: "Welcome back.",
            action: (
              <ToastAction altText="Try again" className="bg-green-500">
                {resultFromNextServer.status}
              </ToastAction>
            ),
          });
          // await authApiRequests.reload();
          navigate.push("/");
        }
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Login fail",
        description: error.message || "Something went wrong.",
        action: <ToastAction altText="Try again">{error.status}</ToastAction>,
      });
    }
  };

  return (
    <div className="fixed inset-0 w-screen h-screen bg-slate-900 overflow-hidden">
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" />

      <CircuitLine className="h-0.5 w-32 top-20 left-10 animate-pulse" />
      <CircuitLine className="h-0.5 w-48 bottom-40 right-10 animate-pulse delay-300" />
      <CircuitLine className="w-0.5 h-32 top-40 left-60 animate-pulse delay-500" />
      <CircuitNode className="top-20 left-40 animate-ping" />
      <CircuitNode className="bottom-40 right-56 animate-ping delay-700" />
      <CircuitNode className="top-96 right-32 animate-ping delay-1000" />

      <div className="relative flex items-center justify-center min-h-screen px-4">
        <motion.div
          ref={containerRef}
          onMouseMove={handleMouseMove}
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="w-full max-w-md relative"
        >
          {/* Background gradient effect */}
          <motion.div
            animate={{
              background: `radial-gradient(800px circle at ${mousePosition.x}px ${mousePosition.y}px, 
                      rgba(129, 140, 248, 0.15), 
                      rgba(99, 102, 241, 0.1) 20%,
                      rgba(79, 70, 229, 0.05) 30%,
                      transparent 50%)`,
              opacity: isHovering ? 1 : 0,
            }}
            transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
            className="absolute inset-0 rounded-2xl"
            style={{
              zIndex: 1,
              pointerEvents: "none",
            }}
          />

          {/* Border glow effect */}
          <motion.div
            animate={{
              boxShadow: isHovering
                ? "0 0 20px rgba(99, 102, 241, 0.3), inset 0 0 20px rgba(99, 102, 241, 0.2)"
                : "0 0 0px rgba(99, 102, 241, 0)",
            }}
            transition={{ duration: 0.3 }}
            className="absolute inset-0 rounded-2xl"
            style={{ pointerEvents: "none" }}
          />

          <div className="backdrop-blur-xl bg-white/10 rounded-2xl p-8 shadow-2xl shadow-indigo-500/10 relative z-10">
            <div className="relative">
              {/* Decorative corners with animation */}
              <motion.div
                animate={{
                  borderColor: isHovering
                    ? "rgba(99, 102, 241, 0.8)"
                    : "rgba(99, 102, 241, 0.5)",
                }}
                className="absolute -top-4 -left-4 w-8 h-8 border-l-2 border-t-2 rounded-tl-lg"
              />
              <motion.div
                animate={{
                  borderColor: isHovering
                    ? "rgba(99, 102, 241, 0.8)"
                    : "rgba(99, 102, 241, 0.5)",
                }}
                className="absolute -bottom-4 -right-4 w-8 h-8 border-r-2 border-b-2 rounded-br-lg"
              />

              <div className="text-center mb-8 relative">
                <motion.h1
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                  className="text-3xl font-bold bg-gradient-to-r from-indigo-400 to-blue-400 bg-clip-text text-transparent"
                >
                  System Login
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-slate-400 mt-2"
                >
                  Access Admin Workspace
                </motion.p>
              </div>

              <motion.form
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.7 }}
                className="space-y-6"
                onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
                  e.preventDefault();
                }}
              >
                <div className="space-y-2">
                  <label className="text-slate-300 block text-sm">
                    Email Address
                  </label>
                  <div className="relative">
                    <input
                      value={email}
                      onChange={(e) => {
                        setEmail(e.target.value);
                        if (emailTouched) {
                          validateEmailInput(e.target.value);
                        }
                      }}
                      onBlur={() => {
                        setEmailTouched(true);
                        validateEmailInput(email);
                      }}
                      type="email"
                      className={`w-full px-4 py-3 pr-12 rounded-lg bg-slate-800/50 border text-white 
                                     focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300
                                     placeholder-slate-500 hover:bg-slate-800/70 ${
                                       emailTouched
                                         ? isEmailValid
                                           ? "border-green-500 focus:border-green-500"
                                           : "border-red-500 focus:border-red-500"
                                         : "border-slate-700 focus:border-indigo-500 hover:border-indigo-400"
                                     }`}
                      placeholder="Enter your email address"
                    />
                    <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
                      {emailTouched && (
                        <>
                          {isEmailValid ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </>
                      )}
                      <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse" />
                    </div>
                  </div>
                  {emailTouched && emailError && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-red-400 text-sm mt-1"
                    >
                      {emailError}
                    </motion.p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-slate-300 block text-sm">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      value={password}
                      onChange={(e) => {
                        setPassword(e.target.value);
                        if (passwordTouched) {
                          validatePasswordInput(e.target.value);
                        }
                      }}
                      onBlur={() => {
                        setPasswordTouched(true);
                        validatePasswordInput(password);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          login({ email, password });
                        }
                      }}
                      type={showPassword ? "text" : "password"}
                      className={`w-full px-4 py-3 pr-20 rounded-lg bg-slate-800/50 border text-white
                                  focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300
                                  placeholder-slate-500 hover:bg-slate-800/70 ${
                                    passwordTouched
                                      ? isPasswordValid
                                        ? "border-green-500 focus:border-green-500"
                                        : "border-red-500 focus:border-red-500"
                                      : "border-slate-700 focus:border-indigo-500 hover:border-indigo-400"
                                  }`}
                      placeholder="Enter your password"
                    />
                    <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
                      {passwordTouched && (
                        <>
                          {isPasswordValid ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-500" />
                          )}
                        </>
                      )}
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-slate-400 hover:text-slate-300 focus:outline-none ml-2"
                      >
                        {showPassword ? (
                          <EyeOff className="w-5 h-5" />
                        ) : (
                          <Eye className="w-5 h-5" />
                        )}
                      </button>
                      <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse" />
                    </div>
                  </div>
                  {passwordTouched && passwordError && (
                    <motion.p
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-red-400 text-sm mt-1"
                    >
                      {passwordError}
                    </motion.p>
                  )}
                </div>

                <motion.button
                  type="submit"
                  onClick={() => login({ email, password })}
                  whileHover={{ scale: isEmailValid && isPasswordValid ? 1.02 : 1 }}
                  whileTap={{ scale: isEmailValid && isPasswordValid ? 0.98 : 1 }}
                  disabled={!isEmailValid || !isPasswordValid}
                  className={`w-full relative overflow-hidden font-medium py-3 px-4 rounded-lg transition-all duration-300
                             focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 ${
                               isEmailValid && isPasswordValid
                                 ? "bg-gradient-to-r from-indigo-600 to-blue-500 text-white hover:from-indigo-500 hover:to-blue-400 focus:ring-indigo-500 cursor-pointer"
                                 : "bg-slate-700 text-slate-400 cursor-not-allowed focus:ring-slate-500"
                             }`}
                >
                  <span className="relative z-10">
                    {isEmailValid && isPasswordValid ? "Login to System" : "Please complete the form"}
                  </span>
                  {isEmailValid && isPasswordValid && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-blue-500/20"
                      animate={{
                        opacity: isHovering ? 1 : 0,
                      }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </motion.button>

                {/* Additional links */}
                <div className="mt-6 flex items-center justify-between">
                  <button
                    className="text-sm text-slate-400 hover:text-indigo-400 transition-colors"
                    onClick={() => navigate.push("/forgot-password")}
                  >
                    Forgot password?
                  </button>
                  <button
                    className="text-sm text-slate-400 hover:text-indigo-400 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate.push("/register");
                    }}
                  >
                    Create account
                  </button>
                </div>
              </motion.form>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Login;
