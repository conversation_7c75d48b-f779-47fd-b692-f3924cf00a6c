"use client";

import React, { useEffect, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { motion } from "@/components/client";
import { useToast } from "@/hooks/use-toast";
import { ToastAction } from "@/components/ui/toast";
import authApiRequests from "@/apiRequests/user/auth";
import { Eye, EyeOff, CheckCircle, XCircle } from "lucide-react";
import { validatePassword } from "@/common/validation";

// Reuse the same particle interface
interface Particle {
  x: number;
  y: number;
  size: number;
  originX: number;
  originY: number;
  color: string;
  vx: number;
  vy: number;
  angle: number;
  speed: number;
  oscillationRadius: number;
}

const VerifyAccount = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();
  const particlesRef = useRef<Particle[]>([]);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  // Form state
  const navigate = useRouter();
  const [type, setType] = useState<string>("");
  const [titleButton, setTitleButton] = useState<string>("");
  const [verificationCode, setVerificationCode] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Validation state
  const [passwordError, setPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [isPasswordValid, setIsPasswordValid] = useState(false);
  const [isConfirmPasswordValid, setIsConfirmPasswordValid] = useState(false);
  const [passwordTouched, setPasswordTouched] = useState(false);
  const [confirmPasswordTouched, setConfirmPasswordTouched] = useState(false);
  
  const { toast } = useToast();
  const searchParams = useSearchParams();

  // handle hash params
  useEffect(() => {
    const hashParam = searchParams.get("hash");
    const typeParam = searchParams.get("type");
    if (typeParam === "register") {
      setType("register");
      setTitleButton("Verify Account");
    }
    if (typeParam === "forgot") {
      setType("forgot");
      setTitleButton("Reset Password");
    }
    if (typeParam === "force") {
      setType("force");
      setTitleButton("Change Password");
    }
    if (hashParam) {
      setVerificationCode(hashParam);
      const params = new URLSearchParams(window.location.search);
      params.delete("hash");
      window.history.replaceState({}, "", `?${params.toString()}`);
    }
  }, [searchParams]);

  // Handle mouse movement
  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      });
    }
  };

  // Reuse the same particle animation effect
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const createParticle = (width: number, height: number): Particle => {
      const x = Math.random() * width;
      const y = Math.random() * height;
      return {
        x,
        y,
        originX: x,
        originY: y,
        size: Math.random() * 2 + 1,
        color: `rgba(99, 102, 241, ${Math.random() * 0.5 + 0.2})`,
        vx: 0,
        vy: 0,
        angle: Math.random() * Math.PI * 2,
        speed: Math.random() * 0.5 + 0.2,
        oscillationRadius: Math.random() * 50 + 20,
      };
    };

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      particlesRef.current = Array.from({ length: 50 }, () =>
        createParticle(canvas.width, canvas.height)
      );
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    const animate = () => {
      ctx.fillStyle = "rgba(15, 23, 42, 0.1)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      particlesRef.current.forEach((particle) => {
        particle.angle += particle.speed * 0.02;
        const autonomousX =
          particle.originX +
          Math.cos(particle.angle) * particle.oscillationRadius;
        const autonomousY =
          particle.originY +
          Math.sin(particle.angle) * particle.oscillationRadius;

        const dx = autonomousX - particle.x;
        const dy = autonomousY - particle.y;
        particle.vx = (particle.vx + dx * 0.05) * 0.95;
        particle.vy = (particle.vy + dy * 0.05) * 0.95;

        particle.x += particle.vx;
        particle.y += particle.vy;

        ctx.beginPath();
        const gradient = ctx.createRadialGradient(
          particle.x,
          particle.y,
          0,
          particle.x,
          particle.y,
          particle.size * 2
        );
        gradient.addColorStop(0, particle.color);
        gradient.addColorStop(1, "rgba(99, 102, 241, 0)");
        ctx.fillStyle = gradient;
        ctx.arc(particle.x, particle.y, particle.size * 2, 0, Math.PI * 2);
        ctx.fill();
      });

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener("resize", handleResize);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  // Validation helper functions
  const validatePasswordInput = (password: string) => {
    if (!password.trim()) {
      setPasswordError("Password is required");
      setIsPasswordValid(false);
      return false;
    }
    if (!validatePassword(password)) {
      setPasswordError("Password must be at least 8 characters with uppercase, lowercase, and number");
      setIsPasswordValid(false);
      return false;
    }
    setPasswordError("");
    setIsPasswordValid(true);
    return true;
  };

  const validateConfirmPasswordInput = (confirmPassword: string) => {
    if (!confirmPassword.trim()) {
      setConfirmPasswordError("Please confirm your password");
      setIsConfirmPasswordValid(false);
      return false;
    }
    if (password !== confirmPassword) {
      setConfirmPasswordError("Passwords do not match");
      setIsConfirmPasswordValid(false);
      return false;
    }
    setConfirmPasswordError("");
    setIsConfirmPasswordValid(true);
    return true;
  };

  const validateForm = () => {
    const passwordValid = validatePasswordInput(password);
    const confirmPasswordValid = validateConfirmPasswordInput(confirmPassword);
    return passwordValid && confirmPasswordValid;
  };

  const verifyAccount = async () => {
    // Validate form before submitting
    if (!validateForm()) {
      setPasswordTouched(true);
      setConfirmPasswordTouched(true);
      return;
    }

    try {
      let result;

      switch (type) {
        case "register":
          result = await authApiRequests.verifyAccount({
            hash: verificationCode,
            password,
          });
          break;

        case "forgot":
          result = await authApiRequests.resetPassword({
            hash: verificationCode,
            password,
          });
          break;

        case "force":
          result = await authApiRequests.forcePassword({
            hash: verificationCode,
            password,
          });
          break;

        default:
          throw new Error("Invalid verification type");
      }

      if (result.status === 204) {
        toast({
          title: "Success",
          description: "Your account has been verified.",
          action: (
            <ToastAction altText="Success" className="bg-green-500">
              Success
            </ToastAction>
          ),
        });

        navigate.push("/login");
      } else {
        throw new Error("Verification failed");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Verification failed",
        description: error.message || "Please check your information and try again.",
        action: <ToastAction altText="Try again">Error</ToastAction>,
      });
    }
  };

  return (
    <div className="fixed inset-0 w-screen h-screen bg-slate-900 overflow-hidden">
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full" />

      <div className="relative flex items-center justify-center min-h-screen px-4">
        <motion.div
          ref={containerRef}
          onMouseMove={handleMouseMove}
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="w-full max-w-md relative"
        >
          <motion.div
            animate={{
              background: `radial-gradient(800px circle at ${mousePosition.x}px ${mousePosition.y}px, 
                      rgba(129, 140, 248, 0.15), 
                      rgba(99, 102, 241, 0.1) 20%,
                      rgba(79, 70, 229, 0.05) 30%,
                      transparent 50%)`,
              opacity: isHovering ? 1 : 0,
            }}
            transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
            className="absolute inset-0 rounded-2xl"
            style={{
              zIndex: 1,
              pointerEvents: "none",
            }}
          />

          <div className="backdrop-blur-xl bg-white/10 rounded-2xl p-8 shadow-2xl shadow-indigo-500/10 relative z-10">
            <div className="text-center mb-8">
              <motion.h1
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-3xl font-bold bg-gradient-to-r from-indigo-400 to-blue-400 bg-clip-text text-transparent"
              >
                Verify Your Account
              </motion.h1>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="text-slate-400 mt-2"
              >
                Enter your password to verify your account
              </motion.p>
            </div>

            <motion.form
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="space-y-4"
              onSubmit={(e) => {
                e.preventDefault();
                verifyAccount();
              }}
            >
              {/* Password Field */}
              <div className="space-y-2">
                <label className="text-slate-300 block text-sm">
                  New Password
                </label>
                <div className="relative">
                  <input
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      if (passwordTouched) {
                        validatePasswordInput(e.target.value);
                      }
                      // Also revalidate confirm password if it was touched
                      if (confirmPasswordTouched && confirmPassword) {
                        validateConfirmPasswordInput(confirmPassword);
                      }
                    }}
                    onBlur={() => {
                      setPasswordTouched(true);
                      validatePasswordInput(password);
                    }}
                    type={showPassword ? "text" : "password"}
                    className={`w-full px-4 py-3 pr-20 rounded-lg bg-slate-800/50 border text-white
                               focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300
                               placeholder-slate-500 hover:bg-slate-800/70 ${
                                 passwordTouched
                                   ? isPasswordValid
                                     ? "border-green-500 focus:border-green-500"
                                     : "border-red-500 focus:border-red-500"
                                   : "border-slate-700 focus:border-indigo-500 hover:border-indigo-400"
                               }`}
                    placeholder="Create new password"
                  />
                  <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
                    {passwordTouched && (
                      <>
                        {isPasswordValid ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                      </>
                    )}
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-slate-400 hover:text-slate-300 focus:outline-none ml-2"
                    >
                      {showPassword ? (
                        <EyeOff className="w-5 h-5" />
                      ) : (
                        <Eye className="w-5 h-5" />
                      )}
                    </button>
                    <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse" />
                  </div>
                </div>
                {passwordTouched && passwordError && (
                  <motion.p
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-400 text-sm mt-1"
                  >
                    {passwordError}
                  </motion.p>
                )}
              </div>

              {/* Confirm Password Field */}
              <div className="space-y-2">
                <label className="text-slate-300 block text-sm">
                  Confirm Password
                </label>
                <div className="relative">
                  <input
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value);
                      if (confirmPasswordTouched) {
                        validateConfirmPasswordInput(e.target.value);
                      }
                    }}
                    onBlur={() => {
                      setConfirmPasswordTouched(true);
                      validateConfirmPasswordInput(confirmPassword);
                    }}
                    type={showConfirmPassword ? "text" : "password"}
                    className={`w-full px-4 py-3 pr-20 rounded-lg bg-slate-800/50 border text-white
                               focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300
                               placeholder-slate-500 hover:bg-slate-800/70 ${
                                 confirmPasswordTouched
                                   ? isConfirmPasswordValid
                                     ? "border-green-500 focus:border-green-500"
                                     : "border-red-500 focus:border-red-500"
                                   : "border-slate-700 focus:border-indigo-500 hover:border-indigo-400"
                               }`}
                    placeholder="Confirm new password"
                  />
                  <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-1">
                    {confirmPasswordTouched && (
                      <>
                        {isConfirmPasswordValid ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                      </>
                    )}
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="text-slate-400 hover:text-slate-300 focus:outline-none ml-2"
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="w-5 h-5" />
                      ) : (
                        <Eye className="w-5 h-5" />
                      )}
                    </button>
                    <div className="w-2 h-2 bg-indigo-500 rounded-full animate-pulse" />
                  </div>
                </div>
                {confirmPasswordTouched && confirmPasswordError && (
                  <motion.p
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-red-400 text-sm mt-1"
                  >
                    {confirmPasswordError}
                  </motion.p>
                )}
              </div>

              {/* Verify Button */}
              <motion.button
                type="submit"
                whileHover={{ scale: isPasswordValid && isConfirmPasswordValid ? 1.02 : 1 }}
                whileTap={{ scale: isPasswordValid && isConfirmPasswordValid ? 0.98 : 1 }}
                disabled={!isPasswordValid || !isConfirmPasswordValid}
                className={`w-full relative overflow-hidden font-medium py-3 px-4 rounded-lg transition-all duration-300
                           focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900 ${
                             isPasswordValid && isConfirmPasswordValid
                               ? "bg-gradient-to-r from-indigo-600 to-blue-500 text-white hover:from-indigo-500 hover:to-blue-400 focus:ring-indigo-500 cursor-pointer"
                               : "bg-slate-700 text-slate-400 cursor-not-allowed focus:ring-slate-500"
                           }`}
              >
                <span className="relative z-10">
                  {isPasswordValid && isConfirmPasswordValid 
                    ? titleButton 
                    : "Please complete the form"}
                </span>
                {isPasswordValid && isConfirmPasswordValid && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-blue-500/20"
                    animate={{
                      opacity: isHovering ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                )}
              </motion.button>
            </motion.form>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default VerifyAccount;
