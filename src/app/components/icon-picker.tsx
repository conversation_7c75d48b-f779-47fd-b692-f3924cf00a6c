import React, { useState } from "react";
import * as Icons from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { icons } from "lucide-react";

interface IconPickerProps {
  onSelectIcon: (iconName: any) => void;
  selectedIcon?: string;
}

// Type cho icon names từ lucide-react
type IconName = keyof typeof icons;
function isValidIconComponent(
  componentName: string
): componentName is IconName {
  return componentName in icons;
}
export const IconRender = ({ iconName }: { iconName: string }) => {
  if (!isValidIconComponent(iconName)) {
    return iconName;
  }
  const componentName = iconName as IconName;
  const Icon = Icons[componentName];
  return <Icon className="h-4 w-4 text-primary" />;
};

export const IconPicker: React.FC<IconPickerProps> = ({
  onSelectIcon,
  selectedIcon,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");

  // Get all icon names from Lucide
  const iconNames = Object.keys(Icons).filter(
    (key): key is IconName =>
      key !== "createReactComponent" && key !== "default"
  );

  // Filter icons based on search
  const filteredIcons = iconNames.filter((name) =>
    name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleSelect = (iconName: string) => {
    onSelectIcon(iconName);
    setOpen(false);
  };

  // Get the selected icon component
  const SelectedIcon: React.FC<{ iconName: string }> = ({ iconName }) => {
    if (!isValidIconComponent(iconName)) {
      return;
    }
    return <IconRender iconName={iconName} />;
  };

  return (
    <div className="flex flex-col gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            <div className="flex items-center gap-2">
              {SelectedIcon && SelectedIcon !== null && (
                <SelectedIcon iconName={selectedIcon || ""} />
              )}
              <span className="text-muted-foreground">{selectedIcon || "Select an icon..."}</span>
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-0">
          <Command>
            <CommandInput
              placeholder="Search icons..."
              value={searchValue}
              onValueChange={setSearchValue}
            />
            <CommandList>
              <CommandEmpty>No icons found.</CommandEmpty>
              <CommandGroup className="max-h-64 overflow-auto">
                {filteredIcons.map((iconName) => {
                  return (
                    isValidIconComponent(iconName) && (
                      <CommandItem
                        key={iconName}
                        onSelect={() =>
                          handleSelect(iconName)
                        }
                        className="flex items-center gap-2 cursor-pointer"
                      >
                        {/* <Icon className="h-4 w-4" /> */}
                        {/* <DynamicIcon name={iconName} color="red" size={48} /> */}
                        <IconRender iconName={iconName} />
                        <span>{iconName}</span>
                      </CommandItem>
                    )
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};

// Example usage component
const IconPickerDemo: React.FC = () => {
  const [selectedIcon, setSelectedIcon] = useState<any>("");

  return (
    <div className="w-full max-w-sm p-4 space-y-4">
      <h2 className="text-lg font-semibold">Icon Picker</h2>
      <IconPicker selectedIcon={selectedIcon} onSelectIcon={setSelectedIcon} />
      {selectedIcon && (
        <div className="pt-4">
          <p className="text-sm text-gray-500">Selected icon: {IconRender({ iconName: selectedIcon })}</p>
        </div>
      )}
    </div>
  );
};

export default IconPickerDemo;
