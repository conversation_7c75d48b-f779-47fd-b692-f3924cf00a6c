'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Smartphone, Download, Clock, ArrowRight, Apple } from 'lucide-react';
import { ANDROID_APP_DOWNLOAD_LINK, IOS_APP_DOWNLOAD_LINK } from "@/config/config";

const AppManagementCard: React.FC = () => {
  const qrServer = "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=";

  const platforms = [
    {
      name: "Android",
      icon: <Smartphone className="h-5 w-5" />,
      version: "3.16.283",
      buildNumber: "v930",
      buildDate: "2024-11-01",
      buildTime: "13:03",
      color: "from-green-500 to-emerald-600",
      bgColor: "from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20",
      borderColor: "border-green-200 dark:border-green-800",
      downloadUrl: ANDROID_APP_DOWNLOAD_LINK,
      qrCodeUrl: qrServer + ANDROID_APP_DOWNLOAD_LINK
    },
    {
      name: "iOS",
      icon: <Apple className="h-5 w-5" />,
      version: "3.16.20241001417",
      buildNumber: "v1032",
      buildDate: "2024-11-01",
      buildTime: "12:59",
      color: "from-blue-500 to-indigo-600",
      bgColor: "from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20",
      borderColor: "border-blue-200 dark:border-blue-800",
      downloadUrl: IOS_APP_DOWNLOAD_LINK,
      qrCodeUrl: qrServer + IOS_APP_DOWNLOAD_LINK
    }
  ];

  const stats = [
    { label: "Total Versions", value: "24" },
    { label: "Active Builds", value: "2" },
    { label: "Downloads", value: "1.2K" }
  ];

  const handleDownload = (platform: any) => {
    // Open download URL in new tab
    window.open(platform.downloadUrl, '_blank');

    // Show success toast
    toast.success(`Đang chuyển hướng đến ${platform.name} App Store...`, {
      description: `Tải xuống phiên bản ${platform.version}`,
      duration: 3000,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center">
            <div className="text-white text-lg">🏡</div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-foreground">Homicen Staging</h3>
            <p className="text-sm text-muted-foreground">Mobile App Management</p>
          </div>
        </div>
        <Badge className="bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0">
          STG
        </Badge>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-2">
        {stats.map((stat, index) => (
          <div key={index} className="text-center p-2 bg-gradient-to-br from-muted/20 to-muted/5 rounded-md border border-border/30">
            <div className="text-base font-bold text-foreground">{stat.value}</div>
            <div className="text-xs text-muted-foreground">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Platform Cards */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Latest Versions</h4>
        {platforms.map((platform, index) => (
          <div key={index} className={`p-3 rounded-lg border ${platform.borderColor} bg-gradient-to-r ${platform.bgColor}`}>
            <div className="flex items-start gap-4">
              {/* Left side - Platform info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className={`p-1.5 bg-gradient-to-r ${platform.color} rounded-lg text-white shadow-sm`}>
                      {platform.icon}
                    </div>
                    <div>
                      <div className="text-sm font-semibold text-foreground">{platform.name}</div>
                      <div className="text-xs text-muted-foreground">Latest Build</div>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs font-medium">
                    {platform.buildNumber}
                  </Badge>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">Version:</span>
                    <span className="text-xs font-medium text-foreground">{platform.version}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">Build Date:</span>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">{platform.buildDate} {platform.buildTime}</span>
                    </div>
                  </div>
                </div>

                <Button
                  size="sm"
                  className={`w-full text-xs h-8 bg-gradient-to-r ${platform.color} hover:opacity-90 text-white border-0 shadow-sm transition-all duration-200`}
                  onClick={() => handleDownload(platform)}
                >
                  <Download className="h-3 w-3 mr-1.5" />
                  Download App
                </Button>
              </div>

              {/* Right side - QR Code */}
              <div className="flex flex-col items-center gap-2">
                <div className="w-20 h-20 bg-white rounded-lg p-2 border border-gray-200 shadow-sm">
                  <img
                    src={platform.qrCodeUrl}
                    alt={`QR Code for ${platform.name}`}
                    className="w-full h-full object-contain"
                  />
                </div>
                <span className="text-xs text-muted-foreground font-medium">Scan QR</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Action Button */}
      <Button
        size="sm"
        variant="outline"
        className="w-full"
      >
        View All Versions
        <ArrowRight className="h-3 w-3 ml-1" />
      </Button>
    </div>
  );
};

export default AppManagementCard;
