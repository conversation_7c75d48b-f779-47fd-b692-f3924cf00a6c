'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Cpu, Layers, Radio, ArrowRight, Zap, Shield, Globe } from 'lucide-react';

const IoTOverviewCard: React.FC = () => {
  const stats = [
    {
      icon: <Cpu className="h-5 w-5" />,
      label: "Hardware",
      value: "12",
      description: "Custom IoT Devices",
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: <Layers className="h-5 w-5" />,
      label: "Software",
      value: "8",
      description: "Platform Services",
      color: "text-green-600 dark:text-green-400"
    },
    {
      icon: <Radio className="h-5 w-5" />,
      label: "Connectivity",
      value: "5",
      description: "Network Protocols",
      color: "text-purple-600 dark:text-purple-400"
    }
  ];

  const features = [
    {
      icon: <Zap className="h-4 w-4" />,
      title: "Real-time Monitoring",
      description: "Live device status tracking"
    },
    {
      icon: <Shield className="h-4 w-4" />,
      title: "Security First",
      description: "End-to-end encryption"
    },
    {
      icon: <Globe className="h-4 w-4" />,
      title: "Global Scale",
      description: "Worldwide deployment"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">Homicen IoT Solutions</h3>
          <p className="text-sm text-muted-foreground">Intelligent devices for tomorrow</p>
        </div>
        <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
          Active
        </Badge>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-3 gap-4">
        {stats.map((stat, index) => (
          <div key={index} className="text-center p-3 bg-gradient-to-br from-muted/30 to-muted/10 rounded-lg border border-border/50">
            <div className={`${stat.color} flex justify-center mb-2`}>
              {stat.icon}
            </div>
            <div className="text-xl font-bold text-foreground">{stat.value}</div>
            <div className="text-xs text-muted-foreground">{stat.label}</div>
            <div className="text-xs text-muted-foreground mt-1">{stat.description}</div>
          </div>
        ))}
      </div>

      {/* Features List */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Key Features</h4>
        {features.map((feature, index) => (
          <div key={index} className="flex items-start gap-3 p-2 rounded-lg hover:bg-muted/30 transition-colors">
            <div className="p-1.5 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-md text-blue-600 dark:text-blue-400 mt-0.5">
              {feature.icon}
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-foreground">{feature.title}</div>
              <div className="text-xs text-muted-foreground">{feature.description}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 pt-2">
        <Button 
          size="sm" 
          className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0"
        >
          Explore Solutions
          <ArrowRight className="h-3 w-3 ml-1" />
        </Button>
        <Button 
          size="sm" 
          variant="outline" 
          className="flex-1"
        >
          View Details
        </Button>
      </div>
    </div>
  );
};

export default IoTOverviewCard;
