import React, { useState } from "react";
// import useThemeMaterial from "@mui/material/styles/useTheme";

type ColorPickerProps = {
  onChange?: (color: string) => void; // Hàm callback khi màu thay đổi
};

const ColorPicker: React.FC<ColorPickerProps> = ({ onChange }) => {
  // const themeMaterial = useThemeMaterial();
  const mode = document.documentElement.className;
  const [color, setColor] = useState<string>(() => {
    // Lấy giá trị từ localStorage nếu tồn tại
    if (mode === "dark") {
      return localStorage.getItem("palette-primary-dark") || "#000000";
    } else return localStorage.getItem("palette-primary-light") || "#000000";
    // return localStorage.getItem("palette-primary-light") || "#000000";
  });

  // Hàm x<PERSON> lý khi chọn màu
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value;
    setColor(newColor);
    if (onChange) onChange(newColor); // Gọi callback (nếu có)
    if (mode === "dark") localStorage.setItem("palette-primary-dark", newColor);
    if (mode === "light")
      localStorage.setItem("palette-primary-light", newColor);
  };

  const handleColorClick = (value: string) => {
    const newColor = value;
    setColor(newColor);
    if (onChange) onChange(newColor); // Gọi callback (nếu có)
    if (mode === "dark") localStorage.setItem("palette-primary-dark", newColor);
    if (mode === "light")
      localStorage.setItem("palette-primary-light", newColor);
  };

  return (
    <>
    <div className="flex gap-2">
      <button className="bg-[#8c58f7] min-w-20 min-h-20 rounded-md" onClick={() => handleColorClick("#8c58f7")}></button>
      <button className="bg-[#58caf7] min-w-20 min-h-20 rounded-md" onClick={() => handleColorClick("#58caf7")}></button>
      <button className="bg-[#58f760] min-w-20 min-h-20 rounded-md" onClick={() => handleColorClick("#58f760")}></button>
      <button className="bg-[#f7dd58] min-w-20 min-h-20 rounded-md" onClick={() => handleColorClick("#f7dd58")}></button>
    </div>
    <span className="block mt-6 text-sm">Custom color</span>
      <div className="flex gap-2 items-center">
        <input
          type="color"
          value={color}
          onChange={handleColorChange}
          className="w-10 h-10 border-none cursor-pointer"
        />
        <span>{color}</span>
      </div>
    </>
  );
};

export default ColorPicker;
