"use client";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Settings,
  Users,
  Smartphone,
  Settings2,
  HardDrive,
  Package,
  Building2,
  Home,
  Activity,
  Shield,
  ChevronRight,
  LogIn,
  ChevronDown,
  Check,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import SheetSide from "@/app/components/sheet-side";
// Note: AdminModeIndicator removed - using JWT-only permissions

import { clientUserInfo, isUserAuthenticated } from "@/lib/http";
import { checkJWTPermission, RESOURCES } from "@/lib/jwt-permissions";
import { useOrganization } from "@/context/organization-context";
import { decodeJWT } from "@/lib/jwt";
import { clientSessionToken } from "@/lib/http";

type Item = {
  title: string;
  url: string;
  icon: any;
  imoji: string;
  color: string;
  description: string;
  badge?: string;
  resource?: string; // Add resource mapping for permission checking
};

const itemsAdmin: Item[] = [
  {
    title: "Dashboard",
    url: "/",
    icon: Home,
    imoji: "🏠",
    color: "from-blue-500 to-indigo-600",
    description: "Overview & Analytics",
    // Dashboard accessible to all authenticated users
  },
  {
    title: "Users",
    url: "users",
    icon: Users,
    imoji: "👥",
    color: "from-green-500 to-emerald-600",
    description: "User Management",
    resource: RESOURCES.USER,
  },
  {
    title: "Organizations",
    url: "organizations",
    icon: Building2,
    imoji: "🏢",
    color: "from-purple-500 to-violet-600",
    description: "Organization Control",
    resource: RESOURCES.ORGANIZATION,
  },
  {
    title: "Devices",
    url: "devices",
    icon: Smartphone,
    imoji: "💡",
    color: "from-orange-500 to-red-600",
    description: "IoT Device Control",
    badge: "Live",
    resource: RESOURCES.DEVICE,
  },
  {
    title: "Devices Manager",
    url: "devices-manager",
    icon: Settings2,
    imoji: "🛒",
    color: "from-cyan-500 to-blue-600",
    description: "Device Configuration",
    resource: RESOURCES.DEVICE,
  },
  {
    title: "Firmware",
    url: "firmware",
    icon: HardDrive,
    imoji: "🛠️",
    color: "from-amber-500 to-orange-600",
    description: "Firmware Updates",
    resource: RESOURCES.FIRMWARE,
  },
  {
    title: "CodePush",
    url: "code-push",
    icon: Package,
    imoji: "📦",
    color: "from-pink-500 to-rose-600",
    description: "App Deployment",
    resource: RESOURCES.CODE_PUSH,
  },
  {
    title: "Policy",
    url: "policy",
    icon: Shield,
    imoji: "📝",
    color: "from-teal-500 to-cyan-600",
    description: "Security Policies",
    resource: RESOURCES.DEVICE, // Policies are device-related
  },
  {
    title: "Settings",
    url: "settings",
    icon: Settings,
    imoji: "⚙️",
    color: "from-gray-500 to-slate-600",
    description: "Roles & Permissions Management",
    resource: RESOURCES.ROLE,
  },
];

export function AppSidebar() {
  const [open, setOpen] = useState(false);
  const [sideBarItems, setSideBarItems] = useState<Item[]>(itemsAdmin);
  const pathname = usePathname();
  const { userInfo } = clientUserInfo;
  const { isMobile } = useSidebar();
  const { 
    currentOrgId, 
    availableOrganizations, 
    switchOrganization, 
    hasMultipleOrgs,
    isLoading: orgLoading,
    canAccessAllOrgs
  } = useOrganization();
  
  // Check if user is logged in using proper authentication utility
  const isLoggedIn = isUserAuthenticated();

  // Get current organization name
  const getCurrentOrgName = (): string => {
    if (!currentOrgId) return "No Organization";
    
    const org = availableOrganizations.find(org => org.id === currentOrgId);
    if (org?.orgName) return org.orgName;

    // Try to get organization name from JWT or userInfo
    try {
      const token = clientSessionToken.value;
      if (token) {
        const payload = decodeJWT(token);
        const role = payload?.roles?.find(r => r.orgId === currentOrgId);
        if (role && userInfo?.user?.org?.orgName) {
          return userInfo.user.org.orgName;
        }
      }
    } catch (error) {
      console.log('Error getting org name:', error);
    }
    
    return `Org ${currentOrgId.slice(-8)}`;
  };

  useEffect(() => {
    // Only show navigation items if user is logged in
    if (!isLoggedIn) {
      setSideBarItems([]);
      return;
    }

    // Use itemsAdmin for all users - permissions are controlled by JWT token
    const accessibleItems = itemsAdmin.filter(item => {
      // Dashboard is always accessible to authenticated users
      if (item.url === "/" || !item.resource) {
        return true;
      }

      // Check JWT permissions for the resource (defaults to 'get' action)
      return checkJWTPermission(item.resource);
    });

    setSideBarItems(accessibleItems);
  }, [isLoggedIn, userInfo, currentOrgId]); // Add currentOrgId to dependencies

  return (
    <div>
      {/* Modern Enhanced Header */}
      <div className="fixed inset-x-0 top-0 z-50 h-16 w-full">
        {/* Header Background with Gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/98 to-background/95 backdrop-blur-xl border-b border-border/50">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5" />
        </div>

        {/* Header Content */}
        <div className="relative flex h-full items-center gap-2 sm:gap-6 px-2 sm:px-4">
          {/* Single Mobile Trigger - Only show on mobile */}
          {isMobile && (
            <SidebarTrigger className="mr-2 h-9 w-9 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 border-0 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105" />
          )}

          {/* Enhanced Logo Section */}
          <Link
            className={`flex items-center gap-2 sm:gap-3 p-2 sm:p-3 uppercase font-bold transition-all duration-300 hover:scale-105 ${isMobile ? 'text-sm' : ''}`}
            href="/"
          >
            <div className="relative">
              {/* Logo with enhanced effects */}
              <div className="relative p-1 bg-gradient-to-br from-blue-500/10 to-purple-600/10 rounded-xl backdrop-blur-sm">
                <Image
                  src={"/images/Asset4058751b.svg"}
                  alt="Logo"
                  width={isMobile ? 32 : 40}
                  height={isMobile ? 32 : 40}
                  priority
                  className="drop-shadow-lg"
                />
                {/* Animated status indicator */}
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-ping opacity-75"></div>
                </div>
              </div>
            </div>
            <div className="hidden xs:flex flex-col">
              <Image
                src={"/images/Assetaf004edd.svg"}
                alt="Logo"
                width={isMobile ? 80 : 100}
                height={isMobile ? 24 : 30}
                priority
                className="mb-1"
              />
              <div className="text-xs text-muted-foreground font-normal normal-case">
                IoT Management Platform
              </div>
            </div>
          </Link>

          {/* Organization Selector */}
          {isLoggedIn && currentOrgId && (
            <div className="flex-1 flex justify-center">
              {(hasMultipleOrgs || canAccessAllOrgs) ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className={`h-9 px-3 backdrop-blur-sm transition-all duration-300 hover:scale-105 ${
                        canAccessAllOrgs 
                          ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 hover:from-blue-100 hover:to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 dark:border-blue-700'
                          : 'bg-background/50 border-border/50 hover:bg-background/80'
                      }`}
                    >
                      <Building2 className={`h-4 w-4 mr-2 ${canAccessAllOrgs ? 'text-blue-600 dark:text-blue-400' : ''}`} />
                      <span className="hidden sm:inline">{getCurrentOrgName()}</span>
                      <span className="sm:hidden">{getCurrentOrgName().slice(0, 8)}...</span>
                      {canAccessAllOrgs && (
                        <Badge variant="secondary" className="ml-2 text-xs px-1 py-0 bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300">
                          Admin
                        </Badge>
                      )}
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="center" className="w-72">
                    {canAccessAllOrgs && (
                      <div className="px-3 py-2 text-xs text-muted-foreground border-b">
                        {availableOrganizations.length} Organizations Available (Admin Access)
                      </div>
                    )}
                    <div className="max-h-64 overflow-y-auto">
                      {availableOrganizations.map((org) => (
                        <DropdownMenuItem
                          key={org.id}
                          onClick={() => switchOrganization(org.id)}
                          className="flex items-center gap-3 cursor-pointer p-3 hover:bg-muted/50"
                        >
                          <Building2 className="h-4 w-4 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">
                              {org.orgName || `Organization ${org.id.slice(-8)}`}
                            </div>
                            {canAccessAllOrgs && (
                              <div className="text-xs text-muted-foreground truncate">
                                ID: {org.id}
                              </div>
                            )}
                          </div>
                          {currentOrgId === org.id && (
                            <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                          )}
                        </DropdownMenuItem>
                      ))}
                    </div>
                    {canAccessAllOrgs && (
                      <div className="px-3 py-2 text-xs text-muted-foreground border-t bg-muted/20">
                        💡 You have admin access to all organizations
                      </div>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <div className="flex items-center gap-2 px-3 py-2 bg-background/50 border border-border/50 rounded-lg backdrop-blur-sm">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium hidden sm:inline">{getCurrentOrgName()}</span>
                  <span className="text-sm font-medium sm:hidden">{getCurrentOrgName().slice(0, 8)}...</span>
                </div>
              )}
            </div>
          )}

          {/* Header Actions */}
          <div className="flex items-center justify-end gap-2">
            {/* Note: Admin Mode Indicator removed - using JWT-only permissions */}

            {/* Login Button or User Info */}
            {!isLoggedIn ? (
              <Link href="/login">
                <Button
                  variant="default"
                  size="sm"
                  className="h-9 px-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 border-0 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <LogIn className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Login</span>
                </Button>
              </Link>
            ) : (
              <div
                onClick={() => setOpen(!open)}
                className="relative group cursor-pointer"
              >
                <div className="w-9 h-9 sm:w-10 sm:h-10 rounded-xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex justify-center items-center text-white uppercase font-bold text-sm sm:text-base transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl group-hover:shadow-purple-500/25 ring-2 ring-transparent group-hover:ring-purple-500/20">
                  {userInfo?.user?.firstName?.split("")[0] || "U"}

                  {/* Online status indicator */}
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-background rounded-full">
                    <div className="absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75"></div>
                  </div>
                </div>

                {/* Hover tooltip */}
                <div className="absolute top-full right-0 mt-2 px-3 py-2 bg-popover text-popover-foreground text-xs rounded-lg shadow-xl border opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none whitespace-nowrap z-50">
                  <div className="font-medium">{userInfo?.user?.firstName || "User"}</div>
                  <div className="text-muted-foreground flex items-center gap-1 mt-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    Online
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Header bottom glow effect */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>

        <SheetSide open={open} />
      </div>

      {/* Modern Sidebar */}
      <Sidebar
        variant="floating"
        collapsible="icon"
        className="h-[calc(100vh-64px)] mt-16"
      >
        <SidebarContent className="glass-card custom-scrollbar bg-gradient-to-b from-background/95 to-muted/30 backdrop-blur-xl border border-border/50">
          <SidebarGroup className="bg-transparent">
            {/* Mobile Logo in Sidebar */}
            <SidebarGroupLabel className="py-6 md:hidden">
              <Link
                className="flex items-center gap-3 p-3 uppercase font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent"
                href="/"
              >
                <Image
                  src={"/images/Asset4058751b.svg"}
                  alt="Logo"
                  width={40}
                  height={40}
                  priority
                />
                <Image
                  src={"/images/Assetaf004edd.svg"}
                  alt="Logo"
                  width={80}
                  height={40}
                  priority
                />
              </Link>
            </SidebarGroupLabel>

            {/* Navigation Menu */}
            <SidebarGroupContent className="bg-transparent px-2 group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:items-center">
              {!isLoggedIn ? (
                // Login Prompt
                <div className="flex flex-col items-center justify-center py-8 px-4 text-center group-data-[collapsible=icon]:hidden">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center mb-4">
                    <LogIn className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="font-semibold text-lg text-foreground mb-2">Welcome to Homicen</h3>
                  <p className="text-sm text-muted-foreground mb-4">Please login to access your dashboard and manage your IoT devices.</p>
                  <Link href="/login">
                    <Button className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 border-0 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                      <LogIn className="w-4 h-4 mr-2" />
                      Login to Continue
                    </Button>
                  </Link>
                </div>
              ) : (
                <SidebarMenu className="space-y-2 group-data-[collapsible=icon]:space-y-3 group-data-[collapsible=icon]:w-full group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:items-center">
                  {sideBarItems.map((item, index) => {
                    const isActive = pathname.split("/")[1] === item.url || (pathname === "/" && item.url === "/");

                    return (
                      <SidebarMenuItem key={item.title} className="group-data-[collapsible=icon]:w-auto group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center">
                        <SidebarMenuButton
                          asChild
                          className={`
                            group relative overflow-hidden rounded-xl p-0 h-auto transition-all duration-300 hover:scale-[1.02] hover:shadow-lg group-data-[collapsible=icon]:w-12 group-data-[collapsible=icon]:h-12
                            ${isActive
                              ? 'bg-gradient-to-r ' + item.color + ' text-white shadow-lg shadow-black/10'
                              : 'bg-card/50 hover:bg-card/80 text-foreground border border-border/50 hover:border-border'
                            }
                          `}
                        >
                          <Link href={item.url} className="flex items-center gap-3 p-4 w-full group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:p-2">
                            {/* Icon Container */}
                            <div className={`
                              relative flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-300 flex-shrink-0
                              ${isActive
                                ? 'bg-white/20 text-white'
                                : 'bg-gradient-to-r ' + item.color + ' text-white group-hover:scale-110'
                              }
                            `}>
                              <item.icon className="w-5 h-5 flex-shrink-0" />
                            </div>

                            {/* Content */}
                            <div className="flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
                              <div className="flex items-center justify-between">
                                <span className="font-semibold text-sm truncate">{item.title}</span>
                                {item.badge && (
                                  <Badge
                                    className={`
                                      text-xs px-2 py-0.5 ml-2
                                      ${isActive
                                        ? 'bg-white/20 text-white border-white/30'
                                        : 'bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0'
                                      }
                                    `}
                                  >
                                    {item.badge}
                                  </Badge>
                                )}
                              </div>
                              <p className={`
                                text-xs mt-0.5 truncate transition-colors
                                ${isActive ? 'text-white/80' : 'text-muted-foreground'}
                              `}>
                                {item.description}
                              </p>
                            </div>

                            {/* Active Indicator */}
                            {isActive && (
                              <ChevronRight className="w-4 h-4 text-white/80 group-data-[collapsible=icon]:hidden" />
                            )}

                            {/* Hover Effect */}
                            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/5 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    );
                  })}
                </SidebarMenu>
              )}
            </SidebarGroupContent>

            {/* Sidebar Footer - Only show when logged in */}
            {isLoggedIn && (
              <div className="mt-auto p-4 group-data-[collapsible=icon]:hidden">
                <Separator className="mb-4 bg-border/50" />
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-r from-muted/30 to-muted/10 border border-border/30">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-bold">
                    {userInfo?.user?.firstName?.split("")[0] || "U"}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground truncate">
                      {userInfo?.user?.firstName || "User"}
                    </p>
                    <p className="text-xs text-muted-foreground truncate">
                      {userInfo?.user?.email || "<EMAIL>"}
                    </p>
                  </div>
                  <Activity className="w-4 h-4 text-green-500" />
                </div>
              </div>
            )}
          </SidebarGroup>
        </SidebarContent>

        {/* Desktop Trigger */}
        {!isMobile && (
          <SidebarTrigger className="mx-auto mb-2 mt-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 border-0 rounded-lg shadow-lg" />
        )}
      </Sidebar>
    </div>
  );
}
