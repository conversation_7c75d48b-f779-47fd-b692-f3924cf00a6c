'use client';

import { useSidebar } from "@/components/ui/sidebar";
import { useEffect } from "react";

export function SidebarOverlay() {
  const { open, setOpen, isMobile } = useSidebar();

  // Close sidebar when clicking overlay
  const handleOverlayClick = () => {
    if (isMobile && open) {
      setOpen(false);
    }
  };

  // Close sidebar on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isMobile && open) {
        setOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isMobile, open, setOpen]);

  // Prevent body scroll when sidebar is open on mobile
  useEffect(() => {
    if (isMobile && open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobile, open]);

  if (!isMobile) return null;

  return (
    <div
      className={`sidebar-overlay ${open ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
      onClick={handleOverlayClick}
      aria-hidden="true"
    />
  );
}
