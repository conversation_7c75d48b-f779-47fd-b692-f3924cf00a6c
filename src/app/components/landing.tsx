"use client";

import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  memo,
} from "react";
import { ChevronRight, Shield, Cpu, Layers, Server, Radio } from "lucide-react";
import { useRouter } from "next/navigation";

interface Solution {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface AnimatedBackgroundProps {
  parentRef: React.RefObject<HTMLDivElement>;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = memo(
  ({ parentRef }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const animationRef = useRef<number | null>(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    const createOptions = useCallback(
      (width: number, height: number) => ({
        len: 20,
        baseTime: 10,
        addedTime: 10,
        dieChance: 0.05,
        spawnChance: 0.5,
        sparkChance: 0.1,
        sparkDist: 10,
        sparkSize: 2,
        baseLight: 50,
        addedLight: 10,
        shadowToTimePropMult: 6,
        baseLightInputMultiplier: 0.01,
        addedLightInputMultiplier: 0.02,
        hueChange: 0.1,
        repaintAlpha: 0.04,
      }),
      []
    );

    useEffect(() => {
      const currentRef = parentRef.current;
      if (!currentRef) return;

      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          if (parentRef.current) {
            const { width, height } = parentRef.current.getBoundingClientRect();
            setDimensions({ width, height });
            // console.log({ width, height });
          }
        }
      });

      resizeObserver.observe(currentRef);

      return () => {
        if (currentRef) {
          resizeObserver.unobserve(currentRef);
        }
      };
    }, [parentRef]);

    useEffect(() => {
      const canvas = canvasRef.current;
      const ctx = canvas?.getContext("2d");
      if (!canvas || !ctx || dimensions.width === 0) return;

      const opts = createOptions(dimensions.width, dimensions.height);
      const lines: any[] = [];
      let tick = 0;

      const dieX = dimensions.width / 2 / opts.len;
      const dieY = dimensions.height / 2 / opts.len;
      const baseRad = (Math.PI * 2) / 6;
      const cx = dimensions.width / 2;
      const cy = dimensions.height / 2;

      class Line {
        x: number = 0;
        y: number = 0;
        addedX: number = 0;
        addedY: number = 0;
        rad: number = 0;
        lightInputMultiplier: number = 0;
        color: string = "";
        cumulativeTime: number = 0;
        time: number = 0;
        targetTime: number = 0;

        constructor() {
          this.reset();
        }

        reset() {
          this.x = 0;
          this.y = 0;
          this.addedX = 0;
          this.addedY = 0;
          this.rad = 0;
          this.lightInputMultiplier =
            opts.baseLightInputMultiplier +
            opts.addedLightInputMultiplier * Math.random();
          this.color = `hsl(${tick * opts.hueChange},100%,50%)`;
          this.cumulativeTime = 0;
          this.beginPhase();
        }

        beginPhase() {
          this.x += this.addedX;
          this.y += this.addedY;
          this.time = 0;
          this.targetTime =
            (opts.baseTime + opts.addedTime * Math.random()) | 0;
          this.rad += baseRad * (Math.random() < 0.5 ? 1 : -1);
          this.addedX = Math.cos(this.rad);
          this.addedY = Math.sin(this.rad);

          if (
            Math.random() < opts.dieChance ||
            this.x > dieX ||
            this.x < -dieX ||
            this.y > dieY ||
            this.y < -dieY
          ) {
            this.reset();
          }
        }

        step(ctx: CanvasRenderingContext2D) {
          ++this.time;
          ++this.cumulativeTime;

          if (this.time >= this.targetTime) {
            this.beginPhase();
          }

          const prop = this.time / this.targetTime;
          const wave = Math.sin((prop * Math.PI) / 2);
          const x = this.addedX * wave;
          const y = this.addedY * wave;

          const lightValue =
            opts.baseLight +
            opts.addedLight *
              Math.sin(this.cumulativeTime * this.lightInputMultiplier);
          ctx.shadowBlur = prop * opts.shadowToTimePropMult;
          ctx.fillStyle =
            ctx.shadowColor = `hsl(${tick * opts.hueChange},100%,${lightValue}%)`;

          ctx.fillRect(
            cx + (this.x + x) * opts.len,
            cy + (this.y + y) * opts.len,
            2,
            2
          );

          if (Math.random() < opts.sparkChance) {
            ctx.fillRect(
              cx +
                (this.x + x) * opts.len +
                Math.random() *
                  opts.sparkDist *
                  (Math.random() < 0.5 ? 1 : -1) -
                opts.sparkSize / 2,
              cy +
                (this.y + y) * opts.len +
                Math.random() *
                  opts.sparkDist *
                  (Math.random() < 0.5 ? 1 : -1) -
                opts.sparkSize / 2,
              opts.sparkSize,
              opts.sparkSize
            );
          }
        }
      }

      const loop = () => {
        if (lines.length < 50 && Math.random() < opts.spawnChance) {
          lines.push(new Line());
        }

        ctx.globalCompositeOperation = "source-over";
        ctx.shadowBlur = 0;
        ctx.fillStyle = `rgba(0,0,0,${opts.repaintAlpha})`;
        ctx.fillRect(0, 0, dimensions.width, dimensions.height);
        ctx.globalCompositeOperation = "lighter";

        lines.forEach((line) => line.step(ctx));
        ++tick;

        animationRef.current = requestAnimationFrame(loop);
      };

      animationRef.current = requestAnimationFrame(loop);

      return () => {
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
    }, [dimensions, createOptions]);

    return (
      <canvas
        ref={canvasRef}
        width={dimensions.width}
        height={dimensions.height}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          zIndex: -1,
          borderRadius: 12,
          backgroundColor: "transparent",
        }}
      />
    );
  }
);

const IoTLandingPage: React.FC = () => {
  const [activeTab, setActiveTab] =
    useState<keyof typeof solutions>("hardware");
  const containerRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const navigate = useRouter();

  // Responsive breakpoint check
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const solutions: Record<string, Solution> = useMemo(
    () => ({
      hardware: {
        icon: <Cpu className="w-12 h-12 text-indigo-700" />,
        title: "Custom IoT Hardware",
        description:
          "Advanced, scalable IoT devices engineered for precision and reliability.",
      },
      software: {
        icon: <Layers className="w-12 h-12 text-teal-600" />,
        title: "Intelligent Software Platform",
        description:
          "Robust, secure software ecosystem for seamless device management and data insights.",
      },
      connectivity: {
        icon: <Radio className="w-12 h-12 text-sky-600" />,
        title: "Adaptive Connectivity",
        description:
          "Multi-protocol support ensuring robust communication across diverse networks.",
      },
    }),
    []
  );

  const navItems = useMemo(
    () => ["Solutions", "Products", "About", "Contact"],
    []
  );

  return (
    <div className="min-h-max overflow-hidden font-sans select-none">
      <div className="relative z-10">
        {/* Header with mobile responsiveness */}
        <header className="container mx-auto px-4 py-6 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-3">
            <Server className="w-8 h-8 text-indigo-700" />
            <h1 className="text-2xl font-bold">Homicen IoT Solutions</h1>
          </div>
          <nav className={`${isMobile ? "w-full" : ""}`}>
            <ul
              className={`
              ${
                isMobile
                  ? "flex flex-col space-y-2 text-center"
                  : "flex space-x-6"
              }
            `}
            >
              {navItems.map((item) => (
                <li
                  key={item}
                  className="text-neutral-600 dark:text-gray-300 dark:hover:text-cyan-300 hover:text-indigo-600 transition cursor-pointer"
                  onClick={() => navigate.push(`/${item.toLowerCase()}`)}
                >
                  {item}
                </li>
              ))}
            </ul>
          </nav>
        </header>

        {/* Main content with responsive grid */}
        <main className="container mx-auto px-4 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center py-16">
          <section className="order-2 md:order-1">
            <h2 className="text-3xl md:text-4xl font-extrabold text-foreground mb-6 text-center md:text-left">
              Intelligent IoT Devices,
              <br />
              Engineered for Tomorrow
            </h2>
            <p className="text-base md:text-lg text-muted-foreground mb-8 text-center md:text-left">
              Comprehensive IoT solutions combining cutting-edge hardware,
              intelligent software, and seamless connectivity.
            </p>
            <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center md:justify-start">
              <button
                className="bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition flex items-center justify-center"
                onClick={() => navigate.push("/solutions")}
              >
                Explore Solutions <ChevronRight className="ml-2" />
              </button>
              <button className="border border-primary text-primary px-6 py-3 rounded-lg hover:bg-accent hover:text-accent-foreground transition">
                Request Demo
              </button>
            </div>
            <div className="mt-12 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-6 justify-center md:justify-start">
              <div className="flex items-center space-x-2 justify-center md:justify-start">
                <Shield className="w-6 h-6 text-teal-600" />
                <span className="text-muted-foreground">
                  Secure by Design
                </span>
              </div>
              <div className="flex items-center space-x-2 justify-center md:justify-start">
                <Cpu className="w-6 h-6 text-sky-600" />
                <span className="text-gray-600 dark:text-gray-400">
                  Custom Engineering
                </span>
              </div>
            </div>
          </section>
          <section
            className="order-1 md:order-2 bg-neutral-900/30 dark:bg-neutral-500/30 shadow-xl rounded-xl p-4 md:p-8 relative"
            ref={containerRef}
          >
            <AnimatedBackground parentRef={containerRef} />
            <div className="flex overflow-x-auto mb-6 border-b">
              {Object.keys(solutions).map((key) => (
                <button
                  key={key}
                  onClick={() => setActiveTab(key as keyof typeof solutions)}
                  className={`py-3 px-4 md:px-6 flex-shrink-0 ${activeTab === key ? "border-b-2 border-indigo-600 text-indigo-600" : "text-gray-300"}`}
                >
                  {solutions[key].title}
                </button>
              ))}
            </div>
            <div className="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
              <div className="flex justify-center md:justify-start w-full md:w-auto">
                {solutions[activeTab]?.icon}
              </div>
              <div className="text-center md:text-left">
                <h3 className="text-xl font-bold text-gray-100">
                  {solutions[activeTab]?.title}
                </h3>
                <p className="text-gray-400 mt-2">
                  {solutions[activeTab]?.description}
                </p>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
};

export default IoTLandingPage;
