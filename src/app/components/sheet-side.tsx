import {
  SettingsIcon,
  BellRing,
  Check,
  User,
  LogOut,
  Palette,
  Shield,
  Bell,
  ChevronRight,
  Crown,
  Mail,
  Clock,
  X,
  Building2,
  ChevronDown,
} from "lucide-react";
import {
  Sheet,
  SheetClose,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetPortal,
  SheetOverlay,
} from "@/components/ui/sheet";
import * as SheetPrimitive from "@radix-ui/react-dialog";
import { cva, type VariantProps } from "class-variance-authority";
import React from "react";

// Custom SheetContent without default close button
const sheetVariants = cva(
  "fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
  {
    variants: {
      side: {
        top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
        bottom:
          "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
        left: "inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
        right:
          "inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm",
      },
    },
    defaultVariants: {
      side: "right",
    },
  }
);

interface CustomSheetContentProps
  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,
    VariantProps<typeof sheetVariants> {}

const CustomSheetContent = React.forwardRef<
  React.ElementRef<typeof SheetPrimitive.Content>,
  CustomSheetContentProps
>(({ side = "right", className, children, ...props }, ref) => (
  <SheetPortal>
    <SheetOverlay />
    <SheetPrimitive.Content
      ref={ref}
      className={cn(sheetVariants({ side }), className)}
      {...props}
    >
      {children}
    </SheetPrimitive.Content>
  </SheetPortal>
));
CustomSheetContent.displayName = "CustomSheetContent";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

import { ThemeSelector } from "@/components/ui/theme-selector";
// Note: AdminModeToggle removed - using JWT-only permissions
import { clientUserInfo } from "@/lib/http";
import { cn } from "@/lib/utils";
import authApiRequests from "@/apiRequests/user/auth";
import { redirect } from "next/navigation";
import { useEffect, useState } from "react";
import { useOrganization } from "@/context/organization-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";

const SHEET_SIDES = ["top", "right", "bottom", "left"] as const;
type SheetSide = (typeof SHEET_SIDES)[number];
type CardProps = React.ComponentProps<typeof Card>;

const notifications = [
  {
    title: "Your call has been confirmed.",
    description: "1 hour ago",
  },
  {
    title: "You have a new message!",
    description: "1 hour ago",
  },
  {
    title: "Your subscription is expiring soon!",
    description: "2 hours ago",
  },
];

const signOut = async () => {
  const currentPath = window.location.pathname + window.location.search;
  if (currentPath !== "/login") {
    // Note: User mode setting removed - using JWT-only permissions
    console.log("check currentPath: ", currentPath);
    await authApiRequests.logout({});
    sessionStorage.setItem("redirectAfterLogin", currentPath);
    redirect("/login");
  }
};

export function CardDemo({ className, ...props }: CardProps) {
  return (
    <Card
      className={cn("w-full border-0 shadow-none bg-transparent", className)}
      {...props}
    >
      <CardContent className="p-0 space-y-4">
        {/* Push Notifications Toggle */}
        <div className="flex items-center justify-between p-4 bg-card rounded-lg border hover:bg-muted/50 transition-colors">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center">
              <BellRing className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="text-sm font-medium">Push Notifications</div>
              <div className="text-xs text-muted-foreground">
                Send notifications to device
              </div>
            </div>
          </div>
          <Switch />
        </div>

        {/* Recent Notifications */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Recent Activity</h4>
            <Button variant="ghost" size="sm" className="h-6 text-xs">
              View All
            </Button>
          </div>

          {notifications.map((notification, index) => (
            <div
              key={index}
              className="flex items-start gap-3 p-3 bg-card rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
            >
              <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-2 animate-pulse" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium leading-none mb-1">
                  {notification.title}
                </p>
                <div className="flex items-center gap-2">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <p className="text-xs text-muted-foreground">
                    {notification.description}
                  </p>
                </div>
              </div>
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            </div>
          ))}
        </div>

        {/* Action Button */}
        <Button
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0"
          size="sm"
        >
          <Check className="h-4 w-4 mr-2" />
          Mark all as read
        </Button>
      </CardContent>
    </Card>
  );
}

export function TabsDemo() {
  const { userInfo } = clientUserInfo;
  return (
    // <Card>
    // </Card>
    <Tabs defaultValue="account" className="w-full max-w-[350px] mt-6 sm:mt-10">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="account">Account</TabsTrigger>
        {/* <TabsTrigger value="password">Password</TabsTrigger> */}
      </TabsList>
      <TabsContent value="account">
        <Card>
          <CardHeader>
            <CardTitle>Account</CardTitle>
            <CardDescription>
              Make changes to your account here. Click save when you're done.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                disabled
                defaultValue={
                  userInfo?.user?.firstName + " " + userInfo?.user?.lastName
                }
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                disabled
                defaultValue={userInfo?.user?.email}
              />
            </div>
          </CardContent>
          <CardFooter>{/* <Button>Save changes</Button> */}</CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="password">
        <Card>
          <CardHeader>
            <CardTitle>Password</CardTitle>
            <CardDescription>
              Change your password here. After saving, you'll be logged out.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="space-y-1">
              <Label htmlFor="current">Current password</Label>
              <Input id="current" type="password" />
            </div>
            <div className="space-y-1">
              <Label htmlFor="new">New password</Label>
              <Input id="new" type="password" />
            </div>
          </CardContent>
          <CardFooter>
            <Button>Save password</Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  );
}

interface SheetSideProps {
  open?: boolean;
}

export default function SheetSide({ open }: SheetSideProps) {
  const [position, setPosition] = useState(200);
  const [isDragging, setIsDragging] = useState(false);
  const [isOpen, setIsOpen] = useState(open);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const { userInfo } = clientUserInfo;
  const { 
    currentOrgId, 
    availableOrganizations, 
    switchOrganization, 
    hasMultipleOrgs,
    isLoading 
  } = useOrganization();

  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  // Hide default close button after component mounts
  useEffect(() => {
    const hideDefaultCloseButton = () => {
      const sheetContent = document.querySelector(
        '[data-radix-dialog-content][data-side="right"]'
      );
      if (sheetContent) {
        const defaultCloseButton = sheetContent.querySelector(
          "button[data-radix-dialog-close]"
        );
        if (defaultCloseButton) {
          (defaultCloseButton as HTMLElement).style.display = "none";
        }
      }
    };

    // Run immediately and after a short delay to ensure DOM is ready
    hideDefaultCloseButton();
    const timer = setTimeout(hideDefaultCloseButton, 100);

    return () => clearTimeout(timer);
  }, [isOpen]);

  const handleMouseDown = () => {
    setIsDragging(true);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition((prev) => Math.max(0, prev + e.movementY));
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Touch handlers for mobile swipe to close
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    // Close sheet on right swipe (swipe from left to right)
    if (isRightSwipe && window.innerWidth <= 768) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isDragging) {
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp);
    } else {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    }

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging]);

  return (
    <div className="flex flex-col gap-2">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        {/* Desktop Trigger - Draggable */}
        <SheetTrigger asChild className="hidden md:block">
          <Button
            variant="outline"
            style={{
              top: `${position}px`,
              right: 0,
            }}
            onMouseDown={handleMouseDown}
            className="absolute rounded-l-2xl bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-50"
          >
            <SettingsIcon className="h-5 w-5" />
          </Button>
        </SheetTrigger>

        {/* Mobile Trigger - Disabled */}
        {/* <SheetTrigger asChild className="md:hidden">
          <Button
            variant="outline"
            className="fixed top-4 right-4 w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-50"
          >
            <SettingsIcon className="h-5 w-5" />
            <span className="sr-only">Mở cài đặt</span>
          </Button>
        </SheetTrigger> */}

        <CustomSheetContent
          side={"right"}
          className="w-full sm:w-[420px] sm:max-w-[420px] p-0 overflow-hidden bg-gradient-to-b from-background to-muted/20 flex flex-col h-full"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Accessibility Title - Hidden but required */}
          <SheetHeader className="sr-only">
            <SheetTitle>User Settings</SheetTitle>
            <SheetDescription>
              Manage your account settings, preferences, and system
              configuration
            </SheetDescription>
          </SheetHeader>

          {/* Header with User Profile */}
          <div className="relative p-4 sm:p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white flex-shrink-0">
            <div className="absolute inset-0 bg-black/10"></div>

            {/* Mobile Close Button - Hidden */}
            <SheetClose asChild>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 hidden w-10 h-10 bg-white/15 hover:bg-white/25 active:bg-white/30 text-white border-0 rounded-full backdrop-blur-md z-10 shadow-lg shadow-black/20 hover:shadow-xl hover:shadow-black/30 transition-all duration-300 hover:scale-110 active:scale-95 touch-manipulation"
              >
                <X className="h-5 w-5" />
                <span className="sr-only">Đóng</span>
              </Button>
            </SheetClose>

            <div className="relative">
              <div className="flex items-center gap-3 sm:gap-4 mb-4 pr-10 sm:pr-0">
                <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center text-lg sm:text-2xl font-bold flex-shrink-0">
                  {userInfo?.user?.firstName?.split("")[0] || "U"}
                </div>
                <div className="flex-1 min-w-0">
                  <h2 className="text-lg sm:text-xl font-bold truncate">
                    {userInfo?.user?.firstName} {userInfo?.user?.lastName}
                  </h2>
                  <p className="text-white/80 text-xs sm:text-sm truncate">
                    {userInfo?.user?.email}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-xs text-white/80">Online</span>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-2 sm:gap-3">
                <div className="text-center p-2 bg-white/10 rounded-lg backdrop-blur-sm">
                  <div className="text-base sm:text-lg font-bold">24</div>
                  <div className="text-xs text-white/80">Devices</div>
                </div>
                <div className="text-center p-2 bg-white/10 rounded-lg backdrop-blur-sm">
                  <div className="text-base sm:text-lg font-bold">12</div>
                  <div className="text-xs text-white/80">Projects</div>
                </div>
                <div className="text-center p-2 bg-white/10 rounded-lg backdrop-blur-sm">
                  <div className="text-base sm:text-lg font-bold">98%</div>
                  <div className="text-xs text-white/80">Uptime</div>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-4 sm:p-6 space-y-4 sm:space-y-6 overflow-y-auto min-h-0">
            {/* Organization Section */}
            {hasMultipleOrgs && (
              <div className="space-y-4">
                <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Organization
                </h3>

                <div className="p-3 bg-card rounded-lg border">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="ghost" 
                        className="w-full justify-between h-auto p-0 hover:bg-transparent"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center">
                            <Building2 className="h-4 w-4 text-blue-600" />
                          </div>
                          <div className="text-left">
                            <div className="text-sm font-medium">
                              {availableOrganizations.find(org => org.id === currentOrgId)?.orgName || 'Select Organization'}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {currentOrgId ? `ID: ${currentOrgId.slice(-8)}` : 'No organization selected'}
                            </div>
                          </div>
                        </div>
                        <ChevronDown className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-64">
                      <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {availableOrganizations.map((org) => (
                        <DropdownMenuItem
                          key={org.id}
                          onClick={() => switchOrganization(org.id)}
                          className="flex items-center gap-3 cursor-pointer"
                        >
                          <div className="w-6 h-6 bg-blue-500/10 rounded flex items-center justify-center">
                            <Building2 className="h-3 w-3 text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <div className="text-sm font-medium">{org.orgName}</div>
                            <div className="text-xs text-muted-foreground">ID: {org.id.slice(-8)}</div>
                          </div>
                          {currentOrgId === org.id && (
                            <Check className="h-4 w-4 text-green-600" />
                          )}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            )}

            {/* Account Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                <User className="h-4 w-4" />
                Account Settings
              </h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-card rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center">
                      <Mail className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium">Email</div>
                      <div className="text-xs text-muted-foreground">
                        {userInfo?.user?.email}
                      </div>
                    </div>
                  </div>
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                </div>

                <div className="flex items-center justify-between p-3 bg-card rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-500/10 rounded-lg flex items-center justify-center">
                      <Shield className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium">Security</div>
                      <div className="text-xs text-muted-foreground">
                        Two-factor authentication
                      </div>
                    </div>
                  </div>
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            </div>

            {/* Theme Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Appearance
              </h3>
              <div className="p-4 bg-card rounded-lg border">
                <ThemeSelector variant="button" showLabel={true} />
              </div>
            </div>

            {/* Permission Status Section */}
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                <Crown className="h-4 w-4" />
                Access Control
              </h3>
              <div className="p-4 bg-card rounded-lg border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-500/10 rounded-lg flex items-center justify-center">
                      <Shield className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="text-sm font-medium">JWT Permissions</div>
                      <div className="text-xs text-muted-foreground">
                        Access controlled by token
                      </div>
                    </div>
                  </div>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Notifications */}
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Notifications
              </h3>
              <CardDemo />
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 p-4 sm:p-6 border-t bg-muted/30 sheet-side-footer">
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-3">
              {/* Settings Button - Enhanced Mobile Design */}
              <Button
                variant="outline"
                className="group flex-1 h-14 sm:h-10 text-base sm:text-sm font-medium bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-blue-200/50 dark:border-blue-800/50 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/40 dark:hover:to-indigo-900/40 hover:border-blue-300/60 dark:hover:border-blue-700/60 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] touch-manipulation"
                size="sm"
              >
                <SettingsIcon className="h-5 w-5 sm:h-4 sm:w-4 mr-3 sm:mr-2 flex-shrink-0 text-blue-600 dark:text-blue-400 group-hover:rotate-90 transition-transform duration-300" />
                <span className="truncate text-blue-700 dark:text-blue-300 font-semibold">
                  Settings
                </span>
              </Button>

              {/* Sign Out Button - Enhanced Mobile Design */}
              <SheetClose asChild>
                <Button
                  onClick={() => signOut()}
                  className="group flex-1 h-14 sm:h-10 bg-gradient-to-r from-red-500 via-red-600 to-pink-600 hover:from-red-600 hover:via-red-700 hover:to-pink-700 text-white border-0 text-base sm:text-sm font-semibold shadow-lg shadow-red-500/30 hover:shadow-xl hover:shadow-red-500/40 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] touch-manipulation"
                  size="sm"
                >
                  <LogOut className="h-5 w-5 sm:h-4 sm:w-4 mr-3 sm:mr-2 flex-shrink-0 group-hover:-translate-x-1 transition-transform duration-300" />
                  <span className="truncate">Logout</span>
                </Button>
              </SheetClose>
            </div>

            {/* Mobile Quick Actions - Enhanced */}
            <div className="mt-4 sm:hidden">
              <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground/80">
                <SheetClose asChild>
                  <button className="flex items-center gap-2 px-3 py-2 rounded-full bg-muted/50 hover:bg-muted hover:text-foreground transition-all duration-200 hover:scale-105 active:scale-95 touch-manipulation">
                    <X className="h-3 w-3" />
                    <span className="font-medium">Close</span>
                  </button>
                </SheetClose>
                <div className="flex items-center gap-2 text-muted-foreground/60">
                  <div className="w-1 h-1 bg-current rounded-full"></div>
                  <span className="text-xs">
                    Swipe right to close
                  </span>
                </div>
              </div>

              {/* Mobile Gesture Indicator */}
              <div className="flex justify-center mt-3">
                <div className="w-12 h-1 bg-muted-foreground/20 rounded-full"></div>
              </div>
            </div>
          </div>
        </CustomSheetContent>
      </Sheet>
    </div>
  );
}
