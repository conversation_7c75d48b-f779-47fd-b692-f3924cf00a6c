'use client';

import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { QrCode, Download, Info } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PlatformInfo, VersionHistoryItem } from './app-data';

interface VersionTableProps {
  platform: PlatformInfo;
  data: VersionHistoryItem[];
  onShowQR: (platformName: string, version: VersionHistoryItem) => void;
}

const VersionTable: React.FC<VersionTableProps> = ({ platform, data, onShowQR }) => {
  const { name, iconComponent: PlatformIcon, colorClass } = platform;

  return (
    <div>
      <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 flex items-center gap-2">
        <span className={colorClass.icon}>
          <PlatformIcon />
        </span>
        <PERSON>ên bản {name}
      </h2>
      
      <div className="border rounded-lg shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[120px] sm:w-[180px]">Phiên bản</TableHead>
              <TableHead className="w-[80px] sm:w-[100px]">Build</TableHead>
              <TableHead className="w-[120px] sm:w-[180px]">Ngày tạo</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((version) => (
              <TableRow key={version.id}>
                <TableCell className="font-medium">
                  <span className="text-xs sm:text-sm">{version.version}</span>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {version.buildNumber}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className="text-xs sm:text-sm text-gray-500">
                    {version.buildDate} {version.buildTime}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-1 sm:gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                      onClick={() => onShowQR(platform.name, version)}
                    >
                      <QrCode className="w-3 h-3 sm:w-4 sm:h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                      asChild
                    >
                      <a href={version.downloadUrl} target="_blank" rel="noopener noreferrer">
                        <Download className="w-3 h-3 sm:w-4 sm:h-4" />
                      </a>
                    </Button>
                    <Button variant="ghost" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                      <Info className="w-3 h-3 sm:w-4 sm:h-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default VersionTable;