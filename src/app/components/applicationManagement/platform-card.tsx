'use client';

import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Download, QrCode, Clock } from 'lucide-react';
import { PlatformInfo, AppVersion } from './app-data';

interface PlatformCardProps {
  platform: PlatformInfo;
  versionData: AppVersion;
  onShowQR: (platformName: string) => void;
}

const PlatformCard: React.FC<PlatformCardProps> = ({ 
  platform, 
  versionData, 
  onShowQR 
}) => {
  const { name, iconComponent: PlatformIcon, colorClass, fileType } = platform;
  const { version, buildNumber, buildDate, buildTime } = versionData;

  return (
    <div className={`border rounded-lg shadow-sm overflow-hidden border-l-4 ${colorClass.border}`}>
      <div className={`${colorClass.bg} p-3 sm:p-4 flex items-center justify-between`}>
        <div className="flex items-center gap-2 sm:gap-3">
          <div className={`${colorClass.icon} p-1.5 sm:p-2 rounded-lg`}>
            <PlatformIcon />
          </div>
          <div>
            <h3 className="font-semibold text-base sm:text-lg">{name}</h3>
            <div className="flex items-center gap-2">
              <Badge className={`${colorClass.badge} font-normal text-xs`}>
                Latest
              </Badge>
              <span className="text-xs sm:text-sm text-gray-500">
                {buildDate}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="p-4 sm:p-6">
        <div className="mb-4 sm:mb-6">
          <div className="flex flex-wrap items-center gap-2 mb-1">
            <span className="text-xs sm:text-sm text-gray-500">Version:</span>
            <span className="font-semibold text-sm sm:text-base">{version}</span>
            <Badge variant="outline" className="ml-1 text-xs">
              {buildNumber}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500" />
            <span className="text-xs sm:text-sm text-gray-500">
              {buildDate} {buildTime}
            </span>
          </div>
        </div>

        <div className="flex justify-center mb-4 sm:mb-6">
          <div 
            className={`relative border-4 border-gray-200 rounded-lg p-2 sm:p-3 cursor-pointer ${colorClass.hover} transition-colors`}
            onClick={() => onShowQR(name)}
          >
            <div className="w-24 h-24 sm:w-32 sm:h-32 bg-gray-100 flex items-center justify-center">
              <QrCode size={80} className="text-gray-400 sm:w-24 sm:h-24" />
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-2 sm:gap-3">
          <Button 
            onClick={() => onShowQR(name)}
            variant="outline"
            className="bg-background text-foreground border border-border hover:bg-accent hover:text-accent-foreground flex items-center justify-center gap-2 text-xs sm:text-sm w-full sm:w-auto"
          >
            <QrCode className="w-3 h-3 sm:w-4 sm:h-4" />
            Mã QR
          </Button>
          <Button className={`${colorClass.button} text-primary-foreground flex items-center justify-center gap-2 text-xs sm:text-sm w-full sm:w-auto mt-2 sm:mt-0`}>
            <Download className="w-3 h-3 sm:w-4 sm:h-4" />
            Tải xuống {fileType}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PlatformCard;