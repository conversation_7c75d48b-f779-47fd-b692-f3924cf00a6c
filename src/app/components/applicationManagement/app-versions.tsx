'use client';

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { appVersions, platformInfo } from './app-data';
import { AppVersion, VersionHistoryItem } from './app-data';
import PlatformCard from './platform-card';
import VersionTable from './version-table';
import QRDialog from './qr-dialog';

type TabValues = 'latest' | 'history';

interface QRDialogState {
  isOpen: boolean;
  platform: string;
  version: AppVersion | VersionHistoryItem | null;
}

const AppVersionManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabValues>('latest');
  const [qrDialogState, setQRDialogState] = useState<QRDialogState>({
    isOpen: false,
    platform: '',
    version: null
  });

  const handleShowQR = (platform: string, version: VersionHistoryItem | null = null) => {
    setQRDialogState({
      isOpen: true,
      platform,
      version: version || (platform === 'Android' ? appVersions.android.latest : appVersions.ios.latest)
    });
  };

  const handleCloseQR = () => {
    setQRDialogState(prev => ({ ...prev, isOpen: false }));
  };

  return (
    <div className="bg-background min-h-screen">
      <div className="container mx-auto py-4 sm:py-6 md:py-8 px-4 max-w-6xl">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="relative w-20 h-20 sm:w-24 sm:h-24 bg-amber-400 rounded-lg overflow-hidden shadow-lg flex items-center justify-center">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-amber-700 rounded-full flex items-center justify-center">
              <div className="text-white text-2xl sm:text-3xl">🏡</div>
            </div>
          </div>

          <div className="flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
              <h1 className="text-2xl sm:text-3xl font-bold">Homicen Staging</h1>
              <Badge variant="outline" className="bg-primary text-primary-foreground w-fit">STG</Badge>
            </div>
            <p className="text-muted-foreground mt-1 sm:mt-2 text-sm sm:text-base">Quản lý phiên bản ứng dụng trong môi trường staging</p>
          </div>
        </div>

        {/* Tabs using shadcn/ui Tabs component */}
        <Tabs 
          defaultValue="latest" 
          className="w-full" 
          onValueChange={(value) => setActiveTab(value as TabValues)}
          value={activeTab}
        >
          <TabsList className="mb-4">
            <TabsTrigger value="latest">Phiên bản mới nhất</TabsTrigger>
            <TabsTrigger value="history">Lịch sử phiên bản</TabsTrigger>
          </TabsList>
          
          <TabsContent value="latest" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              {/* Android Card */}
              <PlatformCard 
                platform={platformInfo.android} 
                versionData={appVersions.android.latest}
                onShowQR={handleShowQR}
              />
              
              {/* iOS Card */}
              <PlatformCard 
                platform={platformInfo.ios} 
                versionData={appVersions.ios.latest}
                onShowQR={handleShowQR}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="history" className="mt-0">
            <div className="space-y-6 sm:space-y-8">
              {/* Android History */}
              <VersionTable 
                platform={platformInfo.android}
                data={appVersions.android.history}
                onShowQR={handleShowQR}
              />
              
              {/* iOS History */}
              <VersionTable 
                platform={platformInfo.ios}
                data={appVersions.ios.history}
                onShowQR={handleShowQR}
              />
            </div>
          </TabsContent>
        </Tabs>

        {/* QR Dialog */}
        <QRDialog 
          isOpen={qrDialogState.isOpen}
          onClose={handleCloseQR}
          platform={qrDialogState.platform}
          version={qrDialogState.version}
        />
      </div>
    </div>
  );
};

export default AppVersionManager;