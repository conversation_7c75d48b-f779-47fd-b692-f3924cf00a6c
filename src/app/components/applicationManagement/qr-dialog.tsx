'use client';

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Download, QrCode } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { AppVersion, VersionHistoryItem } from './app-data';

interface QRDialogProps {
  isOpen: boolean;
  onClose: () => void;
  platform: string;
  version: AppVersion | VersionHistoryItem | null;
}

const QRDialog: React.FC<QRDialogProps> = ({ isOpen, onClose, platform, version }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="w-4 h-4 sm:w-5 sm:h-5" />
            Mã QR tải xuống {platform}
          </DialogTitle>
          {version && (
            <DialogDescription>
              <PERSON><PERSON><PERSON> bản {version.version} ({version.buildNumber})
            </DialogDescription>
          )}
        </DialogHeader>
        
        <div className="flex flex-col items-center p-2 sm:p-4">
          <div className="border-4 sm:border-8 border-border rounded-lg p-2 sm:p-4 mb-3 sm:mb-4">
            <div className="w-36 h-36 sm:w-48 sm:h-48 bg-muted flex items-center justify-center">
              <QrCode size={96} className="text-muted-foreground sm:w-32 sm:h-32" />
            </div>
          </div>
          <p className="text-xs sm:text-sm text-muted-foreground text-center mb-3 sm:mb-4">
            Quét mã QR bằng thiết bị {platform} để tải xuống phiên bản mới nhất
          </p>
        </div>
        
        <DialogFooter>
          <Button className="w-full bg-primary hover:bg-primary/90 text-primary-foreground flex items-center justify-center gap-2 text-xs sm:text-sm">
            <Download className="w-3 h-3 sm:w-4 sm:h-4" />
            Tải xuống trực tiếp
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default QRDialog;