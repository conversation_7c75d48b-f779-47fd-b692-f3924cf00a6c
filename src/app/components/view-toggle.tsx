"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { LayoutGrid, LayoutList, List } from "lucide-react";
import { cn } from "@/lib/utils";
import { ViewMode } from "@/hooks/useViewMode";

interface ViewToggleProps {
  viewMode: ViewMode | string;
  onViewModeChange: (mode: ViewMode | string) => void;
  options?: {
    grid?: {
      value: ViewMode | string;
      icon?: React.ComponentType<{ className?: string }>;
      label?: string;
    };
    list?: {
      value: ViewMode | string;
      icon?: React.ComponentType<{ className?: string }>;
      label?: string;
    };
  };
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function ViewToggle({
  viewMode,
  onViewModeChange,
  options = {
    grid: { value: "grid", icon: LayoutGrid, label: "Grid" },
    list: { value: "list", icon: LayoutList, label: "List" }
  },
  className,
  size = "sm"
}: ViewToggleProps) {
  const GridIcon = options.grid?.icon || LayoutGrid;
  const ListIcon = options.list?.icon || LayoutList;
  
  const buttonSizes = {
    sm: "h-8 w-8",
    md: "h-9 w-9", 
    lg: "h-10 w-10"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  };

  return (
    <div className={cn(
      "inline-flex bg-muted/50 backdrop-blur-sm rounded-lg p-1 border border-border/50",
      // Mobile: Compact size with explicit width, Desktop: Normal size
      "w-fit shrink-0 flex-none",
      // Ensure it doesn't grow on mobile
      "max-w-fit",
      className
    )}>
      <Button
        variant={viewMode === options.grid?.value ? "secondary" : "ghost"}
        size="sm"
        onClick={() => onViewModeChange(options.grid?.value || "grid")}
        className={cn(
          buttonSizes[size],
          "transition-all duration-200 hover:scale-105 flex-shrink-0"
        )}
        title={options.grid?.label || "Grid View"}
      >
        <GridIcon className={iconSizes[size]} />
        <span className="sr-only">{options.grid?.label || "Grid View"}</span>
      </Button>
      <Button
        variant={viewMode === options.list?.value ? "secondary" : "ghost"}
        size="sm"
        onClick={() => onViewModeChange(options.list?.value || "list")}
        className={cn(
          buttonSizes[size],
          "transition-all duration-200 hover:scale-105 flex-shrink-0"
        )}
        title={options.list?.label || "List View"}
      >
        <ListIcon className={iconSizes[size]} />
        <span className="sr-only">{options.list?.label || "List View"}</span>
      </Button>
    </div>
  );
}

// Preset configurations for different pages
export const VIEW_TOGGLE_PRESETS = {
  // Standard grid/list toggle
  standard: {
    grid: { value: "grid", icon: LayoutGrid, label: "Grid View" },
    list: { value: "list", icon: LayoutList, label: "List View" }
  },
  
  // Table/grid toggle (for management pages)
  tableGrid: {
    grid: { value: "grid", icon: LayoutGrid, label: "Grid View" },
    list: { value: "table", icon: LayoutList, label: "Table View" }
  },
  
  // Organizations specific
  organizations: {
    grid: { value: "grid", icon: LayoutGrid, label: "Grid View" },
    list: { value: "list", icon: List, label: "List View" }
  }
};
