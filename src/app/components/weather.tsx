"use client";

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Sun, Cloud, CloudRain, Wind, Droplets, ThermometerSun, CloudFog, CloudSnow, CloudLightning, MapPin, Loader2 } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import { Alert, AlertDescription } from "@/components/ui/alert";

interface Location {
  latitude: number;
  longitude: number;
  name: string;
  loading: boolean;
}

interface CurrentWeather {
  temperature: number;
  humidity: number;
  weatherCode: number;
  windSpeed: number;
  precipitation: number;
}

interface HourlyForecast {
  time: string;
  temperature: number;
  weatherCode: number;
}

interface DailyForecast {
  date: string;
  maxTemp: number;
  minTemp: number;
  weatherCode: number;
  precipitation: number;
}

interface Forecast {
  hourly: HourlyForecast[];
  daily: DailyForecast[];
}

interface WeatherInfo {
  icon: JSX.Element;
  description: string;
}

interface GeocodingResult {
  name: string;
  admin1?: string;
  country?: string;
}

interface GeocodingResponse {
  results?: GeocodingResult[];
}

const WeatherDashboard: React.FC = () => {
  const [currentWeather, setCurrentWeather] = useState<CurrentWeather | null>(null);
  const [forecast, setForecast] = useState<Forecast | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [location, setLocation] = useState<Location>({
    latitude: 10.8231,
    longitude: 106.6297,
    name: 'TP.HCM',
    loading: true
  });

  const getLocationName = async (latitude: number, longitude: number): Promise<string> => {
    try {
      const response = await fetch(
        `https://api.open-meteo.com/v1/geocoding?latitude=${latitude}&longitude=${longitude}&language=vi`
      );
      const data: GeocodingResponse = await response.json();
      
      if (data.results && data.results.length > 0) {
        const result = data.results[0];
        // Combine city name with administrative region if available
        return result.admin1 
          ? `${result.name}, ${result.admin1}`
          : result.name;
      }
      return 'Vị trí hiện tại';
    } catch (err) {
      console.log('Lỗi khi lấy tên địa điểm:', err);
      return 'Vị trí hiện tại';
    }
  };

  const getCurrentLocation = (): void => {
    if (!navigator.geolocation) {
      setError('Trình duyệt của bạn không hỗ trợ định vị');
      setLocation(prev => ({ ...prev, loading: false }));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const locationName = await getLocationName(
          position.coords.latitude,
          position.coords.longitude
        );

        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          name: locationName,
          loading: false
        });
      },
      (error) => {
        console.log('Lỗi định vị:', error);
        setLocation(prev => ({ ...prev, loading: false }));
        setError('Không thể lấy vị trí của bạn. Đang hiển thị thời tiết tại TP.HCM.');
      }
    );
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  useEffect(() => {
    const fetchWeatherData = async (): Promise<void> => {
      if (location.loading) return;
      
      try {
        const forecastResponse = await fetch(
          `https://api.open-meteo.com/v1/forecast?latitude=${location.latitude}&longitude=${location.longitude}&hourly=temperature_2m,relative_humidity_2m,precipitation_probability,weather_code,wind_speed_10m&daily=weather_code,temperature_2m_max,temperature_2m_min,precipitation_probability_max&timezone=auto`
        );
        const data = await forecastResponse.json();

        const currentHourIndex = new Date().getHours();
        const currentWeatherData: CurrentWeather = {
          temperature: data.hourly.temperature_2m[currentHourIndex],
          humidity: data.hourly.relative_humidity_2m[currentHourIndex],
          weatherCode: data.hourly.weather_code[currentHourIndex],
          windSpeed: data.hourly.wind_speed_10m[currentHourIndex],
          precipitation: data.hourly.precipitation_probability[currentHourIndex]
        };

        const hourlyForecast: HourlyForecast[] = data.hourly.temperature_2m
          .slice(currentHourIndex, currentHourIndex + 24)
          .map((temp: number, index: number) => ({
            time: new Date(data.hourly.time[currentHourIndex + index]).getHours() + 'h',
            temperature: Math.round(temp),
            weatherCode: data.hourly.weather_code[currentHourIndex + index]
          }));

        const dailyForecast: DailyForecast[] = data.daily.weather_code.map((code: number, index: number) => ({
          date: new Date(data.daily.time[index]).toLocaleDateString('vi-VN', { weekday: 'short' }),
          maxTemp: Math.round(data.daily.temperature_2m_max[index]),
          minTemp: Math.round(data.daily.temperature_2m_min[index]),
          weatherCode: code,
          precipitation: data.daily.precipitation_probability_max[index]
        }));

        setCurrentWeather(currentWeatherData);
        setForecast({
          hourly: hourlyForecast,
          daily: dailyForecast
        });
        setLoading(false);
      } catch (err) {
        setError('Không thể tải dữ liệu thời tiết');
        setLoading(false);
      }
    };

    fetchWeatherData();
  }, [location.loading, location.latitude, location.longitude]);

  const getWeatherInfo = (code: number): WeatherInfo => {
    if (code === 0) return { icon: <Sun className="text-yellow-500" />, description: "Trời quang" };
    if (code <= 3) return { icon: <Cloud className="text-gray-400" />, description: "Ít mây" };
    if (code <= 48) return { icon: <CloudFog className="text-gray-500" />, description: "Sương mù" };
    if (code <= 57) return { icon: <CloudRain className="text-blue-300" />, description: "Mưa phùn" };
    if (code <= 67) return { icon: <CloudRain className="text-blue-500" />, description: "Mưa" };
    if (code <= 77) return { icon: <CloudSnow className="text-blue-200" />, description: "Tuyết rơi" };
    if (code <= 82) return { icon: <CloudRain className="text-blue-600" />, description: "Mưa rào" };
    if (code <= 86) return { icon: <CloudSnow className="text-blue-300" />, description: "Mưa tuyết" };
    if (code <= 99) return { icon: <CloudLightning className="text-yellow-400" />, description: "Giông bão" };
    return { icon: <Sun className="text-yellow-500" />, description: "Không xác định" };
  };

  if (loading || location.loading) return (
    <div className="flex items-center justify-center h-64 gap-2">
      <Loader2 className="w-6 h-6 animate-spin" />
      <div className="text-lg">Đang tải...</div>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-4 space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {currentWeather && forecast && (
        <>
          {/* Current Weather */}
          <Card className="bg-gradient-to-br from-blue-500 to-purple-600 text-white overflow-hidden">
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="w-5 h-5" />
                    <h2 className="text-2xl font-bold">{location.name}</h2>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-6xl">{getWeatherInfo(currentWeather.weatherCode).icon}</div>
                    <div>
                      <p className="text-5xl font-bold">{Math.round(currentWeather.temperature)}°C</p>
                      <p className="text-xl">{getWeatherInfo(currentWeather.weatherCode).description}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mt-6">
                    <div className="flex items-center gap-2">
                      <Droplets className="w-5 h-5" />
                      <div>
                        <p className="text-sm opacity-80">Độ ẩm</p>
                        <p className="font-bold">{currentWeather.humidity}%</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Wind className="w-5 h-5" />
                      <div>
                        <p className="text-sm opacity-80">Gió</p>
                        <p className="font-bold">{Math.round(currentWeather.windSpeed)} km/h</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="h-48">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={forecast.hourly.slice(0, 12)} margin={{ top: 5, right: 20, left: 0, bottom: 5 }}>
                      <XAxis dataKey="time" stroke="#fff" />
                      <YAxis stroke="#fff" />
                      <Tooltip />
                      <Line type="monotone" dataKey="temperature" stroke="#fff" strokeWidth={2} dot={false} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </Card>

          {/* Daily Forecast */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            {forecast.daily.map((day, index) => (
              <Card key={index} className="p-4">
                <div className="flex flex-col items-center">
                  <p className="font-medium text-gray-600">{day.date}</p>
                  <div className="my-2">
                    {React.cloneElement(getWeatherInfo(day.weatherCode).icon, { size: 32 })}
                  </div>
                  <div className="flex gap-2 text-sm">
                    <span className="font-bold">{day.maxTemp}°</span>
                    <span className="text-gray-500">{day.minTemp}°</span>
                  </div>
                  <div className="mt-2 flex items-center gap-1 text-sm text-gray-500">
                    <Droplets size={14} />
                    <span>{day.precipitation}%</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default WeatherDashboard;