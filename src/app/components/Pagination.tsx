import React from "react";
import { 
  ChevronLeft, 
  ChevronRight, 
  MoreHorizontal, 
  ChevronsLeft, 
  ChevronsRight,
  FileText 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

interface PaginationInfo {
  totalDocs: number;
  limit: number;
  page: number;
  totalPages: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  viewMode: string;
}

interface CustomPaginationProps extends Partial<PaginationInfo> {
  onPageChange: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  className?: string;
  showPageSize?: boolean;
  pageSizeOptions?: number[];
}

const CustomPagination = ({
  totalDocs = 0,
  viewMode,
  limit = 50,
  page = 1,
  totalPages = 1,
  hasPrevPage = false,
  hasNextPage = false,
  onPageChange,
  onLimitChange,
  className = "",
  showPageSize = true,
  pageSizeOptions = [10, 20, 50, 100, 200],
}: CustomPaginationProps) => {
  const getPageNumbers = () => {
    if (!totalPages || totalPages <= 1) return [1];
    
    const delta = 1; // Show 1 page on each side of current page
    const pages = [];
    
    // Always show first page
    pages.push(1);
    
    // Calculate range around current page
    const start = Math.max(2, page - delta);
    const end = Math.min(totalPages - 1, page + delta);
    
    // Add ellipsis after first page if needed
    if (start > 2) {
      pages.push("...");
    }
    
    // Add pages around current page
    for (let i = start; i <= end; i++) {
      if (i !== 1 && i !== totalPages) {
        pages.push(i);
      }
    }
    
    // Add ellipsis before last page if needed
    if (end < totalPages - 1) {
      pages.push("...");
    }
    
    // Always show last page (if it's different from first)
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  // Calculate current range
  const startItem = totalDocs === 0 ? 0 : (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, totalDocs);

  if (totalPages <= 1 && !showPageSize && totalDocs <= limit) return null;

  return (
    <div className={`sticky bottom-0 w-full z-10 ${className}`}>
      <div className="bg-background/95 backdrop-blur-md border-t border-border/50 shadow-2xl">
        <div className="px-4 py-3">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-2 sm:gap-4">

            {/* Left - Info */}
            <div className="flex items-center gap-2 flex-wrap">
              {totalDocs > 0 && (
                <span className="text-xs sm:text-sm text-muted-foreground whitespace-nowrap">
                  <span className="hidden sm:inline">Showing </span>
                  <span className="font-semibold text-foreground">{startItem}-{endItem}</span>
                  <span className="hidden sm:inline"> of </span>
                  <span className="sm:hidden">/</span>
                  <span className="font-semibold text-foreground">{totalDocs}</span>
                </span>
              )}
              
              {/* Items per page */}
              {showPageSize && onLimitChange && (
                <Select
                  value={limit.toString()}
                  onValueChange={(value) => onLimitChange(Number(value))}
                >
                  <SelectTrigger className="w-22 sm:w-22 h-7 sm:h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {pageSizeOptions.map((size) => (
                      <SelectItem key={size} value={size.toString()}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Right - Pagination Controls */}
            {totalPages > 1 && (
              <div className="flex items-center gap-1">
                {/* Page indicator */}
                <span className="text-xs text-muted-foreground mr-1 sm:mr-2 whitespace-nowrap">
                  {page}/{totalPages}
                </span>

                {/* Navigation buttons */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(1)}
                  disabled={!hasPrevPage}
                  className="h-7 w-7 sm:h-8 sm:w-8 p-0 hidden sm:flex"
                  title="First"
                >
                  <ChevronsLeft className="h-3 w-3" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(page - 1)}
                  disabled={!hasPrevPage}
                  className="h-7 w-7 sm:h-8 sm:w-8 p-0"
                  title="Previous"
                >
                  <ChevronLeft className="h-3 w-3" />
                </Button>

                {/* Page numbers */}
                <div className="hidden lg:flex items-center gap-1 mx-1">
                  {getPageNumbers().slice(0, 5).map((pageNumber, index) => (
                    <Button
                      key={index}
                      variant={page === pageNumber ? "default" : "ghost"}
                      size="sm"
                      disabled={pageNumber === "..."}
                      onClick={() =>
                        pageNumber !== "..." && onPageChange(Number(pageNumber))
                      }
                      className="h-7 w-7 sm:h-8 sm:w-8 p-0 text-xs"
                    >
                      {pageNumber === "..." ? "..." : pageNumber}
                    </Button>
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(page + 1)}
                  disabled={!hasNextPage}
                  className="h-7 w-7 sm:h-8 sm:w-8 p-0"
                  title="Next"
                >
                  <ChevronRight className="h-3 w-3" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange(totalPages)}
                  disabled={!hasNextPage}
                  className="h-7 w-7 sm:h-8 sm:w-8 p-0 hidden sm:flex"
                  title="Last"
                >
                  <ChevronsRight className="h-3 w-3" />
                </Button>
              </div>
            )}
            </div>
        </div>
      </div>
    </div>
  );
};

export default CustomPagination;
