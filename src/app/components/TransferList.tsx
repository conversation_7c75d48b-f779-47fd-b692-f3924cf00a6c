"use client";

import React, { useState } from "react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight } from "lucide-react";


interface TransferListProps {
  available: any[];
  selected: any[];
  onTransfer: (selected: any[], direction: 'left' | 'right') => void;
}
export const TransferList: React.FC<TransferListProps> = ({
  available,
  selected,
  onTransfer,
}) => {
  const [availableSearch, setAvailableSearch] = useState("");
  const [selectedSearch, setSelectedSearch] = useState("");
  const [availableSelected, setAvailableSelected] = useState<string[]>([]);
  const [selectedSelected, setSelectedSelected] = useState<string[]>([]);

  const filteredAvailable = available.filter((item) =>
    item.name.toLowerCase().includes(availableSearch.toLowerCase())
  );

  const filteredSelected = selected.filter((item) =>
    item.name.toLowerCase().includes(selectedSearch.toLowerCase())
  );

  const handleTransfer = (direction: "left" | "right") => {
    if (direction === "right") {
      const itemsToTransfer = available.filter((item) =>
        availableSelected.includes(item.id)
      );
      onTransfer(itemsToTransfer, "right");
      setAvailableSelected([]);
    } else {
      const itemsToTransfer = selected.filter((item) =>
        selectedSelected.includes(item.id)
      );
      onTransfer(itemsToTransfer, "left");
      setSelectedSelected([]);
    }
  };

  const ListBox: React.FC<{
    items: any[];
    selected: string[];
    setSelected: (ids: string[]) => void;
    searchValue: string;
    onSearchChange: (value: string) => void;
    placeholder: string;
  }> = ({
    items,
    selected,
    setSelected,
    searchValue,
    onSearchChange,
    placeholder,
  }) => (
    <Command className="h-[320px] rounded-md border">
      <CommandInput
        placeholder={placeholder}
        value={searchValue}
        onValueChange={onSearchChange}
      />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup>
          {items.map((item) => (
            <CommandItem
              key={item.id}
              onSelect={() => {
                setSelected(
                  selected.includes(item.id)
                    ? selected.filter((id) => id !== item.id)
                    : [...selected, item.id]
                );
              }}
              className={`${
                selected.includes(item.id) ? "bg-blue-50" : ""
              } cursor-pointer`}
            >
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={selected.includes(item.id)}
                  onChange={() => {}}
                  className="h-4 w-4"
                />
                <span>{item.name}</span>
              </div>
            </CommandItem>
          ))}
        </CommandGroup>
      </CommandList>
    </Command>
  );

  return (
    <div className="flex gap-4 items-center">
      <div className="flex-1">
        <div className="mb-2 font-medium">Available Permissions</div>
        <ListBox
          items={filteredAvailable}
          selected={availableSelected}
          setSelected={setAvailableSelected}
          searchValue={availableSearch}
          onSearchChange={setAvailableSearch}
          placeholder="Search available..."
        />
      </div>

      <div className="flex flex-col gap-2">
        <Button
          variant="outline"
          size="icon"
          onClick={() => handleTransfer("right")}
          disabled={availableSelected.length === 0}
        >
          <ArrowRight className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handleTransfer("left")}
          disabled={selectedSelected.length === 0}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex-1">
        <div className="mb-2 font-medium">Selected Permissions</div>
        <ListBox
          items={filteredSelected}
          selected={selectedSelected}
          setSelected={setSelectedSelected}
          searchValue={selectedSearch}
          onSearchChange={setSelectedSearch}
          placeholder="Search selected..."
        />
      </div>
    </div>
  );
};
