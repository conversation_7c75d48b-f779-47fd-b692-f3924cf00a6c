"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CodePush } from "./types";

interface CodePushDeleteConfirmProps {
  codePush: CodePush | null;
  open: boolean;
  onClose: () => void;
  onConfirmDelete: (codePushId: string) => Promise<void>;
}

const CodePushDeleteConfirm = ({
  codePush,
  open,
  onClose,
  onConfirmDelete
}: CodePushDeleteConfirmProps) => {
  const [inputRelease, setInputRelease] = useState('');
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!codePush) return;
    
    if (inputRelease !== codePush.release) {
      setError('Release version không chính xác. Vui lòng kiểm tra lại.');
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirmDelete(codePush.id);
      onClose();
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa code push. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setInputRelease('');
    setError('');
    onClose();
  };

  if (!codePush) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Xác nhận xóa code push</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <p className="mb-2">
              Để xóa code push cho app "<span className="font-medium">{codePush.appType}</span>", 
              vui lòng nhập Release version sau để xác nhận:
            </p>
            <p className="font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded select-all">
              {codePush.release}
            </p>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
            <div className="text-sm">
              <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">
                Code Push Details:
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                App Type: <span className="font-medium">{codePush.appType}</span>
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Target Version: <span className="font-medium">{codePush.targetVersion}</span>
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Status: <span className="font-medium">{codePush.status}</span>
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Mandatory: <span className="font-medium">{codePush.mandatory ? 'Yes' : 'No'}</span>
              </p>
              {codePush.activeDevices > 0 && (
                <p className="text-amber-600 dark:text-amber-400 mt-2">
                  ⚠️ Lưu ý: Code push này đang được sử dụng bởi {codePush.activeDevices} thiết bị.
                </p>
              )}
            </div>
          </div>

          <Input
            value={inputRelease}
            onChange={(e) => {
              setInputRelease(e.target.value);
              setError('');
            }}
            placeholder="Nhập release version để xác nhận"
            disabled={isDeleting}
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              type="submit"
              variant="destructive"
              disabled={!inputRelease || isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : 'Xác nhận xóa'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CodePushDeleteConfirm;
