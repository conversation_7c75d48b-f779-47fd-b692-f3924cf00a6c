import { Metadata } from "next";
import CodePushManagement from "./CodePushManagement";
import PermissionGuard from "@/components/PermissionGuard";

export const metadata: Metadata = {
  title: "Code Push",
  openGraph: {
    title: "Code Push",
  },
};

export default function Page() {
  return (
    <PermissionGuard
      // requiredPermissions={[
      //   { resource: 'codePush', action: 'get' }
      // ]}
      // fallbackToLegacy={true}
      showAccessDenied={true}
    >
      <CodePushManagement />
    </PermissionGuard>
  );
}
