"use client";

import React, { use, useEffect, useState } from "react";
import {
  <PERSON>ci<PERSON>,
  Trash2,
  Plus,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  Pause,
  Shield,
  ShieldCheck,
  Package,
  Search,
  Smartphone,
  RotateCcw,
} from "lucide-react";
import { ViewToggle, VIEW_TOGGLE_PRESETS } from "@/app/components/view-toggle";
import { useCodePushViewMode, ViewMode } from "@/hooks/useViewMode";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import codepushApiRequests from "@/apiRequests/admin/codepush";
import { formatDate } from "@/lib/moment";

import { Label } from "@/components/ui/label";
import Loading from "@/app/components/Loading";
import CustomPagination from "@/app/components/Pagination";
import CodePushDeleteConfirm from "./CodePushDeleteConfirm";
import {DEVICE_TYPE} from "@/app/(DashboardLayout)/devices-manager/types";
import {clientUserInfo} from "@/lib/http";
import {useToast} from "@/hooks/use-toast";
import {CodePush} from "@/app/(DashboardLayout)/code-push/types";

const deviceTypeOptions = Object.keys(DEVICE_TYPE).map((key) => key);

// Status helper functions
const getStatusConfig = (status: string) => {
  const statusLower = status?.toLowerCase() || '';

  switch (statusLower) {
    case 'active':
    case 'deployed':
    case 'live':
      return {
        icon: CheckCircle,
        className: "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400",
        label: "Active"
      };
    case 'pending':
    case 'deploying':
      return {
        icon: Clock,
        className: "bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400",
        label: "Pending"
      };
    case 'failed':
    case 'error':
      return {
        icon: XCircle,
        className: "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400",
        label: "Failed"
      };
    case 'paused':
    case 'stopped':
      return {
        icon: Pause,
        className: "bg-slate-100 text-slate-700 dark:bg-slate-900/20 dark:text-slate-400",
        label: "Paused"
      };
    case 'warning':
      return {
        icon: AlertTriangle,
        className: "bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400",
        label: "Warning"
      };
    default:
      return {
        icon: Clock,
        className: "bg-muted text-muted-foreground",
        label: status || "Unknown"
      };
  }
};

const StatusBadge = ({ status }: { status: string }) => {
  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium ${config.className}`}>
      <Icon className="w-3 h-3" />
      {config.label}
    </span>
  );
};

const MandatoryBadge = ({ mandatory }: { mandatory: boolean }) => {
  if (!mandatory) return null;

  return (
    <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
      <ShieldCheck className="w-3 h-3" />
      Required
    </span>
  );
};

const CodePushManagement = () => {
  const { toast } = useToast();
  const [codepush, setCodePush] = useState<CodePush[]>([]);
  const { viewMode, setViewMode } = useCodePushViewMode();

  // const [alertMessage, setAlertMessage] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [hasPrevPage, setHasPrevPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingCodePush, setDeletingCodePush] = useState<CodePush | null>(null);
  const { userInfo } = clientUserInfo;
  const orgId = userInfo?.user?.orgId;

  useEffect(() => {
    const fetchCodePush = async () => {
      const response = await codepushApiRequests.getListCodePush(page, limit);
      response?.payload?.docs && setCodePush(response.payload.docs) && setTotal(response.payload.docs.length);
      response?.payload?.hasNextPage && setHasNextPage(response.payload.hasNextPage);
      response?.payload?.hasPrevPage && setHasPrevPage(response.payload.hasPrevPage);
      response?.payload?.totalPages && setTotalPages(response.payload.totalPages);
      setLoading(false);
    };

    fetchCodePush();
  }, [page, limit]);

  const handleDeleteClick = (codePush: CodePush) => {
    setDeletingCodePush(codePush);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async (codePushId: string) => {
    try {
      await codepushApiRequests.deleteCodePush(codePushId);
      setCodePush(codepush.filter((cp) => cp.id !== codePushId));
      setIsDeleteDialogOpen(false);
      setDeletingCodePush(null);
      toast({
        title: "Success",
        description: "Code push deleted successfully",
      });
    } catch (error) {
      console.log("Error deleting code push:", error);
      toast({
        title: "Error",
        description: "Failed to delete code push",
        variant: "destructive",
      });
      throw error; // Re-throw để CodePushDeleteConfirm có thể handle
    }
  };

  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingCodePush(null);
  };

  const TableView = () => (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="border-border/50">
            <TableHead className="font-semibold">Application</TableHead>
            <TableHead className="font-semibold">Release</TableHead>
            <TableHead className="font-semibold">Target Version</TableHead>
            <TableHead className="font-semibold">Status</TableHead>
            <TableHead className="font-semibold hidden md:table-cell">Mandatory</TableHead>
            <TableHead className="font-semibold hidden lg:table-cell">Rollbacks</TableHead>
            <TableHead className="font-semibold hidden sm:table-cell">Active Devices</TableHead>
            <TableHead className="font-semibold hidden lg:table-cell">Date</TableHead>
            <TableHead className="text-right font-semibold">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {codepush.length > 0 ? (
            codepush.map((codepush, index) => (
              <TableRow key={index} className="hover:bg-muted/30 transition-colors">
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2 min-w-0">
                    <div className="p-2 bg-primary/10 rounded-lg flex-shrink-0">
                      <Package className="h-4 w-4 text-primary" />
                    </div>
                    <span className="truncate" title={codepush.appType}>{codepush.appType}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="font-mono text-sm bg-muted px-2 py-1 rounded">
                    {codepush.release}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="text-sm text-muted-foreground">
                    {codepush.targetVersion}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <StatusBadge status={codepush.status} />
                    <div className="md:hidden">
                      <MandatoryBadge mandatory={codepush.mandatory} />
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <MandatoryBadge mandatory={codepush.mandatory} />
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  <span className="font-semibold text-purple-600 dark:text-purple-400">
                    {codepush.rollbacks || 0}
                  </span>
                </TableCell>
                <TableCell className="hidden sm:table-cell">
                  <span className="font-semibold text-blue-600 dark:text-blue-400">
                    {codepush.activeDevices || 0}
                  </span>
                </TableCell>
                <TableCell className="hidden lg:table-cell text-muted-foreground">
                  {formatDate(codepush.createdAt)}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteClick(codepush)}
                      className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={9} className="text-center py-4">
                No data available
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );

  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
      {codepush.map((codepush, index: number) => (
        <Card key={codepush.id} className="group hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border/50">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20 flex-shrink-0">
                    <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-bold text-lg text-foreground group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate" title={codepush.appType}>
                      {codepush.appType}
                    </h3>
                    <p className="text-sm text-muted-foreground truncate" title={`Release ${codepush.release}`}>
                      Release {codepush.release}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <StatusBadge status={codepush.status} />
                  <MandatoryBadge mandatory={codepush.mandatory} />
                </div>
              </div>

              {/* Version Info */}
              <div className="bg-muted/30 rounded-lg p-3 border border-border/30">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-muted-foreground">Target Version</span>
                  <span className="font-mono text-sm bg-background px-2 py-1 rounded border">
                    {codepush.targetVersion}
                  </span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
                  <div className="flex items-center gap-2 mb-1">
                    <Smartphone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Devices</span>
                  </div>
                  <p className="text-xl font-bold text-blue-700 dark:text-blue-300">
                    {codepush.activeDevices || 0}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 rounded-lg p-3 border border-purple-200/30 dark:border-purple-800/30">
                  <div className="flex items-center gap-2 mb-1">
                    <RotateCcw className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    <span className="text-xs font-medium text-purple-600 dark:text-purple-400">Rollbacks</span>
                  </div>
                  <p className="text-xl font-bold text-purple-700 dark:text-purple-300">
                    {codepush.rollbacks || 0}
                  </p>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-2 border-t border-border/30">
                <p className="text-xs text-muted-foreground">
                  {formatDate(codepush.createdAt)}
                </p>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteClick(codepush)}
                    className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="p-6 w-full">
      {/* Modern Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
            <Package className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              CodePush Management
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Deploy and manage over-the-air updates for your applications
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Search */}
          <div className="relative flex-1 sm:flex-none">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search releases..."
              className="pl-9 w-full sm:w-64 bg-background/50 backdrop-blur-sm border-border/50"
            />
          </div>

          {/* Controls Row - Compact on mobile */}
          <div className="flex items-center gap-2 justify-between sm:justify-start">
            {/* View Toggle */}
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={(mode) => setViewMode(mode as ViewMode)}
              options={VIEW_TOGGLE_PRESETS.tableGrid}
            />

            {/* Add Button */}
            <Button className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">New Release</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Active Releases</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {codepush.filter(cp => cp.status === 'active').length}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Total Devices</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {codepush.reduce((sum, cp) => sum + (cp.activeDevices || 0), 0)}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Smartphone className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-950/20 dark:to-cyan-900/30 border-cyan-200/50 dark:border-cyan-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-cyan-600 dark:text-cyan-400">Rollbacks</p>
                <p className="text-2xl font-bold text-cyan-700 dark:text-cyan-300">
                  {codepush.reduce((sum, cp) => sum + (cp.rollbacks || 0), 0)}
                </p>
              </div>
              <div className="p-3 bg-cyan-100 dark:bg-cyan-900/30 rounded-full">
                <RotateCcw className="h-6 w-6 text-cyan-600 dark:text-cyan-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/30 border-orange-200/50 dark:border-orange-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Mandatory Updates</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {codepush.filter(cp => cp.mandatory).length}
                </p>
              </div>
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <AlertTriangle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-xl">
        <CardContent className="p-0">
          {viewMode === "table" ? <TableView /> : <GridView />}
        </CardContent>
      </Card>

      {/* Pagination - Fixed at bottom center */}
      <CustomPagination
        viewMode={viewMode}
        totalDocs={total}
        limit={limit}
        page={page}
        totalPages={totalPages}
        hasPrevPage={hasPrevPage}
        hasNextPage={hasNextPage}
        onPageChange={setPage}
        onLimitChange={(value: number) => setLimit(value)}
      />

      {/* Delete Confirmation Dialog */}
      <CodePushDeleteConfirm
        codePush={deletingCodePush}
        open={isDeleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirmDelete={handleConfirmDelete}
      />
    </div>
  );
};

export default CodePushManagement;
