import React, { useState, useRef } from 'react';
import { 
  Lightbulb, 
  Zap, 
  Monitor, 
  Settings, 
  Palette, 
  Clock, 
  Shield, 
  Cpu,
  Smartphone,
  ArrowRight,
  Package,
  Layers,
  BarChart
} from 'lucide-react';

const SmartLightingSolution: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'features' | 'tech' | 'ecosystem'>('overview');
  const videoRef = useRef<HTMLVideoElement>(null);

  const renderTabs = () => {
    const tabs = [
      { 
        id: 'overview', 
        label: 'Tổng Quan', 
        icon: <Monitor className="w-5 h-5 mr-2" /> 
      },
      { 
        id: 'features', 
        label: 'T<PERSON>h <PERSON>', 
        icon: <Settings className="w-5 h-5 mr-2" /> 
      },
      { 
        id: 'tech', 
        label: 'Công Nghệ', 
        icon: <Cpu className="w-5 h-5 mr-2" /> 
      },
      { 
        id: 'ecosystem', 
        label: '<PERSON><PERSON>h<PERSON>i', 
        icon: <Layers className="w-5 h-5 mr-2" /> 
      }
    ];

    return (
      <div className="flex overflow-x-auto border-b bg-gray-100">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`
              flex items-center justify-center w-full py-4 transition 
              ${activeTab === tab.id 
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white' 
                : 'hover:bg-gray-200 text-gray-600'}
            `}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="grid md:grid-cols-2 gap-8 p-8">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
                Đèn Thông Minh Homicen Pro
              </h2>
              <p className="text-gray-700 leading-relaxed">
                Giải pháp chiếu sáng đột phá kết hợp công nghệ AI, IoT và thiết kế tinh tế. Mang đến trải nghiệm ánh sáng thông minh, tiết kiệm năng lượng và tối ưu cho từng không gian.
              </p>
              
              <div className="grid md:grid-cols-2 gap-4">
                {[
                  {
                    icon: <Zap className="text-warning" />,
                    title: "Tiết Kiệm Năng Lượng",
                    value: "Lên tới 75%"
                  },
                  {
                    icon: <Clock className="text-success" />,
                    title: "Tuổi Thọ",
                    value: "50,000 giờ"
                  },
                  {
                    icon: <Palette className="text-primary" />,
                    title: "Màu Sắc",
                    value: "16 triệu màu"
                  },
                  {
                    icon: <Shield className="text-info" />,
                    title: "Bảo Mật",
                    value: "Cao Cấp"
                  }
                ].map((stat, index) => (
                  <div 
                    key={index} 
                    className="bg-muted p-4 rounded-lg flex items-center space-x-4 hover:shadow-md transition"
                  >
                    <div className="p-3 bg-background rounded-full">{stat.icon}</div>
                    <div>
                      <p className="text-sm text-muted-foreground">{stat.title}</p>
                      <p className="font-bold text-foreground">{stat.value}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative flex items-center justify-center">
              <div className="absolute -inset-2 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full opacity-50 blur-xl"></div>
              <Lightbulb className="relative w-64 h-64 text-indigo-600 animate-pulse z-10" />
            </div>
          </div>
        );
      
      case 'features':
        return (
          <div className="p-8 grid md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-indigo-700">Tính Năng Nổi Bật</h3>
              <video 
                ref={videoRef}
                controls 
                className="w-full rounded-xl shadow-lg"
                poster="/api/placeholder/800/450"
              >
                Trình duyệt của bạn không hỗ trợ video
              </video>
              
              <div className="grid grid-cols-2 gap-4">
                <button className="bg-indigo-100 text-indigo-700 p-3 rounded-lg flex items-center justify-between hover:bg-indigo-200 transition">
                  Xem Demo <ArrowRight />
                </button>
                <button className="bg-purple-100 text-purple-700 p-3 rounded-lg flex items-center justify-between hover:bg-purple-200 transition">
                  Tải Tài Liệu <Package />
                </button>
              </div>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              {[
                {
                  icon: <Smartphone className="text-info" />,
                  title: "Điều Khiển Từ Xa",
                  description: "Quản lý toàn bộ hệ thống đèn qua ứng dụng di động"
                },
                {
                  icon: <Clock className="text-success" />,
                  title: "Lập Lịch Thông Minh",
                  description: "Tự động điều chỉnh ánh sáng theo thời gian và hoạt động"
                },
                {
                  icon: <Palette className="text-primary" />,
                  title: "Trải Nghiệm Màu Sắc",
                  description: "Tùy chỉnh không gian với 16 triệu màu sắc"
                },
                {
                  icon: <BarChart className="text-warning" />,
                  title: "Báo Cáo Năng Lượng",
                  description: "Thống kê và đánh giá mức tiêu thụ điện"
                }
              ].map((feature, index) => (
                <div 
                  key={index} 
                  className="bg-card p-5 rounded-lg hover:shadow-md transition space-y-3"
                >
                  <div className="p-3 bg-background rounded-full w-fit">{feature.icon}</div>
                  <h4 className="font-semibold text-lg text-card-foreground">{feature.title}</h4>
                  <p className="text-muted-foreground">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        );
      
      case 'tech':
        return (
          <div className="p-8 space-y-8">
            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-indigo-50 p-6 rounded-xl">
                <h4 className="text-xl font-semibold mb-4 text-indigo-700">Kết Nối Đa Giao Thức</h4>
                <div className="grid md:grid-cols-2 gap-3">
                  {[
                    "WiFi 6",
                    "Bluetooth 5.2",
                    "ZigBee Thread",
                    "Matter Protocol",
                    "HomeKit",
                    "Google Home"
                  ].map((protocol, index) => (
                    <div 
                      key={index} 
                      className="bg-white p-3 rounded text-center hover:bg-indigo-100 transition"
                    >
                      {protocol}
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="bg-purple-50 p-6 rounded-xl">
                <h4 className="text-xl font-semibold mb-4 text-purple-700">Thông Số Kỹ Thuật</h4>
                <div className="space-y-3">
                  {[
                    { label: "Công Suất", value: "9W - 15W" },
                    { label: "Độ Sáng", value: "800 - 1200 Lumens" },
                    { label: "Nhiệt Độ Màu", value: "2700K - 6500K" }
                  ].map((spec, index) => (
                    <div 
                      key={index} 
                      className="flex justify-between bg-white p-3 rounded"
                    >
                      <span className="font-medium">{spec.label}</span>
                      <span className="text-purple-600">{spec.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'ecosystem':
        return (
          <div className="p-8 space-y-8">
            <div className="text-center">
              <h3 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600 mb-4">
                Hệ Sinh Thái Kết Nối Thông Minh
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Đèn Homicen không chỉ là một thiết bị chiếu sáng, mà là một phần không thể thiếu trong hệ sinh thái nhà thông minh hiện đại.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-6">
              {[
                {
                  icon: <Cpu className="w-12 h-12 text-info" />,
                  title: "Tích Hợp Tập Trung",
                  description: "Kết nối liền mạch với các thiết bị thông minh khác"
                },
                {
                  icon: <Shield className="w-12 h-12 text-success" />,
                  title: "An Ninh Cao",
                  description: "Bảo vệ dữ liệu và quyền riêng tư của bạn"
                },
                {
                  icon: <Layers className="w-12 h-12 text-primary" />,
                  title: "Mở Rộng Dễ Dàng",
                  description: "Dễ dàng mở rộng và nâng cấp hệ thống"
                }
              ].map((item, index) => (
                <div 
                  key={index} 
                  className="bg-card p-6 rounded-xl text-center hover:shadow-lg transition"
                >
                  {item.icon}
                  <h4 className="mt-4 font-semibold text-xl text-card-foreground">{item.title}</h4>
                  <p className="mt-2 text-muted-foreground">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="container mx-auto max-w-6xl bg-white shadow-2xl rounded-3xl overflow-hidden">
        {renderTabs()}
        {renderTabContent()}
      </div>
    </div>
  );
};

export default SmartLightingSolution;