"use client";

import React, { useState } from "react";
import {
  Lightbulb,
  Thermometer,
  Leaf,
  Radio,
  Cpu,
  Zap,
  ServerCog,
  Waves,
} from "lucide-react";
import SmartLightingSolution from "./Smart-light";

interface Solution {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  applications: string[];
  benefits: string[];
}

const solutionData: Solution[] = [
  {
    id: "smart-lighting",
    title: "Đèn Thông <PERSON>",
    icon: <Lightbulb className="w-16 h-16 text-yellow-500" />,
    description:
      "Gi<PERSON>i pháp chiếu sáng thông minh tối ưu hóa năng lượng và trải nghiệm người dùng.",
    applications: [
      "Nhà ở thông minh",
      "Văn phòng",
      "Không gian công cộng",
      "Chiếu sáng đô thị",
    ],
    benefits: [
      "Tiết kiệm năng lượng lên đến 70%",
      "<PERSON><PERSON><PERSON><PERSON> khiển từ xa qua ứng dụng",
      "<PERSON><PERSON><PERSON> hợp cảm biến chuyển động",
      "Tù<PERSON> chỉnh màu sắc và độ sáng",
    ],
  },
  {
    id: "smart-switch",
    title: "Công Tắc Thông Minh",
    icon: <Zap className="w-16 h-16 text-blue-500" />,
    description:
      "Giải pháp điều khiển điện thông minh, nâng cao hiệu quả và an toàn.",
    applications: [
      "Hệ thống điện gia đình",
      "Quản lý năng lượng",
      "Tự động hóa văn phòng",
      "Giám sát từ xa",
    ],
    benefits: [
      "Điều khiển từ xa",
      "Lập lịch bật/tắt thiết bị",
      "Giám sát năng lượng tiêu thụ",
      "Tích hợp với hệ sinh thái smarthome",
    ],
  },
  {
    id: "agriculture-sensors",
    title: "Cảm Biến Môi Trường Nông Nghiệp",
    icon: <Leaf className="w-16 h-16 text-green-500" />,
    description:
      "Giải pháp giám sát và quản lý môi trường nông nghiệp chính xác.",
    applications: [
      "Nông trại thông minh",
      "Nhà kính",
      "Quản lý vùng trồng",
      "Nông nghiệp chính xác",
    ],
    benefits: [
      "Theo dõi nhiệt độ, độ ẩm",
      "Phân tích chất lượng đất",
      "Điều chỉnh tưới tiêu tự động",
      "Dự báo năng suất mùa vụ",
    ],
  },
  {
    id: "environmental-monitoring",
    title: "Giám Sát Môi Trường",
    icon: <Waves className="w-16 h-16 text-teal-500" />,
    description: "Hệ thống giám sát môi trường toàn diện và chi tiết.",
    applications: [
      "Quan trắc không khí",
      "Kiểm soát ô nhiễm",
      "Quản lý tài nguyên nước",
      "Nghiên cứu khoa học",
    ],
    benefits: [
      "Đo đạc đa chỉ số",
      "Cảnh báo ô nhiễm",
      "Dữ liệu thời gian thực",
      "Phân tích xu hướng môi trường",
    ],
  },
];

const solutionDetails: Record<string, React.ReactNode> = {
  "smart-lighting": <SmartLightingSolution />,
  "smart-switch": <SmartLightingSolution />,
  "agriculture-sensors": <SmartLightingSolution />,
  "environmental-monitoring": <SmartLightingSolution />,
};

const SolutionDetailPage: React.FC = () => {
  const [activeSolution, setActiveSolution] = useState<any>(solutionData[0]);
  const [selectedSolution, setSelectedSolution] = useState<any>(
    <SmartLightingSolution />
  );

  return (
    <div className="min-h-screen bg-background w-full">
      {/* Header-style Navigation */}
      <div className="bg-card shadow-md">
        <div className="container mx-auto flex items-center justify-between px-4 py-3">
          <h2 className="text-xl font-bold text-card-foreground">
            Giải Pháp IoT
          </h2>
          <div className="flex space-x-2">
            {solutionData.map((solution) => (
              <button
                key={solution.id}
                onClick={() => {
                  setActiveSolution(solution);
                  setSelectedSolution(solutionDetails[solution.id]);
                }}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition ${
                  activeSolution.id === solution.id
                    ? "bg-accent text-accent-foreground"
                    : "hover:bg-muted"
                }`}
              >
                {solution.icon}
                <span className="text-sm font-medium">{solution.title}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Solution Details */}
      <div className="container mx-auto px-4 py-6">
        <div className="bg-white dark:bg-neutral-800 rounded-xl shadow-lg p-6">
          {selectedSolution}
        </div>
      </div>
    </div>
  );
};

export default SolutionDetailPage;
