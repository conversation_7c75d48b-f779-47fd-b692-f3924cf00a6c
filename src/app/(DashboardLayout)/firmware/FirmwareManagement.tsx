"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Trash2,
  Plus,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  Pause,
  Shield,
  ShieldCheck,
  HardDrive,
  Cpu,
  Search,
  Smartphone,
  Download,
  FileText,
} from "lucide-react";
import { ViewToggle, VIEW_TOGGLE_PRESETS } from "@/app/components/view-toggle";
import { useFirmwareViewMode, ViewMode } from "@/hooks/useViewMode";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import filesApiRequests from "@/apiRequests/admin/fileUploadService";
import firmwaresApiRequests from "@/apiRequests/admin/firmwares";
import { formatDate } from "@/lib/moment";

import { Firmware } from "./types";
import { Label } from "@/components/ui/label";
import Loading from "@/app/components/Loading";
import CustomPagination from "@/app/components/Pagination";
import FirmwareDeleteConfirm from "./FirmwareDeleteConfirm";
import {DEVICE_TYPE} from "@/app/(DashboardLayout)/devices-manager/types";
import {clientUserInfo} from "@/lib/http";
import {isEmpty, isNil, isNumber, omitBy} from "lodash";
import get from "lodash/get";
import {toast, useToast} from "@/hooks/use-toast";
import {ToastAction} from "@/components/ui/toast";

const deviceTypeOptions = Object.keys(DEVICE_TYPE).map((key) => key);

// Status helper functions for firmware
const getFirmwareStatusConfig = (status: string) => {
  const statusLower = status?.toLowerCase() || '';

  switch (statusLower) {
    case 'active':
    case 'deployed':
    case 'stable':
      return {
        icon: CheckCircle,
        className: "bg-primary/10 text-primary border border-primary/20",
        label: "Active"
      };
    case 'beta':
    case 'testing':
      return {
        icon: Clock,
        className: "bg-warning/10 text-warning border border-warning/20",
        label: "Beta"
      };
    case 'deprecated':
    case 'outdated':
      return {
        icon: XCircle,
        className: "bg-destructive/10 text-destructive border border-destructive/20",
        label: "Deprecated"
      };
    case 'draft':
    case 'pending':
      return {
        icon: Pause,
        className: "bg-muted text-muted-foreground border border-muted",
        label: "Draft"
      };
    case 'critical':
    case 'security':
      return {
        icon: Shield,
        className: "bg-warning/10 text-warning border border-warning/20",
        label: "Critical"
      };
    default:
      return {
        icon: CheckCircle,
        className: "bg-primary/10 text-primary border border-primary/20",
        label: "Active"
      };
  }
};

const FirmwareStatusBadge = ({ status }: { status: string }) => {
  const config = getFirmwareStatusConfig(status);
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium ${config.className}`}>
      <Icon className="w-3 h-3" />
      {config.label}
    </span>
  );
};

const DeviceTypeBadge = ({ deviceType }: { deviceType: string }) => {
  return (
    <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
      <HardDrive className="w-3 h-3" />
      {deviceType}
    </span>
  );
};

const FirmwareManagement = () => {
  const { toast } = useToast();

  const [firmwares, setFirmwares] = useState<Firmware[]>([]);
  const { viewMode, setViewMode } = useFirmwareViewMode();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [deletingFirmware, setDeletingFirmware] = useState<Firmware | null>(null);
  const [formData, setFormData] = useState<Partial<Firmware>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [hasPrevPage, setHasPrevPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);
  const { userInfo } = clientUserInfo;
  const orgId = userInfo?.user?.orgId;

  useEffect(() => {
    const fetchFirmwares = async () => {
      const response = await firmwaresApiRequests.getFirmwares(page, limit);
      response?.payload?.docs && setFirmwares(response.payload.docs) && setTotal(response.payload.docs.length);
      response?.payload?.hasNextPage && setHasNextPage(response.payload.hasNextPage);
      response?.payload?.hasPrevPage && setHasPrevPage(response.payload.hasPrevPage);
      response?.payload?.totalPages && setTotalPages(response.payload.totalPages);
      setLoading(false);
    };

    fetchFirmwares();
  }, [page, limit, isLoading]);

  useEffect(() => {
    if (!isAddDialogOpen && !isEditDialogOpen && !isDeleteDialogOpen) {
      setEditIndex(null);
      setFile(null);
      setFormData({});
      setDeletingFirmware(null);
    }
  }, [isAddDialogOpen, isEditDialogOpen, isDeleteDialogOpen]);

  const errorToast = (alertMessage: string) => toast({
    variant: "destructive",
    description: alertMessage,
  });

  const successToast = (alertMessage: string) => toast({
    description: alertMessage,
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (file.size > 10 * 1024 * 1024) {
        errorToast("File size must be less than 10MB");
        return;
      }
      setFile(file);
    }
  };

  const handleAdd = async () => {
    if (!file || !formData.name || !formData.version || !formData.deviceType) {
      console.log(formData);
      errorToast("Please fill in all fields");
      return;
    }

    const response = await filesApiRequests.uploadFile(file);
    const fileId = get(response, "file.id");
    await firmwaresApiRequests.createFirmware({
      ...formData,
      fileId,
    });

    setFormData({});
    setIsAddDialogOpen(false);
    setIsLoading(!isLoading);
    successToast("Firmware added successfully");
  };

  const handleEdit = async () => {
    let fileId: string | undefined = undefined;
    if (file) {
      const response = await filesApiRequests.uploadFile(file);
      fileId = get(response, "file.id");
    }

    if (!isNumber(editIndex)) {
      errorToast("No firmware selected for editing");
      return;
    }

    await firmwaresApiRequests.updateFirmware(orgId, get(firmwares[editIndex], 'id'), omitBy({
      name: formData.name,
      version: formData.version,
      deviceType: formData.deviceType,
      description: formData.description,
      fileId,
    }, isNil));

    setIsLoading(!isLoading);
    setFormData({});
    setIsEditDialogOpen(false);
    successToast("Firmware updated successfully");
  };

  const handleDeleteClick = (firmware: Firmware) => {
    setDeletingFirmware(firmware);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async (firmwareId: string) => {
    try {
      await firmwaresApiRequests.deleteFirmware(firmwareId);
      setFirmwares(firmwares.filter((fw) => fw.id !== firmwareId));
      setIsDeleteDialogOpen(false);
      setDeletingFirmware(null);
      successToast("Firmware deleted successfully");
    } catch (error) {
      console.log("Error deleting firmware:", error);
      throw error; // Re-throw để FirmwareDeleteConfirm có thể handle
    }
  };

  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingFirmware(null);
  };

  const openEditDialog = (index: number) => {
    setEditIndex(index);
    setFormData(firmwares[index]);
    setIsEditDialogOpen(true);
  };

  const TableView = () => (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="border-border/50">
            <TableHead className="font-semibold">Name</TableHead>
            <TableHead className="font-semibold">Version</TableHead>
            <TableHead className="font-semibold hidden md:table-cell">Description</TableHead>
            <TableHead className="font-semibold">Device Type</TableHead>
            <TableHead className="font-semibold hidden lg:table-cell">Upload Date</TableHead>
            <TableHead className="font-semibold hidden sm:table-cell">Status</TableHead>
            <TableHead className="text-right font-semibold">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {firmwares.length > 0 ? (
            firmwares.map((firmware, index) => (
              <TableRow key={index} className="hover:bg-muted/30 transition-colors">
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2 min-w-0">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex-shrink-0">
                      <HardDrive className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="truncate" title={firmware.name}>{firmware.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="font-mono text-sm bg-muted px-2 py-1 rounded">
                    {firmware.version}
                  </span>
                </TableCell>
                <TableCell className="hidden md:table-cell text-muted-foreground max-w-xs">
                  <span className="truncate block" title={firmware.description}>{firmware.description}</span>
                </TableCell>
                <TableCell>
                  <DeviceTypeBadge deviceType={firmware.deviceType} />
                </TableCell>
                <TableCell className="hidden lg:table-cell text-muted-foreground">
                  {formatDate(firmware.createdAt)}
                </TableCell>
                <TableCell className="hidden sm:table-cell">
                  <FirmwareStatusBadge status="Active" />
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openEditDialog(index)}
                      className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteClick(firmware)}
                      className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                No firmware found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );

  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {firmwares.map((firmware, index: number) => (
        <Card key={firmware.id} className="group hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border/50">
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20 flex-shrink-0">
                    <HardDrive className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-bold text-lg text-foreground group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate" title={firmware.name}>
                      {firmware.name}
                    </h3>
                    <p className="text-sm text-muted-foreground truncate" title={`Version ${firmware.version}`}>
                      Version {firmware.version}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <FirmwareStatusBadge status="Active" />
                  <DeviceTypeBadge deviceType={firmware.deviceType} />
                </div>
              </div>

              {/* Description */}
              {firmware.description && (
                <div className="bg-muted/30 rounded-lg p-3 border border-border/30">
                  <p className="text-sm text-muted-foreground overflow-hidden" style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical'
                  }} title={firmware.description}>
                    {firmware.description}
                  </p>
                </div>
              )}

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
                  <div className="flex items-center gap-2 mb-1">
                    <Cpu className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Version</span>
                  </div>
                  <p className="text-lg font-bold text-blue-700 dark:text-blue-300">
                    {firmware.version}
                  </p>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 rounded-lg p-3 border border-purple-200/30 dark:border-purple-800/30">
                  <div className="flex items-center gap-2 mb-1">
                    <Smartphone className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    <span className="text-xs font-medium text-purple-600 dark:text-purple-400">Device</span>
                  </div>
                  <p className="text-sm font-bold text-purple-700 dark:text-purple-300 truncate">
                    {firmware.deviceType}
                  </p>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-2 border-t border-border/30">
                <p className="text-xs text-muted-foreground">
                  {formatDate(firmware.createdAt)}
                </p>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => openEditDialog(index)}
                    className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteClick(firmware)}
                    className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="p-6 w-full">
      {/* Modern Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
            <HardDrive className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Firmware Management
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Manage and deploy firmware updates for your IoT devices
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Search */}
          <div className="relative flex-1 sm:flex-none">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search firmware..."
              className="pl-9 w-full sm:w-64 bg-background/50 backdrop-blur-sm border-border/50"
            />
          </div>

          {/* Controls Row - Compact on mobile */}
          <div className="flex items-center gap-2 justify-between sm:justify-start">
            {/* View Toggle */}
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={(mode) => setViewMode(mode as ViewMode)}
              options={VIEW_TOGGLE_PRESETS.tableGrid}
            />

            {/* Add Button */}
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">New Firmware</span>
                  <span className="sm:hidden">Add</span>
                </Button>
              </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Add New Firmware</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 py-4">
                <div className="space-y-2">
                  <Label htmlFor="new-firmware-name">
                    Firmware Name
                    <span className="text-destructive ml-1">*</span>
                  </Label>
                  <Input
                    id="new-firmware-name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                  />
                  <p className="text-sm text-muted-foreground">
                    Enter a unique name for your firmware
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new-version">
                    Version
                    <span className="text-destructive ml-1">*</span>
                  </Label>
                  <Input
                    id="new-version"
                    value={formData.version}
                    onChange={(e) =>
                      setFormData({ ...formData, version: e.target.value })
                    }
                  />
                  <p className="text-sm text-muted-foreground">
                    Use semantic versioning (e.g., 1.0.0)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new-description">Description</Label>
                  <Input
                    id="new-description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        description: e.target.value,
                      })
                    }
                  />
                  <p className="text-sm text-muted-foreground">
                    Add a description of your firmware's features
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new-device-type">
                    Device Type
                    <span className="text-destructive ml-1">*</span>
                  </Label>
                  <Select
                    value={formData.deviceType}
                    onValueChange={(value) =>
                      setFormData({ ...formData, deviceType: value })
                    }
                  >
                    <SelectTrigger id="new-device-type">
                      <SelectValue placeholder="Select Device Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {deviceTypeOptions.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Choose the target device type
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new-firmware-file">
                    Firmware File
                    <span className="text-destructive ml-1">*</span>
                  </Label>
                  <Input
                    id="new-firmware-file"
                    type="file"
                    accept=".bin,.hex,.fw,.png"
                    onChange={handleFileChange}
                  />
                  <p className="text-sm text-muted-foreground">
                    Supported formats: .bin, .hex, .fw, .png
                  </p>
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleAdd}>Add Firmware</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Firmware</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {firmwares.length}
                </p>
              </div>
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <HardDrive className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Device Types</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {new Set(firmwares.map(fw => fw.deviceType)).size}
                </p>
              </div>
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Smartphone className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-950/20 dark:to-cyan-900/30 border-cyan-200/50 dark:border-cyan-800/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-cyan-600 dark:text-cyan-400">Latest Version</p>
                <p className="text-2xl font-bold text-cyan-700 dark:text-cyan-300">
                  {firmwares.length > 0 ? firmwares[0]?.version || "N/A" : "N/A"}
                </p>
              </div>
              <div className="p-2 bg-cyan-100 dark:bg-cyan-900/30 rounded-full">
                <Cpu className="h-5 w-5 text-cyan-600 dark:text-cyan-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950/20 dark:to-indigo-900/30 border-indigo-200/50 dark:border-indigo-800/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-indigo-600 dark:text-indigo-400">Total Downloads</p>
                <p className="text-2xl font-bold text-indigo-700 dark:text-indigo-300">
                  {firmwares.reduce((sum, fw) => sum + ((fw as any).downloadCount || 0), 0)}
                </p>
              </div>
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
                <Download className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-xl">
        <CardContent className="p-6 pb-24">
          {viewMode === "table" ? <TableView /> : <GridView />}
        </CardContent>
      </Card>

      {/* Pagination */}
      <CustomPagination
        viewMode={viewMode}
        totalDocs={total}
        limit={limit}
        page={page}
        totalPages={totalPages}
        hasPrevPage={hasPrevPage}
        hasNextPage={hasNextPage}
        onPageChange={setPage}
        onLimitChange={(value: number) => setLimit(value)}
      />

      {/* Delete Confirmation Dialog */}
      <FirmwareDeleteConfirm
        firmware={deletingFirmware}
        open={isDeleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirmDelete={handleConfirmDelete}
      />
    </div>
  );
};

export default FirmwareManagement;
