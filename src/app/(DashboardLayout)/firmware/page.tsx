import { Metadata } from "next";
import FirmwareManagement from "./FirmwareManagement";
import PermissionGuard from "@/components/PermissionGuard";

export const metadata: Metadata = {
  title: "Firmware",
  openGraph: {
    title: "Firmware",
  },
};

export default function Page() {
  return (
    <PermissionGuard
      // requiredPermissions={[
      //   { resource: 'firmware', action: 'get' }
      // ]}
      // fallbackToLegacy={true}
      showAccessDenied={true}
    >
      <FirmwareManagement />
    </PermissionGuard>
  );
}
