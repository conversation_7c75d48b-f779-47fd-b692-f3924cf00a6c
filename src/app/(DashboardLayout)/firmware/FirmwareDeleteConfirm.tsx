"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Firmware } from "./types";

interface FirmwareDeleteConfirmProps {
  firmware: Firmware | null;
  open: boolean;
  onClose: () => void;
  onConfirmDelete: (firmwareId: string) => Promise<void>;
}

const FirmwareDeleteConfirm = ({
  firmware,
  open,
  onClose,
  onConfirmDelete
}: FirmwareDeleteConfirmProps) => {
  const [inputName, setInputName] = useState('');
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!firmware) return;
    
    if (inputName !== firmware.name) {
      setError('Tên firmware không chính xác. Vui lòng kiểm tra lại.');
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirmDelete(firmware.id);
      onClose();
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa firmware. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setInputName('');
    setError('');
    onClose();
  };

  if (!firmware) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Xác nhận xóa firmware</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <p className="mb-2">
              Để xóa firmware "<span className="font-medium">{firmware.name}</span>", 
              vui lòng nhập tên firmware sau để xác nhận:
            </p>
            <p className="font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded select-all">
              {firmware.name}
            </p>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
            <div className="text-sm">
              <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">
                Firmware Details:
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Version: <span className="font-medium">{firmware.version}</span>
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Device Type: <span className="font-medium">{firmware.deviceType}</span>
              </p>
              {firmware.description && (
                <p className="text-blue-600 dark:text-blue-400">
                  Description: <span className="font-medium">{firmware.description}</span>
                </p>
              )}
            </div>
          </div>

          <Input
            value={inputName}
            onChange={(e) => {
              setInputName(e.target.value);
              setError('');
            }}
            placeholder="Nhập tên firmware để xác nhận"
            disabled={isDeleting}
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              type="submit"
              variant="destructive"
              disabled={!inputName || isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : 'Xác nhận xóa'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default FirmwareDeleteConfirm;
