/**
 * Enhanced Settings Page with integrated Role-Permission and User-Role management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Settings, 
  Key, 
  UserCheck, 
  Plus,
  Edit,
  Trash2,
  Loader2,
  ListPlus,
  Check,
  X
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Import existing components
import { UserRoleManager } from '@/components/UserRoleManager';

// Import API requests
import rolesApiRequests from '@/apiRequests/admin/roles';
import permissionsApiRequests from '@/apiRequests/admin/permissions';
import { useCurrentOrgId } from '@/hooks/useCurrentOrgId';

// Import types
import { Role } from '@/app/(DashboardLayout)/settings/roles/types';
import { Permission } from '@/app/(DashboardLayout)/settings/permissions/types';
import { User } from '@/app/(DashboardLayout)/users/types';
import rolePermissionMappingsApiRequests from '@/apiRequests/admin/rolePermissionMappings';
import userRolesMappingsApiRequests from '@/apiRequests/admin/userRolesMappings';
import { RolePermissionMapping, UserRoleMapping } from '@/types/mappings';

// Common resources and actions
const EnhancedSettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('user-roles-assignment');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const orgId = useCurrentOrgId();
  
  // Data states
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);

  const [userRoles, setUserRoles] = useState<UserRoleMapping[]>([]);


  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedRoleAssignment, setSelectedRoleAssignment] = useState<Role | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  // Permission selection states
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [updatingPermissions, setUpdatingPermissions] = useState(false);
  const [updatingPermissionIds, setUpdatingPermissionIds] = useState<Set<string>>(new Set());

  // Inline editing states
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [editingRoleName, setEditingRoleName] = useState('');
  const [editingRoleDescription, setEditingRoleDescription] = useState('');

  // Dialog states
  const [isCreateRoleDialogOpen, setIsCreateRoleDialogOpen] = useState(false);

  // Form states
  const [roleFormData, setRoleFormData] = useState({
    name: '',
    description: '',
  });

  const { toast } = useToast();

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Sync selected permissions when a role is selected for assignment
  useEffect(() => {
    if (selectedRoleAssignment && selectedRoleAssignment.permissions) {
      // Extract permission IDs from the selected role
      // Handle both simple Permission[] and RolePermissionMapping[] structures
      const rolePermissionIds = selectedRoleAssignment.permissions.map(
        (rolePermission: any) => {
          // If it's a RolePermissionMapping object (has permissionId and permission)
          if (rolePermission.permission && rolePermission.permission.id) {
            return rolePermission.permission.id;
          }
          // If it's a simple Permission object
          if (rolePermission.id) {
            return rolePermission.id;
          }
          return null;
        }
      ).filter(Boolean);
      setSelectedPermissions(rolePermissionIds);
    } else {
      setSelectedPermissions([]);
    }
  }, [selectedRoleAssignment]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load roles, permissions, and user role mappings in parallel
      const [rolesResponse, permissionsResponse, rolePermissionsResponse, userRolesResponse] = await Promise.all([
        // todo: update code to get all roles, permissions, and role-permission mappings
        rolesApiRequests.getRoles(1, 100),
        permissionsApiRequests.getPermissions(1, 100),
        rolePermissionMappingsApiRequests.getRolePermissionMappings(1, 100),
        userRolesMappingsApiRequests.getUserRolesMappings(1, 100)
      ]);

      if (rolesResponse.payload) {
        setRoles(rolesResponse.payload.docs || []);
      }
      
      if (permissionsResponse.payload) {
        setPermissions(permissionsResponse.payload.docs || []);
      }
      
      if (userRolesResponse.payload) {
        setUserRoles(userRolesResponse.payload.docs || []);
      }
    } catch (error: any) {
      toast({
        title: "Error loading data",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter data based on search term
  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Extract unique users from userRoles mappings
  const users = React.useMemo(() => {
    const uniqueUsers = new Map<string, User>();
    userRoles.forEach(mapping => {
      if (mapping.user && mapping.user.id && !uniqueUsers.has(mapping.user.id)) {
        uniqueUsers.set(mapping.user.id, mapping.user);
      }
    });
    return Array.from(uniqueUsers.values());
  }, [userRoles]);

  const filteredUsers = users.filter(user =>
    user.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredPermissions = permissions.filter(permission =>
    permission.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
    permission.action.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Role CRUD operations
  const handleCreateRole = async () => {
    try {
      const response = await rolesApiRequests.createRole(roleFormData);
      if (response.status === 201) {
        toast({
          title: "Role created",
          description: "Role has been created successfully",
        });
        setIsCreateRoleDialogOpen(false);
        setRoleFormData({ name: '', description: '' });
        loadInitialData();
      }
    } catch (error: any) {
      toast({
        title: "Error creating role",
        description: error.message,
        variant: "destructive",
      });
    }
  };



  const handleDeleteRole = async (role: Role) => {
    if (!confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
      return;
    }

    try {
      const response = await rolesApiRequests.deleteRole(role.id);
      if (response.status === 200) {
        toast({
          title: "Role deleted",
          description: "Role has been deleted successfully",
        });
        loadInitialData();
        // Clear selected role if it was deleted
        if (selectedRole?.id === role.id) {
          setSelectedRole(null);
        }
      }
    } catch (error: any) {
      toast({
        title: "Error deleting role",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Helper function to update role permissions locally
  const updateRolePermissionsLocally = (roleId: string, newPermissions: any[]) => {
    // Update the roles array
    setRoles(prevRoles => 
      prevRoles.map(role => 
        role.id === roleId 
          ? { ...role, permissions: newPermissions }
          : role
      )
    );
    
    // Update selectedRoleAssignment if it matches
    if (selectedRoleAssignment?.id === roleId) {
      setSelectedRoleAssignment(prev => 
        prev ? { ...prev, permissions: newPermissions } : null
      );
    }
  };

  // Helper function to update user roles locally
  const updateUserRolesLocally = (userId: string, roleId: string, action: 'add' | 'remove', mappingId?: string) => {
    if (action === 'add') {
      // Add new user role mapping
      const newMapping: UserRoleMapping = {
        id: mappingId || `temp-${Date.now()}`,
        userId: userId,
        roleId: roleId,
        orgId: orgId!,
        isActive: true,
        assignedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      setUserRoles(prev => [...prev, newMapping]);
    } else {
      // Remove user role mapping
      setUserRoles(prev => prev.filter(mapping => 
        !(mapping.userId === userId && mapping.roleId === roleId)
      ));
    }
  };

  // Permission selection handlers
  const handlePermissionSelect = async (permissionId: string, checked: boolean) => {
    if (!selectedRoleAssignment) {
      toast({
        title: "No role selected",
        description: "Please select a role first to manage permissions",
        variant: "destructive",
      });
      return;
    }

    try {
      setUpdatingPermissions(true);
      setUpdatingPermissionIds(prev => new Set(prev).add(permissionId));
      
      // Find the current permission being selected
      const currentPermission = permissions.find(p => p.id === permissionId);
      if (!currentPermission) return;
      
    if (checked) {
        // Add permission to role
        const newMapping = await rolePermissionMappingsApiRequests.createRolePermissionMapping({
          roleId: selectedRoleAssignment.id,
          permissionId,
          orgId: orgId!,
          isActive: true,
        });
        
        // Update local state immediately
      setSelectedPermissions(prev => [...prev, permissionId]);
        
        // Create new permission mapping object for local state
        const newPermissionMapping = {
          id: newMapping.payload?.id || `temp-${Date.now()}`,
          permissionId: permissionId,
          roleId: selectedRoleAssignment.id,
          permission: currentPermission,
          role: selectedRoleAssignment,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        // Update role permissions locally
        const updatedPermissions = [...(selectedRoleAssignment.permissions || []), newPermissionMapping];
        updateRolePermissionsLocally(selectedRoleAssignment.id, updatedPermissions);
        
        // If the selected permission has action "*" (All), auto-select all other permissions for the same resource
        if (currentPermission.action === "*") {
          const relatedPermissions = permissions.filter(p => 
            p.resource === currentPermission.resource && 
            p.action !== "*" && 
            !selectedPermissions.includes(p.id) &&
            p.id !== permissionId
          );
          
          if (relatedPermissions.length > 0) {
            // Add related permission IDs to updating set
            setUpdatingPermissionIds(prev => {
              const newSet = new Set(prev);
              relatedPermissions.forEach(p => newSet.add(p.id));
              return newSet;
            });
            
            // Add all related permissions
            const relatedPromises = relatedPermissions.map(permission =>
              rolePermissionMappingsApiRequests.createRolePermissionMapping({
                roleId: selectedRoleAssignment.id,
                permissionId: permission.id,
                orgId: orgId!,
                isActive: true,
              })
            );
            
            const relatedResults = await Promise.all(relatedPromises);
            
            // Update UI state
            setSelectedPermissions(prev => [
              ...prev, 
              ...relatedPermissions.map(p => p.id)
            ]);
            
            // Create new permission mapping objects for related permissions
            const relatedMappings = relatedPermissions.map((permission, index) => ({
              id: relatedResults[index]?.payload?.id || `temp-${Date.now()}-${index}`,
              permissionId: permission.id,
              roleId: selectedRoleAssignment.id,
              permission: permission,
              role: selectedRoleAssignment,
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }));
            
            // Update role permissions locally with all new mappings
            const allNewPermissions = [...updatedPermissions, ...relatedMappings];
            updateRolePermissionsLocally(selectedRoleAssignment.id, allNewPermissions);
            
            toast({
              title: "All permissions added",
              description: `Added "*" permission and ${relatedPermissions.length} specific permissions for ${currentPermission.resource}`,
            });
    } else {
            toast({
              title: "Permission added",
              description: "Permission has been successfully added to the role",
            });
          }
        } else {
          // If selecting a specific permission, check if all specific permissions for this resource are now selected
          const allSpecificPermissions = permissions.filter(p => 
            p.resource === currentPermission.resource && p.action !== "*"
          );
          
          const selectedSpecificPermissions = allSpecificPermissions.filter(p => 
            selectedPermissions.includes(p.id) || p.id === permissionId
          );
          
          // If all specific permissions are now selected, auto-select the "*" permission
          if (selectedSpecificPermissions.length === allSpecificPermissions.length && allSpecificPermissions.length > 0) {
            const allPermission = permissions.find(p => 
              p.resource === currentPermission.resource && p.action === "*"
            );
            
            if (allPermission && !selectedPermissions.includes(allPermission.id)) {
              // Add "*" permission ID to updating set
              setUpdatingPermissionIds(prev => new Set(prev).add(allPermission.id));
              
              const allMappingResult = await rolePermissionMappingsApiRequests.createRolePermissionMapping({
                roleId: selectedRoleAssignment.id,
                permissionId: allPermission.id,
                orgId: orgId!,
                isActive: true,
              });
              
              setSelectedPermissions(prev => [...prev, allPermission.id]);
              
              // Create new permission mapping object for "*" permission
              const allPermissionMapping = {
                id: allMappingResult.payload?.id || `temp-${Date.now()}-all`,
                permissionId: allPermission.id,
                roleId: selectedRoleAssignment.id,
                permission: allPermission,
                role: selectedRoleAssignment,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              };
              
              // Update role permissions locally
              const finalPermissions = [...updatedPermissions, allPermissionMapping];
              updateRolePermissionsLocally(selectedRoleAssignment.id, finalPermissions);
              
              toast({
                title: "All permissions completed",
                description: `Added permission and auto-selected "*" permission for ${currentPermission.resource}`,
              });
    } else {
              toast({
                title: "Permission added",
                description: "Permission has been successfully added to the role",
              });
            }
          } else {
            toast({
              title: "Permission added",
              description: "Permission has been successfully added to the role",
            });
          }
        }
      } else {
        // Remove permission from role
        // First find the mapping to delete
        const rolePermissionMapping = selectedRoleAssignment.permissions?.find(
          (rp: any) => {
            // Handle RolePermissionMapping structure
            if (rp.permission && rp.permission.id) {
              return rp.permission.id === permissionId;
            }
            // Handle simple Permission structure
            if (rp.id) {
              return rp.id === permissionId;
            }
            return false;
          }
        );
        
        if (rolePermissionMapping) {
          await rolePermissionMappingsApiRequests.deleteRolePermissionMapping(
            rolePermissionMapping.id
          );
          
          // Update local state immediately
          setSelectedPermissions(prev => prev.filter(id => id !== permissionId));
          
          // Remove from role permissions locally
          let updatedPermissions = (selectedRoleAssignment.permissions || []).filter(
            (rp: any) => {
              const rpPermissionId = rp.permission?.id || rp.id;
              return rpPermissionId !== permissionId;
            }
          );
          
          // If the deselected permission has action "*" (All), auto-deselect all other permissions for the same resource
          if (currentPermission.action === "*") {
            const relatedPermissions = permissions.filter(p => 
              p.resource === currentPermission.resource && 
              p.action !== "*" && 
              selectedPermissions.includes(p.id)
            );
            
            if (relatedPermissions.length > 0) {
              // Add related permission IDs to updating set
              setUpdatingPermissionIds(prev => {
                const newSet = new Set(prev);
                relatedPermissions.forEach(p => newSet.add(p.id));
                return newSet;
              });
              
              // Find related mappings to delete
              const relatedMappings = selectedRoleAssignment.permissions?.filter(
                (rp: any) => {
                  const permissionId = rp.permission?.id || rp.id;
                  return relatedPermissions.some(rp2 => rp2.id === permissionId);
                }
              ) || [];
              
              // Delete all related mappings
              const relatedDeletePromises = relatedMappings.map(mapping =>
                rolePermissionMappingsApiRequests.deleteRolePermissionMapping(mapping.id)
              );
              
              await Promise.all(relatedDeletePromises);
              
              // Update UI state
              setSelectedPermissions(prev => 
                prev.filter(id => !relatedPermissions.some(rp => rp.id === id))
              );
              
              // Remove related permissions from local state
              updatedPermissions = updatedPermissions.filter((rp: any) => {
                const rpPermissionId = rp.permission?.id || rp.id;
                return !relatedPermissions.some(rp2 => rp2.id === rpPermissionId);
              });
              
              toast({
                title: "All permissions removed",
                description: `Removed "*" permission and ${relatedPermissions.length} specific permissions for ${currentPermission.resource}`,
              });
            } else {
              toast({
                title: "Permission removed",
                description: "Permission has been successfully removed from the role",
              });
            }
          } else {
            // If deselecting a specific permission, check if "*" permission for this resource is selected and auto-deselect it
            const allPermission = permissions.find(p => 
              p.resource === currentPermission.resource && p.action === "*"
            );
            
            if (allPermission && selectedPermissions.includes(allPermission.id)) {
              // Find the "*" permission mapping to delete
              const allPermissionMapping = selectedRoleAssignment.permissions?.find(
                (rp: any) => {
                  const permissionId = rp.permission?.id || rp.id;
                  return permissionId === allPermission.id;
                }
              );
              
              if (allPermissionMapping) {
                // Add "*" permission ID to updating set
                setUpdatingPermissionIds(prev => new Set(prev).add(allPermission.id));
                
                await rolePermissionMappingsApiRequests.deleteRolePermissionMapping(
                  allPermissionMapping.id
                );
                
                setSelectedPermissions(prev => prev.filter(id => id !== allPermission.id));
                
                // Remove "*" permission from local state
                updatedPermissions = updatedPermissions.filter((rp: any) => {
                  const rpPermissionId = rp.permission?.id || rp.id;
                  return rpPermissionId !== allPermission.id;
                });
                
                toast({
                  title: "Permissions updated",
                  description: `Removed permission and auto-deselected "*" permission for ${currentPermission.resource}`,
                });
              } else {
                toast({
                  title: "Permission removed",
                  description: "Permission has been successfully removed from the role",
                });
              }
            } else {
              toast({
                title: "Permission removed",
                description: "Permission has been successfully removed from the role",
              });
            }
          }
          
          // Update role permissions locally
          updateRolePermissionsLocally(selectedRoleAssignment.id, updatedPermissions);
        }
      }
      
      // Note: No need to reload all data - we update state optimistically above
    } catch (error: any) {
      toast({
        title: "Error updating permission",
        description: error.message || "Failed to update permission",
        variant: "destructive",
      });
      
      // Revert the UI change if API call failed
      if (checked) {
        setSelectedPermissions(prev => prev.filter(id => id !== permissionId));
    } else {
        setSelectedPermissions(prev => [...prev, permissionId]);
      }
    } finally {
      setUpdatingPermissions(false);
      setUpdatingPermissionIds(new Set());
    }
  };

  // Select All functionality removed per user request

  // User Role selection handlers
  const handleUserRoleSelect = async (userId: string, roleId: string, checked: boolean) => {
    if (!selectedUser) {
      toast({
        title: "No user selected",
        description: "Please select a user first to manage their roles",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      if (checked) {
        // Add role to user
        const response = await userRolesMappingsApiRequests.createUserRoleMapping({
          userId: userId,
          roleId: roleId,
          isActive: true,
        });
        
        // Update local state immediately
        updateUserRolesLocally(userId, roleId, 'add', response.payload?.id);
        
        toast({
          title: "Role assigned",
          description: "Role has been successfully assigned to the user",
        });
      } else {
        // Remove role from user
        // Find the existing mapping in our local state
        const existingMapping = userRoles.find(
          mapping => mapping.userId === userId && mapping.roleId === roleId
        );
        
        if (existingMapping) {
          await userRolesMappingsApiRequests.deleteUserRoleMapping(existingMapping.id);
          
          // Update local state immediately
          updateUserRolesLocally(userId, roleId, 'remove');
          
          toast({
            title: "Role removed",
            description: "Role has been successfully removed from the user",
          });
        } else {
          // Fallback: if mapping not found in local state, try to find it via API
          const userRoleMappingsResponse = await userRolesMappingsApiRequests.getUserRolesMappingsByUserId(
            userId,
            1,
            100,
            { roleId: roleId }
          );
          
          if (userRoleMappingsResponse.payload && userRoleMappingsResponse.payload.docs.length > 0) {
            const mappingToDelete = userRoleMappingsResponse.payload.docs.find(
              mapping => mapping.roleId === roleId && mapping.userId === userId
            );
            
            if (mappingToDelete) {
              await userRolesMappingsApiRequests.deleteUserRoleMapping(mappingToDelete.id);
              
              // Update local state
              updateUserRolesLocally(userId, roleId, 'remove');
              
              toast({
                title: "Role removed",
                description: "Role has been successfully removed from the user",
              });
            }
          }
        }
      }
    } catch (error: any) {
      toast({
        title: "Error updating user role",
        description: error.message || "Failed to update user role",
        variant: "destructive",
      });
      
      // Revert the UI change if API call failed by refreshing the data
      loadInitialData();
    } finally {
      setLoading(false);
    }
  };

  // Inline editing handlers
  const startEditingRole = (role: Role) => {
    setEditingRoleId(role.id);
    setEditingRoleName(role.name);
    setEditingRoleDescription(role.description || '');
  };

  const cancelEditingRole = () => {
    setEditingRoleId(null);
    setEditingRoleName('');
    setEditingRoleDescription('');
  };

  const saveEditingRole = async () => {
    if (!editingRoleId) return;

    try {
      const response = await rolesApiRequests.updateRole(editingRoleId, {
        name: editingRoleName,
        description: editingRoleDescription
      });
      
      if (response.status === 200) {
        toast({
          title: "Role updated",
          description: "Role has been updated successfully",
        });
        cancelEditingRole();
        loadInitialData();
      }
    } catch (error: any) {
      toast({
        title: "Error updating role",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Settings className="h-8 w-8" />
          Role & Permission Management
        </h1>
      </div>

      {/* Search */}
      {/* <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search roles, users, or permissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card> */}

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          {/* <TabsTrigger value="overview" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Overview
          </TabsTrigger> */}
          {/* <TabsTrigger value="role-permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Role Permissions
          </TabsTrigger> */}
          <TabsTrigger value="user-roles-assignment" className="flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            User Roles Assignment
          </TabsTrigger>
          <TabsTrigger value="role-permissions-assignment" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            Role Permissions Assignment
          </TabsTrigger>
        </TabsList>

        {/* User Roles Tab */}
        <TabsContent value="user-roles-assignment" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Users Management */}
          <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between gap-2">
                  <div className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                    Users ({filteredUsers.length})
                  </div>
                  {/* Search Users - Horizontal with title */}
                  <div className="flex-1 max-w-md">
                    <Input
                      placeholder="Search users by name or email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full"
                    />
                  </div>
              </CardTitle>
            </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-2 max-h-128 overflow-y-auto">
                  {filteredUsers.map((user) => (
                      <div 
                      key={user.id}
                        className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors min-h-[4rem] ${
                          selectedUser?.id === user.id 
                            ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' 
                            : 'hover:bg-muted/50'
                        }`}
                      onClick={() => setSelectedUser(user)}
                      >
                        <div className="flex-1 min-h-0">
                          <div className="font-medium truncate">{user.email}</div>
                          <div className="text-sm text-muted-foreground truncate">
                            {user.firstName && user.lastName 
                              ? `${user.firstName} ${user.lastName}` 
                              : user.firstName || user.lastName || 'No name'}
                      </div>
                </div>
              </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* User Roles Assignment */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  User Roles ({roles.length})
              {selectedUser && (
                    <>
                      <Badge variant="default" className="ml-2">
                        Managing: {selectedUser.email}
                      </Badge>
                      <Badge variant="secondary" className="ml-2">
                        {userRoles.filter(mapping => mapping.userId === selectedUser.id).length} assigned
                      </Badge>
                    </>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : !selectedUser ? (
                  <div className="flex items-center justify-center py-8 text-center">
                    <div className="space-y-2">
                      <div className="text-muted-foreground">
                        Select a user from the left panel to manage their roles
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Click on a user to start assigning roles
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-128 overflow-y-auto">
                    {roles.map((role) => (
                      <div key={role.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                        <Checkbox
                          checked={userRoles.some(userRole => 
                            userRole.userId === selectedUser.id && userRole.roleId === role.id
                          )}
                          onCheckedChange={(checked) => handleUserRoleSelect(selectedUser.id!, role.id, checked as boolean)}
                          className="w-4 h-4"
                        />
                        <div className="flex-1">
                          <div className="font-medium">{role.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {role.description || 'No description'}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
          </div>
        </TabsContent>

        {/* Role Permissions Assignment Tab */}
        <TabsContent value="role-permissions-assignment" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Roles Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <UserCheck className="h-5 w-5" />
                    Roles ({filteredRoles.length})
                  </div>
                  <Dialog open={isCreateRoleDialogOpen} onOpenChange={setIsCreateRoleDialogOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Role
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Role</DialogTitle>
                        <DialogDescription>
                          Create a new role for your organization
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="role-name">Role Name</Label>
                          <Input
                            id="role-name"
                            value={roleFormData.name}
                            onChange={(e) => setRoleFormData({ ...roleFormData, name: e.target.value })}
                            placeholder="Enter role name"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="role-description">Description</Label>
                          <Textarea
                            id="role-description"
                            value={roleFormData.description}
                            onChange={(e) => setRoleFormData({ ...roleFormData, description: e.target.value })}
                            placeholder="Enter role description"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsCreateRoleDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreateRole}>Create Role</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-2 max-h-128 overflow-y-auto">
                    {filteredRoles.map((role) => (
                      <div key={role.id} className={`flex items-center justify-between p-3 border rounded-lg ${
                        selectedRoleAssignment?.id === role.id 
                          ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' 
                          : ''
                      }`}>
                        <div className="flex-1">
                          {editingRoleId === role.id ? (
                            // Editing mode
                            <div className="space-y-2">
                              <Input
                                value={editingRoleName}
                                onChange={(e) => setEditingRoleName(e.target.value)}
                                className="font-medium"
                                placeholder="Role name"
                              />
                              <Input
                                value={editingRoleDescription}
                                onChange={(e) => setEditingRoleDescription(e.target.value)}
                                className="text-sm"
                                placeholder="Role description"
                              />
                            </div>
                          ) : (
                            // Display mode
                            <div>
                              <div className="font-medium">{role.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {role.description || 'No description'}
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {editingRoleId === role.id ? (
                            // Save/Cancel buttons when editing
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={saveEditingRole}
                                className="text-green-600 hover:text-green-700"
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={cancelEditingRole}
                                className="text-red-600 hover:text-red-700"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </>
                          ) : (
                            // Normal action buttons
                            <>
                              <Button
                                size="sm"
                                // disabled={role.isSystemRole}
                                variant="outline"
                                onClick={() => setSelectedRoleAssignment(role)}
                              >
                                <ListPlus className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                disabled={role.isSystemRole}
                                onClick={() => startEditingRole(role)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                disabled={role.isSystemRole}
                                onClick={() => handleDeleteRole(role)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Permissions Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Key className="h-5 w-5" />
                    Permissions ({filteredPermissions.length})
                  {selectedRoleAssignment && (
                    <Badge variant="default" className="ml-2">
                      Managing: {selectedRoleAssignment.name}
                    </Badge>
                  )}
                    {selectedPermissions.length > 0 && (
                      <Badge variant="secondary" className="ml-2">
                        {selectedPermissions.length} selected
                      </Badge>
                    )}
                    {updatingPermissions && updatingPermissionIds.size === 0 && (
                      <Loader2 className="h-4 w-4 animate-spin ml-2" />
                    )}

                  {/* <Dialog open={isCreatePermissionDialogOpen} onOpenChange={setIsCreatePermissionDialogOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Permission
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Permission</DialogTitle>
                        <DialogDescription>
                          Create a new permission for your system
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="permission-resource">Resource</Label>
                          <Select
                            value={permissionFormData.resource}
                            onValueChange={(value) => setPermissionFormData({ ...permissionFormData, resource: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select resource" />
                            </SelectTrigger>
                            <SelectContent>
                              {RESOURCES.map((resource) => (
                                <SelectItem key={resource} value={resource}>
                                  {resource}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="permission-action">Action</Label>
                          <Select
                            value={permissionFormData.action}
                            onValueChange={(value) => setPermissionFormData({ ...permissionFormData, action: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select action" />
                            </SelectTrigger>
                            <SelectContent>
                              {ACTIONS.map((action) => (
                                <SelectItem key={action} value={action}>
                                  {action}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsCreatePermissionDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreatePermission}>Create Permission</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog> */}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : !selectedRoleAssignment ? (
                  <div className="flex items-center justify-center py-8 text-center">
                    <div className="space-y-2">
                      <div className="text-muted-foreground">
                        Select a role from the left panel to manage its permissions
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Click the <ListPlus className="inline h-4 w-4" /> button next to a role to start
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-128 overflow-y-auto">
                    {filteredPermissions.map((permission) => (
                      <div key={permission.id} className={`flex items-center gap-3 p-3 border rounded-lg transition-colors ${
                        updatingPermissionIds.has(permission.id) 
                          ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800' 
                          : 'hover:bg-muted/50'
                      }`}>
                        <div className="relative">
                        <Checkbox
                          checked={selectedPermissions.includes(permission.id)}
                          onCheckedChange={(checked) => handlePermissionSelect(permission.id, checked as boolean)}
                            disabled={updatingPermissionIds.has(permission.id) || !selectedRoleAssignment}
                          className="w-4 h-4"
                        />
                          {updatingPermissionIds.has(permission.id) && (
                            <Loader2 className="absolute -top-1 -right-1 h-3 w-3 animate-spin text-blue-500" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Badge className="w-36 h-6" variant="outline">{permission.resource === '*' ? 'All' : permission.resource}</Badge>
                            <Badge className="w-36 h-6" variant="secondary">{permission.action === '*' ? 'All' : permission.action}</Badge>
                          </div>
                          {/* <div className="text-sm text-muted-foreground mt-1">
                            {permission.resource}:{permission.action}
                          </div> */}
                        </div>
                        {/* <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openEditPermissionDialog(permission)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeletePermission(permission)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div> */}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedSettingsPage;
