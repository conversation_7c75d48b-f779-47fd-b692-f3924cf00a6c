/**
 * Enhanced Settings Page with integrated Role-Permission and User-Role management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Settings, 
  Users, 
  Shield, 
  Key, 
  UserCheck, 
  Search,
  Plus,
  Edit,
  Trash2,
  Loader2,
  ListPlus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Import existing components
import { RolePermissionManager } from '@/components/RolePermissionManager';
import { UserRoleManager } from '@/components/UserRoleManager';

// Import API requests
import rolesApiRequests from '@/apiRequests/admin/roles';
import permissionsApiRequests from '@/apiRequests/admin/permissions';
import usersApiRequests from '@/apiRequests/admin/users';

// Import types
import { Role } from '@/app/(DashboardLayout)/roles/types';
import { Permission } from '@/app/(DashboardLayout)/permissions/types';
import { User } from '@/app/(DashboardLayout)/users/types';

// Common resources and actions
const RESOURCES = [
  'user', 'organization', 'device', 'firmware', 'codePush', 'role', 'permission', '*'
];

const ACTIONS = [
  'create', 'read', 'update', 'delete', 'list', 'manage', '*'
];

const EnhancedSettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Data states
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  // Permission selection states
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  // Dialog states
  const [isCreateRoleDialogOpen, setIsCreateRoleDialogOpen] = useState(false);
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false);
  const [isCreatePermissionDialogOpen, setIsCreatePermissionDialogOpen] = useState(false);
  const [isEditPermissionDialogOpen, setIsEditPermissionDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);

  // Form states
  const [roleFormData, setRoleFormData] = useState({
    name: '',
    description: '',
  });

  const [permissionFormData, setPermissionFormData] = useState({
    resource: '',
    action: '',
  });

  const { toast } = useToast();

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load roles, permissions, and users in parallel
      const [rolesResponse, permissionsResponse, usersResponse] = await Promise.all([
        rolesApiRequests.getRoles(1, 100),
        permissionsApiRequests.getPermissions(1, 100),
        usersApiRequests.getUsers(1, 100)
      ]);

      if (rolesResponse.status === 200 && rolesResponse.payload) {
        setRoles(rolesResponse.payload.docs || []);
      }
      
      if (permissionsResponse.status === 200 && permissionsResponse.payload) {
        setPermissions(permissionsResponse.payload.docs || []);
      }
      
      if (usersResponse.status === 200 && usersResponse.payload) {
        setUsers(usersResponse.payload.docs || []);
      }
    } catch (error: any) {
      toast({
        title: "Error loading data",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter data based on search term
  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredUsers = users.filter(user =>
    user.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredPermissions = permissions.filter(permission =>
    permission.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
    permission.action.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Role CRUD operations
  const handleCreateRole = async () => {
    try {
      const response = await rolesApiRequests.createRole(roleFormData);
      if (response.status === 201) {
        toast({
          title: "Role created",
          description: "Role has been created successfully",
        });
        setIsCreateRoleDialogOpen(false);
        setRoleFormData({ name: '', description: '' });
        loadInitialData();
      }
    } catch (error: any) {
      toast({
        title: "Error creating role",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleEditRole = async () => {
    if (!editingRole) return;

    try {
      const response = await rolesApiRequests.updateRole(editingRole.id, roleFormData);
      if (response.status === 200) {
        toast({
          title: "Role updated",
          description: "Role has been updated successfully",
        });
        setIsEditRoleDialogOpen(false);
        setEditingRole(null);
        setRoleFormData({ name: '', description: '' });
        loadInitialData();
      }
    } catch (error: any) {
      toast({
        title: "Error updating role",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleDeleteRole = async (role: Role) => {
    if (!confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
      return;
    }

    try {
      const response = await rolesApiRequests.deleteRole(role.id);
      if (response.status === 200) {
        toast({
          title: "Role deleted",
          description: "Role has been deleted successfully",
        });
        loadInitialData();
        // Clear selected role if it was deleted
        if (selectedRole?.id === role.id) {
          setSelectedRole(null);
        }
      }
    } catch (error: any) {
      toast({
        title: "Error deleting role",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Permission CRUD operations
  const handleCreatePermission = async () => {
    try {
      const response = await permissionsApiRequests.createPermission(permissionFormData);
      if (response.status === 201) {
        toast({
          title: "Permission created",
          description: "Permission has been created successfully",
        });
        setIsCreatePermissionDialogOpen(false);
        setPermissionFormData({ resource: '', action: '' });
        loadInitialData();
      }
    } catch (error: any) {
      toast({
        title: "Error creating permission",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleEditPermission = async () => {
    if (!editingPermission) return;

    try {
      const response = await permissionsApiRequests.updatePermission(editingPermission.id, permissionFormData);
      if (response.status === 200) {
        toast({
          title: "Permission updated",
          description: "Permission has been updated successfully",
        });
        setIsEditPermissionDialogOpen(false);
        setEditingPermission(null);
        setPermissionFormData({ resource: '', action: '' });
        loadInitialData();
      }
    } catch (error: any) {
      toast({
        title: "Error updating permission",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleDeletePermission = async (permission: Permission) => {
    if (!confirm(`Are you sure you want to delete the permission "${permission.resource}:${permission.action}"?`)) {
      return;
    }

    try {
      const response = await permissionsApiRequests.deletePermission(permission.id);
      if (response.status === 200) {
        toast({
          title: "Permission deleted",
          description: "Permission has been deleted successfully",
        });
        loadInitialData();
      }
    } catch (error: any) {
      toast({
        title: "Error deleting permission",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Permission selection handlers
  const handlePermissionSelect = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permissionId]);
    } else {
      setSelectedPermissions(prev => prev.filter(id => id !== permissionId));
    }
  };

  const handleSelectAllPermissions = () => {
    if (selectedPermissions.length === filteredPermissions.length) {
      // If all are selected, deselect all
      setSelectedPermissions([]);
    } else {
      // Select all filtered permissions
      setSelectedPermissions(filteredPermissions.map(permission => permission.id));
    }
  };

  const isAllPermissionsSelected = selectedPermissions.length === filteredPermissions.length && filteredPermissions.length > 0;
  const isSomePermissionsSelected = selectedPermissions.length > 0 && selectedPermissions.length < filteredPermissions.length;

  // Open edit dialogs
  const openEditRoleDialog = (role: Role) => {
    setEditingRole(role);
    setRoleFormData({
      name: role.name,
      description: role.description || '',
    });
    setIsEditRoleDialogOpen(true);
  };

  const openEditPermissionDialog = (permission: Permission) => {
    setEditingPermission(permission);
    setPermissionFormData({
      resource: permission.resource,
      action: permission.action,
    });
    setIsEditPermissionDialogOpen(true);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Settings className="h-8 w-8" />
          Settings & Access Management
        </h1>
        <p className="text-muted-foreground">
          Manage roles, permissions, and user access controls
        </p>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search roles, users, or permissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="role-permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Role Permissions
          </TabsTrigger>
          <TabsTrigger value="user-roles" className="flex items-center gap-2">
            <UserCheck className="h-4 w-4" />
            User Roles
          </TabsTrigger>
          <TabsTrigger value="management" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            Management
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{roles.length}</div>
                <p className="text-xs text-muted-foreground">
                  Active system roles
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
                <Key className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{permissions.length}</div>
                <p className="text-xs text-muted-foreground">
                  System permissions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{users.length}</div>
                <p className="text-xs text-muted-foreground">
                  Registered users
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <Button 
                  onClick={() => setActiveTab('role-permissions')}
                  className="justify-start"
                  variant="outline"
                >
                  <Shield className="mr-2 h-4 w-4" />
                  Manage Role Permissions
                </Button>
                <Button 
                  onClick={() => setActiveTab('user-roles')}
                  className="justify-start"
                  variant="outline"
                >
                  <UserCheck className="mr-2 h-4 w-4" />
                  Assign User Roles
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Role Permissions Tab */}
        <TabsContent value="role-permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role Permission Management
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Assign permissions to roles. Select a role to manage its permissions.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Role Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Select Role</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {filteredRoles.map((role) => (
                    <Button
                      key={role.id}
                      variant={selectedRole?.id === role.id ? "default" : "outline"}
                      onClick={() => setSelectedRole(role)}
                      className="justify-start"
                    >
                      {role.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Role Permission Manager */}
              {selectedRole && (
                <div className="border-t pt-6">
                  <RolePermissionManager
                    role={selectedRole}
                    onRolePermissionsChanged={() => {
                      // Refresh data if needed
                      loadInitialData();
                    }}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Roles Tab */}
        <TabsContent value="user-roles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                User Role Management
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Assign roles to users. Select a user to manage their roles.
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* User Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Select User</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {filteredUsers.map((user) => (
                    <Button
                      key={user.id}
                      variant={selectedUser?.id === user.id ? "default" : "outline"}
                      onClick={() => setSelectedUser(user)}
                      className="justify-start text-left"
                    >
                      <div className="flex flex-col items-start">
                        <span>{user.firstName} {user.lastName}</span>
                        <span className="text-xs text-muted-foreground">{user.email}</span>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              {/* User Role Manager */}
              {selectedUser && (
                <div className="border-t pt-6">
                  <UserRoleManager
                    user={selectedUser}
                    onUserRolesChanged={() => {
                      // Refresh data if needed
                      loadInitialData();
                    }}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Management Tab */}
        <TabsContent value="management" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Roles Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <UserCheck className="h-5 w-5" />
                    Roles ({filteredRoles.length})
                  </div>
                  <Dialog open={isCreateRoleDialogOpen} onOpenChange={setIsCreateRoleDialogOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Role
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Role</DialogTitle>
                        <DialogDescription>
                          Create a new role for your organization
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="role-name">Role Name</Label>
                          <Input
                            id="role-name"
                            value={roleFormData.name}
                            onChange={(e) => setRoleFormData({ ...roleFormData, name: e.target.value })}
                            placeholder="Enter role name"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="role-description">Description</Label>
                          <Textarea
                            id="role-description"
                            value={roleFormData.description}
                            onChange={(e) => setRoleFormData({ ...roleFormData, description: e.target.value })}
                            placeholder="Enter role description"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsCreateRoleDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreateRole}>Create Role</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {filteredRoles.map((role) => (
                      <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{role.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {role.description || 'No description'}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedRole(role)}
                          >
                            <ListPlus className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            disabled={role.isSystemRole}
                            onClick={() => openEditRoleDialog(role)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            disabled={role.isSystemRole}
                            onClick={() => handleDeleteRole(role)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Permissions Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Key className="h-5 w-5" />
                    Permissions ({filteredPermissions.length})
                    {selectedPermissions.length > 0 && (
                      <Badge variant="secondary" className="ml-2">
                        {selectedPermissions.length} selected
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant={isAllPermissionsSelected ? "default" : "outline"}
                      onClick={handleSelectAllPermissions}
                      className="flex items-center gap-2"
                    >
                      <Checkbox
                        checked={isAllPermissionsSelected || isSomePermissionsSelected}
                        className="w-4 h-4"
                      />
                      {isAllPermissionsSelected ? 'Deselect All' : 'Select All'}
                    </Button>
                  </div>
                  {/* <Dialog open={isCreatePermissionDialogOpen} onOpenChange={setIsCreatePermissionDialogOpen}>
                    <DialogTrigger asChild>
                      <Button size="sm">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Permission
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Permission</DialogTitle>
                        <DialogDescription>
                          Create a new permission for your system
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="permission-resource">Resource</Label>
                          <Select
                            value={permissionFormData.resource}
                            onValueChange={(value) => setPermissionFormData({ ...permissionFormData, resource: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select resource" />
                            </SelectTrigger>
                            <SelectContent>
                              {RESOURCES.map((resource) => (
                                <SelectItem key={resource} value={resource}>
                                  {resource}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="permission-action">Action</Label>
                          <Select
                            value={permissionFormData.action}
                            onValueChange={(value) => setPermissionFormData({ ...permissionFormData, action: value })}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select action" />
                            </SelectTrigger>
                            <SelectContent>
                              {ACTIONS.map((action) => (
                                <SelectItem key={action} value={action}>
                                  {action}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsCreatePermissionDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleCreatePermission}>Create Permission</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog> */}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-2 max-h-128 overflow-y-auto">
                    {filteredPermissions.map((permission) => (
                      <div key={permission.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                        <Checkbox
                          checked={selectedPermissions.includes(permission.id)}
                          onCheckedChange={(checked) => handlePermissionSelect(permission.id, checked as boolean)}
                          className="w-4 h-4"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Badge className="w-36 h-6" variant="outline">{permission.resource === '*' ? 'All' : permission.resource}</Badge>
                            <Badge className="w-36 h-6" variant="secondary">{permission.action === '*' ? 'All' : permission.action}</Badge>
                          </div>
                          {/* <div className="text-sm text-muted-foreground mt-1">
                            {permission.resource}:{permission.action}
                          </div> */}
                        </div>
                        {/* <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openEditPermissionDialog(permission)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeletePermission(permission)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div> */}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Edit Role Dialog */}
      <Dialog open={isEditRoleDialogOpen} onOpenChange={setIsEditRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
            <DialogDescription>
              Update role information
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-role-name">Role Name</Label>
              <Input
                id="edit-role-name"
                value={roleFormData.name}
                onChange={(e) => setRoleFormData({ ...roleFormData, name: e.target.value })}
                placeholder="Enter role name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-role-description">Description</Label>
              <Textarea
                id="edit-role-description"
                value={roleFormData.description}
                onChange={(e) => setRoleFormData({ ...roleFormData, description: e.target.value })}
                placeholder="Enter role description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditRoleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditRole}>Update Role</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Permission Dialog */}
      <Dialog open={isEditPermissionDialogOpen} onOpenChange={setIsEditPermissionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Permission</DialogTitle>
            <DialogDescription>
              Update permission information
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-permission-resource">Resource</Label>
              <Select
                value={permissionFormData.resource}
                onValueChange={(value) => setPermissionFormData({ ...permissionFormData, resource: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select resource" />
                </SelectTrigger>
                <SelectContent>
                  {RESOURCES.map((resource) => (
                    <SelectItem key={resource} value={resource}>
                      {resource}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-permission-action">Action</Label>
              <Select
                value={permissionFormData.action}
                onValueChange={(value) => setPermissionFormData({ ...permissionFormData, action: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  {ACTIONS.map((action) => (
                    <SelectItem key={action} value={action}>
                      {action}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditPermissionDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditPermission}>Update Permission</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedSettingsPage;
