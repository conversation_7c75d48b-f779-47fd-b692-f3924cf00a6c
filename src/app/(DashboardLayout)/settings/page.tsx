import EnhancedSettingsPage from "./EnhancedSettings";
import { Metadata } from "next";
import PermissionGuard from "@/components/PermissionGuard";

export const metadata: Metadata = {
  title: 'Settings & Access Management',
  openGraph: {
    title: 'Settings & Access Management',
  },
}

function ProtectedSettingsPage() {
  return (
    <PermissionGuard
      // requiredPermissions={[
      //   { resource: 'role', action: 'get' }
      // ]}
      // fallbackToLegacy={true}
      adminOnly={true}
      showAccessDenied={true}
    >
      <EnhancedSettingsPage />
    </PermissionGuard>
  );
}

export default ProtectedSettingsPage;