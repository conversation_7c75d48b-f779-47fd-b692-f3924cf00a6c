import type { Metada<PERSON> } from "next";
import { cookies } from "next/headers";
import "@/app/globals.css";
import { AppSidebar } from "@/app/components/app-sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Footer } from "../components/footer";
import { SidebarOverlay } from "@/app/components/sidebar-overlay";
import { IframeDialogProvider } from "@/context/iframe-dialog-provider";
import GlobalIframeDialog from "@/components/ui/global-iframe-dialog";

export const metadata: Metadata = {
  title: "Homicen",
  description: "Homicen Dashboard Admin",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get("sidebar:state")?.value === "true";
  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <IframeDialogProvider>
        <div className="layout bg-gradient-to-br from-background via-background to-muted/20 min-h-screen">
          <AppSidebar />
          <SidebarOverlay />

          <main className="w-full min-h-screen pt-16 overflow-auto relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.05] pointer-events-none" />

            {/* Animated Background Elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl animate-pulse" />
              <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/10 to-blue-600/10 rounded-full blur-3xl animate-pulse delay-1000" />
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/5 to-pink-600/5 rounded-full blur-3xl animate-pulse delay-500" />
            </div>

            {/* Content */}
            <div className="relative z-10">
              {children}
              {/* <Footer /> */}
            </div>
          </main>

          {/* Global Iframe Dialog */}
          <GlobalIframeDialog />
        </div>
      </IframeDialogProvider>
    </SidebarProvider>
  );
}
