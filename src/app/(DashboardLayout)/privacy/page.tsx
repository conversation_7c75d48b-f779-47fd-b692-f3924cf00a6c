import React from 'react';
import { Mail, Phone, MapPin, ChevronDown } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const PrivacyPolicy = () => {
  return (
    <div className="min-h-screen bg-primary-foreground py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-3xl font-bold text-center text-primary">Privacy Policy</CardTitle>
            <CardDescription className="text-center mt-2">
              We are committed to protecting your privacy and personal information. This policy explains how we collect, use, and safeguard the information you provide when using the [App Name] mobile application.
            </CardDescription>
          </CardHeader>
        </Card>

        <Accordion type="single" collapsible className="space-y-4">
          <AccordionItem value="item-1" className="rounded-lg shadow-sm">
            <AccordionTrigger className="px-6 py-4 hover:no-underline">
              <h3 className="text-xl font-semibold">1. Information We Collect</h3>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4 text-foreground">
              <p className="mb-4">When you use our application, we may collect the following types of information:</p>
              <ul className="space-y-2 list-disc pl-6">
                <li>Registration Information: Your name, phone number, email address, and other details required to create an account.</li>
                <li>Login Information: Username, password, and login history.</li>
                <li>Transaction Information: Game history, payment details for deposits/withdrawals to manage users and process rewards.</li>
                <li>Device Data: IP address, device type, operating system version, and app performance data.</li>
              </ul>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-2" className="rounded-lg shadow-sm">
            <AccordionTrigger className="px-6 py-4 hover:no-underline">
              <h3 className="text-xl font-semibold">2. How We Use Your Information</h3>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <p className="mb-4">Your information will be used for the following purposes:</p>
              <ul className="space-y-2 list-disc pl-6">
                <li>To provide and maintain the functionality of the application.</li>
                <li>To authenticate your identity during login or transactions.</li>
                <li>To manage and track game results and process user rewards.</li>
                <li>To improve user experience through data analysis.</li>
                <li>To comply with legal requirements and regulations.</li>
              </ul>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-3" className="rounded-lg shadow-sm">
            <AccordionTrigger className="px-6 py-4 hover:no-underline">
              <h3 className="text-xl font-semibold">3. Information Sharing</h3>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <p className="mb-4">We are committed to not selling, trading, or sharing your personal information with third parties, except in the following cases:</p>
              <ul className="space-y-2 list-disc pl-6">
                <li>With your explicit consent.</li>
                <li>When required by law or legal authorities.</li>
                <li>When collaborating with service partners within the scope of this policy.</li>
              </ul>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-4" className="rounded-lg shadow-sm">
            <AccordionTrigger className="px-6 py-4 hover:no-underline">
              <h3 className="text-xl font-semibold">4. Information Security</h3>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <p className="mb-4">We employ advanced security measures to protect your personal information, including:</p>
              <ul className="space-y-2 list-disc pl-6">
                <li>Encrypting sensitive data such as passwords and transaction details.</li>
                <li>Implementing strict access controls for data.</li>
                <li>Regularly auditing and upgrading our security systems.</li>
              </ul>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-5" className="rounded-lg shadow-sm">
            <AccordionTrigger className="px-6 py-4 hover:no-underline">
              <h3 className="text-xl font-semibold">5. User Rights</h3>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <p className="mb-4">You have the following rights regarding your personal information:</p>
              <ul className="space-y-2 list-disc pl-6">
                <li>Access and update your personal details.</li>
                <li>Request deletion of your data when you no longer use the service.</li>
                <li>File a complaint if your information is misused or breached.</li>
              </ul>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-6" className="rounded-lg shadow-sm">
            <AccordionTrigger className="px-6 py-4 hover:no-underline">
              <h3 className="text-xl font-semibold">6. Policy Updates</h3>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-4">
              <p>This Privacy Policy may be updated periodically. Any changes will be notified through the app or via email before they take effect.</p>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">7. Contact Us</CardTitle>
            <CardDescription>
              If you have any questions or concerns about this Privacy Policy, please contact us:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-primary" />
                <span>[Support Email Address]</span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-primary" />
                <span>[Support Phone Number]</span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-primary" />
                <span>[Company or Organization Address]</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <p className="text-center mt-8 text-gray-600">
          Thank you for trusting and using the [App Name] application.
        </p>
      </div>
    </div>
  );
};

export default PrivacyPolicy;