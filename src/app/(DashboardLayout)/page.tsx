"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

import {
  Activity,
  Cpu,
  Globe,
  HardDrive,
  Package,
  Server,
  Shield,
  Smartphone,
  TrendingUp,
  Users,
  Zap,
  Eye,
  Settings
} from "lucide-react";

// Import optimized dashboard components
import IoTOverviewCard from "@/app/components/dashboard/IoTOverviewCard";
import AppManagementCard from "@/app/components/dashboard/AppManagementCard";
import { useIframeDialog, IFRAME_CONFIGS } from "@/context/iframe-dialog-provider";
import { clientSessionToken, clientUserInfo, isUserAuthenticated } from "@/lib/http";
import { useToast } from "@/hooks/use-toast";
import { PermissionPanel } from "@/components/PermissionStatus";

// Mock data for dashboard stats
const dashboardStats = {
  devices: {
    total: 1247,
    online: 1089,
    offline: 158,
    trend: "+12%"
  },
  users: {
    total: 342,
    active: 298,
    inactive: 44,
    trend: "+8%"
  },
  organizations: {
    total: 23,
    active: 21,
    inactive: 2,
    trend: "+2%"
  },
  firmware: {
    total: 45,
    latest: 12,
    outdated: 33,
    trend: "+5%"
  },
  policies: {
    total: 67,
    active: 58,
    inactive: 9,
    trend: "+3%"
  },
  codePush: {
    total: 89,
    deployed: 76,
    pending: 13,
    trend: "+15%"
  }
};



export default function page() {
  const router = useRouter();
  const { openIframeDialog } = useIframeDialog();
  const { toast } = useToast();

  // Check if user is logged in using proper authentication utility
  const isLoggedIn = () => {
    return isUserAuthenticated();
  };

  const handleViewAnalytics = () => {
    // Check login status before opening analytics
    if (!isLoggedIn()) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please log in to view analytics dashboard.",
      });
      router.push('/login');
      return;
    }

    // Open analytics dashboard in iframe dialog
    openIframeDialog(IFRAME_CONFIGS.GRAFANA_DASHBOARD);
  };

  const handleSettings = () => {
    // Check login status before navigating to settings
    if (!isLoggedIn()) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please log in to access settings.",
      });
      router.push('/login');
      return;
    }

    // Navigate to settings page
    router.push('/settings');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-950 dark:via-blue-950 dark:to-indigo-950">
      <div className="p-4 sm:p-6 lg:p-8 w-full space-y-6">
        {/* Optimized Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-white shadow-lg">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute -top-2 -right-2 h-16 w-16 rounded-full bg-white/5 blur-lg"></div>

          <div className="relative z-10">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2.5 bg-white/15 backdrop-blur-sm rounded-xl border border-white/20">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-white">
                    Homicen Dashboard
                  </h1>
                  <p className="text-blue-100 text-sm sm:text-base">
                    IoT Management Platform
                  </p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleViewAnalytics}
                  className="bg-white/15 backdrop-blur-sm border-white/20 text-white hover:bg-white/25 transition-all duration-200 text-xs sm:text-sm"
                >
                  <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5" />
                  Analytics
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleSettings}
                  className="bg-white/15 backdrop-blur-sm border-white/20 text-white hover:bg-white/25 transition-all duration-200 text-xs sm:text-sm"
                >
                  <Settings className="h-3 w-3 sm:h-4 sm:w-4 mr-1.5" />
                  Settings
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Permission Status and Application Management Section */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Permission Status Panel */}
          {/* <div className="lg:col-span-1">
            <PermissionPanel className="h-full" />
          </div> */}

          {/* Application Management Section */}
          <div className="lg:col-span-3">
            <Card className="bg-gradient-to-br from-background to-muted/20 border-border/50 shadow-lg hover:shadow-xl transition-shadow duration-300 h-full">
              <CardHeader className="bg-gradient-to-r from-muted/50 to-muted/30 border-b border-border/30 pb-3">
                <CardTitle className="flex items-center gap-3 text-foreground text-lg">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg text-white shadow-sm">
                    <Smartphone className="h-4 w-4" />
                  </div>
                  Application Management
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <AppManagementCard />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Stats Cards Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
          {/* Devices Card */}
          <Card className="bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950/20 dark:to-teal-950/20 border-emerald-200/50 dark:border-emerald-800/50 hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
            <CardContent className="p-3">
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg text-white group-hover:scale-105 transition-transform duration-200">
                  <Cpu className="h-4 w-4" />
                </div>
                <div>
                  <p className="text-xs font-medium text-emerald-600 dark:text-emerald-400">Devices</p>
                  <p className="text-lg font-bold text-emerald-700 dark:text-emerald-300">{dashboardStats.devices.total.toLocaleString()}</p>
                </div>
                <Badge className="bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 text-xs">
                  <TrendingUp className="h-2 w-2 mr-1" />
                  {dashboardStats.devices.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Users Card */}
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200/50 dark:border-blue-800/50 hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
            <CardContent className="p-3">
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg text-white group-hover:scale-105 transition-transform duration-200">
                  <Users className="h-4 w-4" />
                </div>
                <div>
                  <p className="text-xs font-medium text-blue-600 dark:text-blue-400">Users</p>
                  <p className="text-lg font-bold text-blue-700 dark:text-blue-300">{dashboardStats.users.total.toLocaleString()}</p>
                </div>
                <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800 text-xs">
                  <TrendingUp className="h-2 w-2 mr-1" />
                  {dashboardStats.users.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Organizations Card */}
          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border-purple-200/50 dark:border-purple-800/50 hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
            <CardContent className="p-3">
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg text-white group-hover:scale-105 transition-transform duration-200">
                  <Globe className="h-4 w-4" />
                </div>
                <div>
                  <p className="text-xs font-medium text-purple-600 dark:text-purple-400">Organizations</p>
                  <p className="text-lg font-bold text-purple-700 dark:text-purple-300">{dashboardStats.organizations.total}</p>
                </div>
                <Badge className="bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-800 text-xs">
                  <TrendingUp className="h-2 w-2 mr-1" />
                  {dashboardStats.organizations.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Firmware Card */}
          <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 border-orange-200/50 dark:border-orange-800/50 hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
            <CardContent className="p-3">
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg text-white group-hover:scale-105 transition-transform duration-200">
                  <HardDrive className="h-4 w-4" />
                </div>
                <div>
                  <p className="text-xs font-medium text-orange-600 dark:text-orange-400">Firmware</p>
                  <p className="text-lg font-bold text-orange-700 dark:text-orange-300">{dashboardStats.firmware.total}</p>
                </div>
                <Badge className="bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300 border-orange-200 dark:border-orange-800 text-xs">
                  <TrendingUp className="h-2 w-2 mr-1" />
                  {dashboardStats.firmware.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Policies Card */}
          <Card className="bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-950/20 dark:to-blue-950/20 border-cyan-200/50 dark:border-cyan-800/50 hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
            <CardContent className="p-3">
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-2 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-lg text-white group-hover:scale-105 transition-transform duration-200">
                  <Shield className="h-4 w-4" />
                </div>
                <div>
                  <p className="text-xs font-medium text-cyan-600 dark:text-cyan-400">Policies</p>
                  <p className="text-lg font-bold text-cyan-700 dark:text-cyan-300">{dashboardStats.policies.total}</p>
                </div>
                <Badge className="bg-cyan-100 text-cyan-700 dark:bg-cyan-900/30 dark:text-cyan-300 border-cyan-200 dark:border-cyan-800 text-xs">
                  <TrendingUp className="h-2 w-2 mr-1" />
                  {dashboardStats.policies.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* CodePush Card */}
          <Card className="bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950/20 dark:to-purple-950/20 border-violet-200/50 dark:border-violet-800/50 hover:shadow-md hover:scale-[1.02] transition-all duration-200 group">
            <CardContent className="p-3">
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-2 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg text-white group-hover:scale-105 transition-transform duration-200">
                  <Package className="h-4 w-4" />
                </div>
                <div>
                  <p className="text-xs font-medium text-violet-600 dark:text-violet-400">CodePush</p>
                  <p className="text-lg font-bold text-violet-700 dark:text-violet-300">{dashboardStats.codePush.total}</p>
                </div>
                <Badge className="bg-violet-100 text-violet-700 dark:bg-violet-900/30 dark:text-violet-300 border-violet-200 dark:border-violet-800 text-xs">
                  <TrendingUp className="h-2 w-2 mr-1" />
                  {dashboardStats.codePush.trend}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>



        {/* Quick Actions Section */}
        <Card className="bg-gradient-to-br from-background to-muted/20 border-border/50 shadow-lg">
          <CardHeader className="bg-gradient-to-r from-muted/50 to-muted/30 border-b border-border/30 pb-3">
            <CardTitle className="flex items-center gap-3 text-foreground text-lg">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg text-white">
                <Zap className="h-4 w-4" />
              </div>
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
              <Button
                variant="outline"
                className="h-16 flex-col gap-1.5 bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-950/20 dark:to-teal-950/20 border-emerald-200/50 dark:border-emerald-800/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <Cpu className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                <span className="text-xs font-medium text-emerald-700 dark:text-emerald-300">Add Device</span>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex-col gap-1.5 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200/50 dark:border-blue-800/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span className="text-xs font-medium text-blue-700 dark:text-blue-300">Add User</span>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex-col gap-1.5 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 border-purple-200/50 dark:border-purple-800/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <Globe className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                <span className="text-xs font-medium text-purple-700 dark:text-purple-300">New Org</span>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex-col gap-1.5 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 border-orange-200/50 dark:border-orange-800/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <HardDrive className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                <span className="text-xs font-medium text-orange-700 dark:text-orange-300">Upload FW</span>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex-col gap-1.5 bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-950/20 dark:to-blue-950/20 border-cyan-200/50 dark:border-cyan-800/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <Shield className="h-4 w-4 text-cyan-600 dark:text-cyan-400" />
                <span className="text-xs font-medium text-cyan-700 dark:text-cyan-300">New Policy</span>
              </Button>

              <Button
                variant="outline"
                className="h-16 flex-col gap-1.5 bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-950/20 dark:to-purple-950/20 border-violet-200/50 dark:border-violet-800/50 hover:shadow-md hover:scale-105 transition-all duration-200"
              >
                <Package className="h-4 w-4 text-violet-600 dark:text-violet-400" />
                <span className="text-xs font-medium text-violet-700 dark:text-violet-300">Deploy Code</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* IoT System Overview Section */}
        <Card className="bg-gradient-to-br from-background to-muted/20 border-border/50 shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader className="bg-gradient-to-r from-muted/50 to-muted/30 border-b border-border/30 pb-3">
            <CardTitle className="flex items-center gap-3 text-foreground text-lg">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg text-white shadow-sm">
                <Server className="h-4 w-4" />
              </div>
              IoT System Overview
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <IoTOverviewCard />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
