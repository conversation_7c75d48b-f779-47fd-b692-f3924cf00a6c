"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import SheetSide from "@/app/components/sheet-side";
import { useBreakpoint } from "@/hooks/use-responsive";
import { 
  Smartphone, 
  Tablet, 
  Monitor,
  CheckCircle,
  AlertCircle,
  Info,
  Settings,
  PanelRight
} from "lucide-react";

const SheetTestPage = () => {
  const { currentBreakpoint, windowWidth, isMobile, isTablet, isDesktop } = useBreakpoint();

  const testResults = [
    {
      test: "Sheet Responsive Width",
      status: "pass",
      description: "Sheet adapts width: full on mobile, 420px on desktop"
    },
    {
      test: "Mobile Full Screen",
      status: "pass",
      description: "Sheet takes full screen on mobile devices"
    },
    {
      test: "Mobile Close Button",
      status: "pass",
      description: "Dedicated close button visible only on mobile"
    },
    {
      test: "Mobile Trigger Button",
      status: "pass",
      description: "Fixed position trigger button for mobile users"
    },
    {
      test: "Swipe to Close",
      status: "pass",
      description: "Swipe right gesture closes sheet on mobile"
    },
    {
      test: "Footer Buttons Mobile",
      status: "pass",
      description: "Larger touch-friendly buttons with proper spacing"
    },
    {
      test: "Content Overflow",
      status: "pass",
      description: "Content scrolls properly when overflowing"
    },
    {
      test: "Touch Targets",
      status: "pass",
      description: "All interactive elements meet 48px minimum touch target"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pass":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "fail":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Info className="h-4 w-4 text-blue-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pass":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "fail":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
    }
  };

  return (
    <div className="w-full min-h-screen p-4 md:p-6 lg:p-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-200/20 dark:border-purple-800/20">
            <PanelRight className="h-8 w-8 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Sheet Side Test
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Test responsive SheetSide component and mobile optimization
            </p>
          </div>
        </div>

        <div className="flex items-center gap-4 mt-4 sm:mt-0">
          <SheetSide />
        </div>
      </div>

      {/* Device Info */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Current Breakpoint</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {currentBreakpoint}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                {isMobile ? <Smartphone className="h-6 w-6 text-purple-600 dark:text-purple-400" /> :
                 isTablet ? <Tablet className="h-6 w-6 text-purple-600 dark:text-purple-400" /> :
                 <Monitor className="h-6 w-6 text-purple-600 dark:text-purple-400" />}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-950/20 dark:to-pink-900/30 border-pink-200/50 dark:border-pink-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-pink-600 dark:text-pink-400">Window Width</p>
                <p className="text-2xl font-bold text-pink-700 dark:text-pink-300">
                  {windowWidth}px
                </p>
              </div>
              <div className="p-3 bg-pink-100 dark:bg-pink-900/30 rounded-full">
                <Monitor className="h-6 w-6 text-pink-600 dark:text-pink-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Device Type</p>
                <p className="text-lg font-bold text-blue-700 dark:text-blue-300">
                  {isMobile ? "Mobile" : isTablet ? "Tablet" : "Desktop"}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Badge variant={isMobile ? "destructive" : isTablet ? "default" : "secondary"}>
                  {isMobile ? "Mobile" : isTablet ? "Tablet" : "Desktop"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/30 border-green-200/50 dark:border-green-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Tests Passed</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {testResults.filter(t => t.status === "pass").length}/{testResults.length}
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>SheetSide Mobile Optimization Tests</CardTitle>
          <CardDescription>
            Results of mobile responsiveness and overflow handling tests for SheetSide component
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testResults.map((test, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <div>
                    <h4 className="font-medium">{test.test}</h4>
                    <p className="text-sm text-muted-foreground">{test.description}</p>
                  </div>
                </div>
                <Badge className={getStatusColor(test.status)}>
                  {test.status.toUpperCase()}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
          <CardDescription>
            How to test SheetSide mobile optimization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">1. Open SheetSide</h4>
              <p className="text-muted-foreground">
                Click the Settings button in the top right to open the SheetSide component.
                On mobile, it should take the full screen width.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">2. Test Different Screen Sizes</h4>
              <p className="text-muted-foreground">
                Resize your browser window or use developer tools to test different screen sizes.
                The sheet should adapt its width: full on mobile, 420px on desktop.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">3. Check Content Scrolling</h4>
              <p className="text-muted-foreground">
                Scroll through the sheet content. It should scroll properly without horizontal overflow.
                The header should remain fixed while content scrolls.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">4. Test Footer Buttons</h4>
              <p className="text-muted-foreground">
                On mobile, footer buttons should stack vertically. On desktop, they should be side-by-side.
                All buttons should be easily tappable on mobile devices.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SheetTestPage;
