"use client";

import React, { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  <PERSON>alogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Globe, Plus, Pencil, Trash2, Search, Building2, Calendar, Activity } from "lucide-react";
import { ViewToggle, VIEW_TOGGLE_PRESETS } from "@/app/components/view-toggle";
import { useOrganizationsViewMode } from "@/hooks/useViewMode";
import { format } from "date-fns";
import organizationsApiRequests from "@/apiRequests/admin/organization";
import { get } from "lodash";
import { Organization } from "./types";
import CustomPagination from "@/app/components/Pagination";
import Loading from "@/app/components/Loading";

// interface Organization {
//   _id: {
//     buffer: {
//       [key: number]: number;
//     };
//   };
//   domain: string;
//   orgName: string;
//   abbreviation: string;
//   createdAt: string;
//   updatedAt: string;
//   __v: number;
// }

interface OrganizationFormData {
  domain: string;
  orgName: string;
  abbreviation: string;
}



const OrganizationForm = ({
  initialValues,
  onSubmit,
  onCancel,
}: {
  initialValues?: OrganizationFormData;
  onSubmit: (data: OrganizationFormData) => void;
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState<OrganizationFormData>(
    initialValues || {
      domain: "",
      orgName: "",
      abbreviation: "",
    }
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="orgName">Organization Name</Label>
        <Input
          id="orgName"
          value={formData.orgName}
          onChange={(e) =>
            setFormData({ ...formData, orgName: e.target.value })
          }
          required
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="domain">Domain</Label>
        <Input
          id="domain"
          value={formData.domain}
          onChange={(e) => setFormData({ ...formData, domain: e.target.value })}
          required
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="abbreviation">Abbreviation</Label>
        <Input
          id="abbreviation"
          value={formData.abbreviation}
          onChange={(e) =>
            setFormData({ ...formData, abbreviation: e.target.value })
          }
          required
        />
      </div>
      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{initialValues ? "Update" : "Create"}</Button>
      </DialogFooter>
    </form>
  );
};

const DeleteConfirmation = ({
  organization,
  onConfirm,
  onCancel,
}: {
  organization: Organization;
  onConfirm: () => void;
  onCancel: () => void;
}) => {
  const [confirmName, setConfirmName] = useState("");

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-600">
        To confirm deletion, please type{" "}
        <span className="font-semibold">{organization.orgName}</span> below:
      </p>
      <Input
        value={confirmName}
        onChange={(e) => setConfirmName(e.target.value)}
        placeholder="Type organization name"
      />
      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="button"
          variant="destructive"
          onClick={onConfirm}
          disabled={confirmName !== organization.orgName}
        >
          Delete
        </Button>
      </DialogFooter>
    </div>
  );
};

const OrganizationPage: React.FC = () => {
  const { viewMode, setViewMode } = useOrganizationsViewMode();
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDocs, setTotalDocs] = useState(0);
  const [hastPrevPage, setHasPrevPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);

  const fetchOrganizations = async () => {
    try {
      const response = await organizationsApiRequests.getOrganizations(page, limit);
      if(response.status === 200 && response.payload) {
          const payload = response.payload;
          const docs = get(payload, "docs", []);
          setOrganizations(docs);
          setTotalDocs(get(response, "payload.totalDocs", 0));
          setTotalPages(get(response, "payload.totalPages", 1));
          setHasPrevPage(get(response, "payload.hasPrevPage", false));
          setHasNextPage(get(response, "payload.hasNextPage", false));
      }
    } catch (error) {
      console.log("Error fetching organizations:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async (data: OrganizationFormData) => {
    if (!data) return;
    await organizationsApiRequests.createOrganization(data);
    setIsCreateDialogOpen(false);
    fetchOrganizations();
  };

  const handleEdit = async(data: OrganizationFormData) => {
    if (!selectedOrg) return;
    setLoading(true);
    await organizationsApiRequests.updateOrganization(
      selectedOrg.id,
      data
    )
    setIsEditDialogOpen(false);
    setSelectedOrg(null);
    fetchOrganizations();
    setLoading(false);
  };

  const handleDelete = async () => {
    if (!selectedOrg) return;
    setLoading(true);
    await organizationsApiRequests.deleteOrganization(
      selectedOrg.id
    )
    setIsDeleteDialogOpen(false);
    setSelectedOrg(null);
    fetchOrganizations();
    setLoading(false);
  };

  const handlePageChange = (page: number) => {
    setPage(page);
  };

  useEffect(() => {
    fetchOrganizations();
  }, [limit, page]);

  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {organizations.map((org, index) => (
        <Card
          key={index}
          className="group hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border/50"
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3 min-w-0 flex-1">
                <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20 group-hover:scale-110 transition-all duration-200 flex-shrink-0">
                  <Globe className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="min-w-0 flex-1">
                  <h3 className="text-lg font-semibold group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate" title={org.orgName}>{org.orgName}</h3>
                  <p className="text-sm text-muted-foreground truncate" title={org.domain}>{org.domain}</p>
                </div>
              </div>
              <div className="flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedOrg(org);
                    setIsEditDialogOpen(true);
                  }}
                  className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedOrg(org);
                    setIsDeleteDialogOpen(true);
                  }}
                  className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30"
                >
                  <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
              </div>
            </div>
            <div className="space-y-3 pt-3 border-t border-border/30">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Abbreviation:</span>
                <span className="font-medium">{org.abbreviation}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Created:</span>
                <span className="font-medium">{format(new Date(org.createdAt), "MMM dd, yyyy")}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Updated:</span>
                <span className="font-medium">{format(new Date(org.updatedAt), "MMM dd, yyyy")}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  const ListView = () => (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Organization Info</TableHead>
            <TableHead>Domain</TableHead>
            <TableHead>Abbreviation</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Updated At</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {organizations.map((org, index) => (
            <TableRow key={index} className="hover:bg-muted/30 transition-colors">
              <TableCell>
                <div className="flex items-center gap-3 min-w-0">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex-shrink-0">
                    <Globe className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="font-medium truncate" title={org.orgName}>{org.orgName}</div>
                    <div className="text-sm text-muted-foreground">
                      Organization
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 max-w-full">
                  <Globe className="w-3 h-3 flex-shrink-0" />
                  <span className="truncate" title={org.domain}>{org.domain}</span>
                </span>
              </TableCell>
              <TableCell>
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 max-w-full">
                  <span className="truncate" title={org.abbreviation}>{org.abbreviation}</span>
                </span>
              </TableCell>
              <TableCell className="text-muted-foreground">
                {format(new Date(org.createdAt), "MMM dd, yyyy")}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {format(new Date(org.updatedAt), "MMM dd, yyyy")}
              </TableCell>
              <TableCell>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedOrg(org);
                      setIsEditDialogOpen(true);
                    }}
                    className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedOrg(org);
                      setIsDeleteDialogOpen(true);
                    }}
                    className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30"
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="p-6">
      {/* Modern Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
            <Building2 className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Organizations
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Manage and monitor your organization network
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Search */}
          <div className="relative flex-1 sm:flex-none">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search organizations..."
              className="pl-9 w-full sm:w-64 bg-background/50 backdrop-blur-sm border-border/50"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Controls Row - Compact on mobile */}
          <div className="flex items-center gap-2 justify-between sm:justify-start">
            {/* View Toggle */}
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={(mode) => setViewMode(mode as "grid" | "list")}
              options={VIEW_TOGGLE_PRESETS.organizations}
            />

            {/* Add Organization Button */}
            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add Organization</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Organizations</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {totalDocs}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Building2 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/30 border-green-200/50 dark:border-green-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Active Domains</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {organizations.filter(org => org.domain).length}
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                <Globe className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Recent Activity</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {organizations.filter(org => {
                    const updatedDate = new Date(org.updatedAt);
                    const now = new Date();
                    const diffTime = Math.abs(now.getTime() - updatedDate.getTime());
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return diffDays <= 7;
                  }).length}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Activity className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/30 border-orange-200/50 dark:border-orange-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">This Month</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {organizations.filter(org => {
                    const createdDate = new Date(org.createdAt);
                    const now = new Date();
                    return createdDate.getMonth() === now.getMonth() && createdDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <Calendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-xl">
        <CardContent className="p-6">
          {viewMode === "grid" ? <GridView /> : <ListView />}
        </CardContent>
      </Card>

      {/* <div className="mt-4 text-sm text-gray-500">
        Showing {organizations.length} organizations
      </div> */}

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Organization</DialogTitle>
          </DialogHeader>
          <OrganizationForm
            onSubmit={handleCreate}
            onCancel={() => setIsCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
          </DialogHeader>
          {selectedOrg && (
            <OrganizationForm
              initialValues={{
                domain: selectedOrg.domain,
                orgName: selectedOrg.orgName,
                abbreviation: selectedOrg.abbreviation,
              }}
              onSubmit={handleEdit}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedOrg(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Organization</DialogTitle>
          </DialogHeader>
          {selectedOrg && (
            <DeleteConfirmation
              organization={selectedOrg}
              onConfirm={handleDelete}
              onCancel={() => {
                setIsDeleteDialogOpen(false);
                setSelectedOrg(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Pagination - Fixed at bottom center */}
      <CustomPagination
        viewMode={viewMode}
        totalDocs={totalDocs}
        limit={limit}
        page={page}
        totalPages={totalPages}
        hasPrevPage={hastPrevPage}
        hasNextPage={hasNextPage}
        onPageChange={handlePageChange}
        onLimitChange={(limit) => setLimit(limit)}
      />
    </div>
  );
};

export default OrganizationPage;
