import { Metadata } from "next";
import Policy from "./PolicyManagement";
import PermissionGuard from "@/components/PermissionGuard";

export const metadata: Metadata = {
    title: 'Policy',
    openGraph: {
        title: 'Policy',
    },
}

function ProtectedPolicy() {
  return (
    <PermissionGuard
      // requiredPermissions={[
      //   { resource: 'device', action: 'get' },
      //   { resource: 'user', action: 'get' }
      // ]}
      // fallbackToLegacy={true}
      showAccessDenied={true}
    >
      <Policy />
    </PermissionGuard>
  );
}

export default ProtectedPolicy;