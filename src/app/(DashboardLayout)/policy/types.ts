import {Device} from "@/app/(DashboardLayout)/devices-manager/types";
import {User} from "@/app/(DashboardLayout)/users/types";

export type PolicyRole = 'deviceAdmin' | 'deviceShared';

export interface Policy {
  id: string;
  deviceName: string;
  policyRole: PolicyRole;
  attributePolicies: Record<string, unknown>;
  deviceId: string;
  device: Device;
  userId: string;
  user: User;
  createdAt: Date;
  updatedAt: Date;
}

export const POLICY_ROLES = [
  { value: 'deviceAdmin' as PolicyRole, label: 'Device Administrator' },
  { value: 'deviceShared' as PolicyRole, label: 'Device Shared User' },
];