import { useState, useEffect, useRef, useCallback } from "react";
import { Policy } from "../types";
import policyApiRequests from "@/apiRequests/admin/policies";
import { useToast } from "@/hooks/use-toast";

interface UsePolicyDataProps {
  page: number;
  limit: number;
  searchQuery: string;
}

interface PolicyDataResult {
  policies: Policy[];
  totalPages: number;
  totalItems: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  loading: boolean;
  refetch: () => void;
}

export const usePolicyData = ({
  page,
  limit,
  searchQuery,
}: UsePolicyDataProps): PolicyDataResult => {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [hasPrevPage, setHasPrevPage] = useState<boolean>(false);
  const [hasNextPage, setHasNextPage] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Memoized fetch function
  const fetchPolicies = useCallback(async (keyword: string, currentPage: number, currentLimit: number) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      setLoading(true);
      
      let response;
      if (keyword && keyword.trim()) {
        // Using organization context - no need to pass orgId
        response = await policyApiRequests.searchPolicies(keyword, currentPage, currentLimit);
      } else {
        // Using organization context - no need to pass orgId
        response = await policyApiRequests.getPolicies(currentPage, currentLimit);
      }

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (response.payload) {
        setPolicies(response.payload.docs);
        setTotalPages(response.payload.totalPages);
        setTotalItems(response.payload.totalDocs);
        setHasPrevPage(response.payload.hasPrevPage);
        setHasNextPage(response.payload.hasNextPage);
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.log("Error fetching policies:", error);
        toast({
          title: "Error",
          description: "Failed to fetch policies",
          variant: "destructive",
        });
      }
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false);
      }
    }
  }, [toast]);

  // Single effect for all data fetching - no debouncing since SearchInput handles it
  useEffect(() => {
    fetchPolicies(searchQuery, page, limit);
  }, [searchQuery, page, limit, fetchPolicies]);

  const refetch = useCallback(() => {
    fetchPolicies(searchQuery, page, limit);
  }, [fetchPolicies, searchQuery, page, limit]);

  return {
    policies,
    totalPages,
    totalItems,
    hasPrevPage,
    hasNextPage,
    loading,
    refetch,
  };
};
