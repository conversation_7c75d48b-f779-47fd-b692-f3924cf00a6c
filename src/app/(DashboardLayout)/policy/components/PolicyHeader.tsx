"use client";

import React, { memo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Shield, Plus } from "lucide-react";
import { ViewToggle, VIEW_TOGGLE_PRESETS } from "@/app/components/view-toggle";
import SearchInput from "./SearchInput";

interface PolicyHeaderProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  viewMode: "table" | "grid";
  onViewModeChange: (mode: "table" | "grid") => void;
  onAddClick: () => void;
  isSearching?: boolean;
}

const PolicyHeader = memo<PolicyHeaderProps>(({
  searchQuery,
  onSearchChange,
  viewMode,
  onViewModeChange,
  onAddClick,
  isSearching = false,
}) => {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
          <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Policy Management
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground mt-1">
            Manage access policies and permissions for your organization
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-3 sm:flex-row sm:items-center mt-4 sm:mt-0">
        {/* Search */}
        <SearchInput
          value={searchQuery}
          onValueChange={onSearchChange}
          placeholder="Search policies..."
          className="flex-1 sm:flex-none sm:w-64"
          isLoading={isSearching}
        />

        {/* Controls Row - Compact on mobile */}
        <div className="flex items-center gap-2 justify-between sm:justify-start">
          {/* View Toggle */}
          <ViewToggle
            viewMode={viewMode}
            onViewModeChange={(mode) => onViewModeChange(mode as "table" | "grid")}
            options={VIEW_TOGGLE_PRESETS.tableGrid}
          />

          {/* Add Button */}
          <Button
            onClick={onAddClick}
            className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">New Policy</span>
            <span className="sm:hidden">Add</span>
          </Button>
        </div>
      </div>
    </div>
  );
});

PolicyHeader.displayName = "PolicyHeader";

export default PolicyHeader;
