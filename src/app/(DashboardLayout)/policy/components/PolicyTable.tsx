"use client";

import React, { memo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Edit,
  Trash2,
  Settings,
  Users,
  Laptop,
  User as UserIcon,
} from "lucide-react";
import { Policy } from "../types";

interface PolicyTableProps {
  policies: Policy[];
  onEdit: (policy: Policy) => void;
  onDelete: (policyId: string) => void;
}

const PolicyRoleBadge = memo(({ role }: { role: string }) => {
  const getConfig = (role: string) => {
    switch (role) {
      case 'deviceAdmin':
        return { className: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400', icon: Settings, label: 'Device Admin' };
      case 'deviceShared':
        return { className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400', icon: Users, label: 'Device Shared' };
      default:
        return { className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400', icon: UserIcon, label: role };
    }
  };

  const config = getConfig(role);
  const IconComponent = config.icon;

  return (
    <Badge variant="secondary" className={`font-medium ${config.className}`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {config.label}
    </Badge>
  );
});

PolicyRoleBadge.displayName = "PolicyRoleBadge";

const PolicyTable = memo<PolicyTableProps>(({ policies, onEdit, onDelete }) => {
  return (
    <div className="p-6">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Policy Role</TableHead>
              <TableHead>Device Info</TableHead>
              <TableHead>User Info</TableHead>
              <TableHead className="hidden sm:table-cell">Created At</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {policies.map((policy) => (
              <TableRow key={policy.id} className="hover:bg-muted/30 transition-colors">
                <TableCell>
                  <PolicyRoleBadge role={policy.policyRole} />
                </TableCell>
                <TableCell>
                  <div className="flex items-start gap-2 min-w-0">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex-shrink-0">
                      <Laptop className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-sm min-w-0 flex-1">
                      <div className="font-medium truncate" title={policy?.device?.deviceId}>
                        {policy?.device?.deviceId}
                      </div>
                      <div className="text-muted-foreground truncate" title={policy?.device?.deviceType}>
                        {policy?.device?.deviceType}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-start gap-2 min-w-0">
                    <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex-shrink-0">
                      <UserIcon className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="text-sm min-w-0 flex-1">
                      <div className="font-medium truncate" title={`${policy.user.firstName} ${policy.user.lastName}`}>
                        {policy.user.firstName} {policy.user.lastName}
                      </div>
                      <div className="text-muted-foreground truncate" title={policy.user.email}>{policy.user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden sm:table-cell text-muted-foreground">
                  {new Date(policy.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(policy)}
                      className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(policy.id)}
                      className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
});

PolicyTable.displayName = "PolicyTable";

export default PolicyTable;
