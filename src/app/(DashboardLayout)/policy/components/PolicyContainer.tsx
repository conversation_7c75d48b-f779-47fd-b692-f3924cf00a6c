"use client";

import React, { useState, useCallback, useMemo, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { usePolicyViewMode } from "@/hooks/useViewMode";
import CustomPagination from "@/app/components/Pagination";
import { Policy, POLICY_ROLES } from "../types";
import { clientUserInfo } from "@/lib/http";
import policyApiRequests from "@/apiRequests/admin/policies";
import DeviceCommandSearch from "./DeviceCommandSearch";
import UserCommandSearch from "./UserCommandSearch";
import PolicyHeader from "./PolicyHeader";
import PolicyStats from "./PolicyStats";
import PolicyContent from "./PolicyContent";
import PolicyTable from "./PolicyTable";
import PolicyGrid from "./PolicyGrid";
import PolicyDeleteConfirm from "./PolicyDeleteConfirm";
import { usePolicyData } from "../hooks/usePolicyData";
import Loading from "@/app/components/Loading";

const PolicyContainer: React.FC = () => {
  // Pagination states
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(10);
  
  // Search state - isolated with debouncing
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>("");
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  
  // Dialog states
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [newPolicy, setNewPolicy] = useState<Partial<Policy>>({});
  const [editingPolicy, setEditingPolicy] = useState<Partial<Policy>>({});
  const [showEditDialog, setShowEditDialog] = useState<boolean>(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);
  const [deletingPolicy, setDeletingPolicy] = useState<Policy | null>(null);
  
  // Hooks
  const { viewMode, setViewMode } = usePolicyViewMode();
  const { toast } = useToast();
  const { user } = clientUserInfo.userInfo;
  const { orgId } = user;

  // Debounce search query with loading state
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set searching state if query is different from debounced
    if (searchQuery !== debouncedSearchQuery) {
      setIsSearching(true);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setIsSearching(false);
    }, 300);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [searchQuery, debouncedSearchQuery]);

  // Data hook - uses debounced search query
  const {
    policies,
    totalPages,
    totalItems,
    hasPrevPage,
    hasNextPage,
    loading,
    refetch,
  } = usePolicyData({ page, limit, searchQuery: debouncedSearchQuery });

  // Memoized search change handler - NO dependencies to prevent re-render
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    // Always reset to page 1 when searching
    setPage(1);
  }, []);

  // Helper function để lọc ra chỉ những field cần thiết cho API
  const getPolicyPayload = useCallback((policy: Partial<Policy>) => {
    return {
      policyRole: policy.policyRole,
      deviceId: policy.deviceId,
      userId: policy.userId,
      deviceName: policy.deviceName,
      attributePolicies: policy.attributePolicies,
    };
  }, []);

  // CRUD operations
  const handleAddPolicy = useCallback(async () => {
    try {
      // Using organization context - no need to pass orgId
      await policyApiRequests.createPolicy(getPolicyPayload(newPolicy));
      toast({
        title: "Success",
        description: "Policy created successfully",
      });
      setShowDialog(false);
      setNewPolicy({});
      refetch();
    } catch (error) {
      console.log("Error creating policy:", error);
      toast({
        title: "Error",
        description: "Failed to create policy",
        variant: "destructive",
      });
    }
  }, [newPolicy, getPolicyPayload, toast, refetch]);

  const handleEditClick = useCallback((policy: Policy) => {
    setEditingPolicy(policy);
    setShowEditDialog(true);
  }, []);

  const handleUpdatePolicy = useCallback(async () => {
    try {
      if (editingPolicy.id) {
        const payload = getPolicyPayload(editingPolicy);

        await policyApiRequests.updatePolicy(
          orgId,
          editingPolicy.id,
          payload
        );
        toast({
          title: "Success",
          description: "Policy updated successfully",
        });
        setShowEditDialog(false);
        setEditingPolicy({});
        refetch();
      }
    } catch (error) {
      console.log("Error updating policy:", error);
      toast({
        title: "Error",
        description: "Failed to update policy",
        variant: "destructive",
      });
    }
  }, [orgId, editingPolicy, getPolicyPayload, toast, refetch]);

  const handleDeleteClick = useCallback((policyId: string) => {
    const policy = policies.find(p => p.id === policyId);
    if (policy) {
      setDeletingPolicy(policy);
      setShowDeleteDialog(true);
    }
  }, [policies]);

  const handleConfirmDelete = useCallback(async (policyId: string) => {
    try {
      await policyApiRequests.deletePolicy(policyId);
      toast({
        title: "Success",
        description: "Policy deleted successfully",
      });
      setShowDeleteDialog(false);
      setDeletingPolicy(null);
      refetch();
    } catch (error) {
      console.log("Error deleting policy:", error);
      toast({
        title: "Error",
        description: "Failed to delete policy",
        variant: "destructive",
      });
      throw error; // Re-throw để PolicyDeleteConfirm có thể handle
    }
  }, [orgId, toast, refetch]);

  const handleCloseDeleteDialog = useCallback(() => {
    setShowDeleteDialog(false);
    setDeletingPolicy(null);
  }, []);

  // Memoized view mode change handler
  const handleViewModeChange = useCallback((mode: "table" | "grid") => {
    setViewMode(mode);
  }, [setViewMode]);

  // Memoized add click handler
  const handleAddClick = useCallback(() => {
    setShowDialog(true);
  }, []);

  // Memoized page change handler
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Memoized limit change handler
  const handleLimitChange = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  }, []);

  // Memoized components - only re-render when data changes
  const memoizedPolicyHeader = useMemo(() => (
    <PolicyHeader
      searchQuery={searchQuery}
      onSearchChange={handleSearchChange}
      viewMode={viewMode as "table" | "grid"}
      onViewModeChange={handleViewModeChange}
      onAddClick={handleAddClick}
      isSearching={isSearching}
    />
  ), [searchQuery, handleSearchChange, viewMode, handleViewModeChange, handleAddClick, isSearching]);

  const memoizedPolicyStats = useMemo(() => (
    <PolicyStats policies={policies} totalItems={totalItems} />
  ), [policies, totalItems]);

  const memoizedPolicyTable = useMemo(() => (
    <PolicyTable
      policies={policies}
      onEdit={handleEditClick}
      onDelete={handleDeleteClick}
    />
  ), [policies, handleEditClick, handleDeleteClick]);

  const memoizedPolicyGrid = useMemo(() => (
    <PolicyGrid
      policies={policies}
      onEdit={handleEditClick}
      onDelete={handleDeleteClick}
    />
  ), [policies, handleEditClick, handleDeleteClick]);

  const memoizedPolicyContent = useMemo(() => (
    <PolicyContent
      searchQuery={debouncedSearchQuery}
      totalItems={totalItems}
      viewMode={viewMode as "table" | "grid"}
      isLoading={loading}
    >
      {viewMode === "table" ? memoizedPolicyTable : memoizedPolicyGrid}
    </PolicyContent>
  ), [debouncedSearchQuery, totalItems, viewMode, loading, memoizedPolicyTable, memoizedPolicyGrid]);

  const memoizedPagination = useMemo(() => (
    <CustomPagination
      page={page}
      totalPages={totalPages}
      onPageChange={handlePageChange}
      onLimitChange={handleLimitChange}
      hasNextPage={hasNextPage}
      hasPrevPage={hasPrevPage}
      totalDocs={totalItems}
      limit={limit}
    />
  ), [page, totalPages, hasNextPage, hasPrevPage, handlePageChange, handleLimitChange, totalItems, limit]);

  return (
    <div className="p-6 w-full">
      {/* Header - memoized */}
      {memoizedPolicyHeader}

      {/* Stats Cards - memoized */}
      {memoizedPolicyStats}

      {/* Main Content - memoized */}
      {memoizedPolicyContent}

      {/* Pagination */}
      {memoizedPagination}

      {/* Add Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Policy</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="policyRole" className="text-right">
                Policy Role
              </label>
              <Select
                value={newPolicy.policyRole || ""}
                onValueChange={(value) =>
                  setNewPolicy({ ...newPolicy, policyRole: value as any })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select policy role" />
                </SelectTrigger>
                <SelectContent>
                  {POLICY_ROLES.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="device" className="text-right">
                Device
              </label>
              <div className="col-span-3">
                <DeviceCommandSearch
                  value={newPolicy.deviceId}
                  onValueChange={(deviceId) => {
                    setNewPolicy({
                      ...newPolicy,
                      deviceId: deviceId,
                    });
                  }}
                  placeholder="Select device..."
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="user" className="text-right">
                User
              </label>
              <div className="col-span-3">
                <UserCommandSearch
                  value={newPolicy.userId}
                  onValueChange={(userId) => {
                    setNewPolicy({ ...newPolicy, userId: userId });
                  }}
                  placeholder="Select user..."
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={handleAddPolicy}
              disabled={
                !newPolicy.policyRole ||
                !newPolicy.deviceId ||
                !newPolicy.userId
              }
            >
              Add Policy
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Policy</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="policyRole" className="text-right">
                Policy Role
              </label>
              <Select
                value={editingPolicy.policyRole || ""}
                onValueChange={(value) =>
                  setEditingPolicy({
                    ...editingPolicy,
                    policyRole: value as any,
                  })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select policy role" />
                </SelectTrigger>
                <SelectContent>
                  {POLICY_ROLES.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="device" className="text-right">
                Device
              </label>
              <div className="col-span-3">
                <DeviceCommandSearch
                  value={editingPolicy.deviceId}
                  onValueChange={(deviceId) => {
                    setEditingPolicy({
                      ...editingPolicy,
                      deviceId: deviceId,
                    });
                  }}
                  placeholder="Select device..."
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="user" className="text-right">
                User
              </label>
              <div className="col-span-3">
                <UserCommandSearch
                  value={editingPolicy.userId}
                  onValueChange={(userId) => {
                    setEditingPolicy({ ...editingPolicy, userId: userId });
                  }}
                  placeholder="Select user..."
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={handleUpdatePolicy}
              disabled={
                !editingPolicy.policyRole ||
                !editingPolicy.deviceId ||
                !editingPolicy.userId
              }
            >
              Update Policy
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <PolicyDeleteConfirm
        policy={deletingPolicy}
        open={showDeleteDialog}
        onClose={handleCloseDeleteDialog}
        onConfirmDelete={handleConfirmDelete}
      />
    </div>
  );
};

export default PolicyContainer;
