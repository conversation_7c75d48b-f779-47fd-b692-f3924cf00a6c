"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Policy } from "../types";

interface PolicyDeleteConfirmProps {
  policy: Policy | null;
  open: boolean;
  onClose: () => void;
  onConfirmDelete: (policyId: string) => Promise<void>;
}

const PolicyDeleteConfirm = ({
  policy,
  open,
  onClose,
  onConfirmDelete
}: PolicyDeleteConfirmProps) => {
  const [inputDeviceId, setInputDeviceId] = useState('');
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!policy) return;
    
    if (inputDeviceId !== policy.device.deviceId) {
      setError('Device-ID không chính xác. Vui lòng kiểm tra lại.');
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirmDelete(policy.id);
      onClose();
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa policy. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setInputDeviceId('');
    setError('');
    onClose();
  };

  if (!policy) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Xác nhận xóa policy</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <p className="mb-2">
              Để xóa policy cho device "<span className="font-medium">{policy.device.deviceType}</span>", 
              vui lòng nhập Device-ID sau để xác nhận:
            </p>
            <p className="font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded select-all">
              {policy.device.deviceId}
            </p>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
            <div className="text-sm">
              <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">
                Policy Details:
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Role: <span className="font-medium">{policy.policyRole}</span>
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                User: <span className="font-medium">{policy.user.firstName} {policy.user.lastName}</span>
              </p>
            </div>
          </div>

          <Input
            value={inputDeviceId}
            onChange={(e) => {
              setInputDeviceId(e.target.value);
              setError('');
            }}
            placeholder="Nhập Device-ID để xác nhận"
            disabled={isDeleting}
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              type="submit"
              variant="destructive"
              disabled={!inputDeviceId || isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : 'Xác nhận xóa'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PolicyDeleteConfirm;
