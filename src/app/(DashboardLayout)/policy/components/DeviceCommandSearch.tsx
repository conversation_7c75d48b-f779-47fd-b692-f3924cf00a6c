"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Device } from "@/app/(DashboardLayout)/devices-manager/types";
import deviceApiRequests from "@/apiRequests/admin/devices";
import { clientUserInfo } from "@/lib/http";

interface DeviceCommandSearchProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

const DeviceCommandSearch: React.FC<DeviceCommandSearchProps> = ({
  value,
  onValueChange,
  placeholder = "Search device...",
  disabled = false,
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const { user } = clientUserInfo.userInfo;
  const { orgId } = user;
  const abortControllerRef = useRef<AbortController | null>(null);

  const searchDevices = useCallback(async (keyword: string) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    if (!keyword.trim()) {
      setDevices([]);
      setLoading(false);
      return;
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    setLoading(true);

    try {
      const response = await deviceApiRequests.searchDevices(orgId, keyword, 20);

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (response.payload && response.payload.docs) {
        setDevices(response.payload.docs);
      } else {
        setDevices([]);
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.log("Error searching devices:", error);
        setDevices([]);
      }
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false);
      }
    }
  }, [orgId]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchDevices(searchQuery);
    }, 600); // Tăng delay từ 300ms lên 600ms

    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [searchQuery, searchDevices]);

  // Effect to find and set selected device when value changes
  useEffect(() => {
    if (value) {
      // Try to find device in current devices list first
      const foundDevice = devices.find((device) => device.id === value);
      if (foundDevice) {
        setSelectedDevice(foundDevice);
      } else {
        // If not found and we have a value, fetch device details
        const fetchSelectedDevice = async () => {
          try {
            const response = await deviceApiRequests.getDevice(orgId, value);
            if (response.payload) {
              setSelectedDevice(response.payload);
            }
          } catch (error) {
            console.log("Error fetching selected device:", error);
            setSelectedDevice(null);
          }
        };
        fetchSelectedDevice();
      }
    } else {
      setSelectedDevice(null);
    }
  }, [value, devices, orgId]);

  const handleSelect = (deviceId: string) => {
    console.log("=== DEBUG DEVICE SEARCH ===");
    console.log("Selected deviceId:", deviceId);
    console.log("===========================");

    // Prevent event bubbling and form submission
    setTimeout(() => {
      onValueChange(deviceId);
      setOpen(false);
      setSearchQuery(""); // Clear search when selecting
    }, 0);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchQuery(""); // Clear search when closing
      setDevices([]); // Clear devices list when closing
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedDevice ? (
            <span className="truncate">
              {selectedDevice.deviceId} ({selectedDevice.deviceType})
            </span>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Type to search devices..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>
              {loading ? "Searching..." : searchQuery ? "No devices found." : "Type to search devices..."}
            </CommandEmpty>
            {devices.length > 0 && (
              <CommandGroup>
                {devices.map((device) => (
                  <CommandItem
                    key={device.id}
                    value={`${device.deviceId} ${device.deviceType}`}
                    onSelect={() => {
                      handleSelect(device.id);
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === device.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium">{device.deviceId}</span>
                      <span className="text-sm text-muted-foreground">
                        {device.deviceType}
                      </span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default DeviceCommandSearch;
