"use client";

import React, { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, Users, Laptop, Settings } from "lucide-react";
import { Policy } from "../types";

interface PolicyStatsProps {
  policies: Policy[];
  totalItems: number;
}

const PolicyStats = memo<PolicyStatsProps>(({ policies, totalItems }) => {
  const uniqueUsers = new Set(policies.map(p => p.userId)).size;
  const uniqueDevices = new Set(policies.map(p => p.deviceId)).size;
  const deviceAdminCount = policies.filter(p => p.policyRole === 'deviceAdmin').length;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Policies</p>
              <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                {totalItems}
              </p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
              <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Unique Users</p>
              <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                {uniqueUsers}
              </p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
              <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-950/20 dark:to-cyan-900/30 border-cyan-200/50 dark:border-cyan-800/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-cyan-600 dark:text-cyan-400">Unique Devices</p>
              <p className="text-2xl font-bold text-cyan-700 dark:text-cyan-300">
                {uniqueDevices}
              </p>
            </div>
            <div className="p-3 bg-cyan-100 dark:bg-cyan-900/30 rounded-full">
              <Laptop className="h-6 w-6 text-cyan-600 dark:text-cyan-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-950/20 dark:to-indigo-900/30 border-indigo-200/50 dark:border-indigo-800/50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-indigo-600 dark:text-indigo-400">Device Admin Policies</p>
              <p className="text-2xl font-bold text-indigo-700 dark:text-indigo-300">
                {deviceAdminCount}
              </p>
            </div>
            <div className="p-3 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
              <Settings className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
});

PolicyStats.displayName = "PolicyStats";

export default PolicyStats;
