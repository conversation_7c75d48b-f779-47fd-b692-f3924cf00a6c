"use client";

import React, { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Policy } from "../types";

interface PolicyContentProps {
  searchQuery: string;
  totalItems: number;
  viewMode: "table" | "grid";
  children: React.ReactNode;
  isLoading?: boolean;
}

const PolicyContent = memo<PolicyContentProps>(({
  searchQuery,
  totalItems,
  viewMode,
  children,
  isLoading = false,
}) => {
  return (
    <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-xl">
      {searchQuery && (
        <div className="px-6 py-3 border-b border-border/50 bg-gradient-to-r from-background/80 to-muted/20">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Search results for "<span className="font-medium text-foreground">{searchQuery}</span>"
            </div>
            <div className="text-sm text-muted-foreground">
              {totalItems} found
            </div>
          </div>
        </div>
      )}
      <CardContent className="p-0 pb-24 relative">
        {isLoading && (
          <div className="absolute inset-0 bg-background/60 backdrop-blur-sm z-10 flex items-center justify-center">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
              Updating...
            </div>
          </div>
        )}
        {children}
      </CardContent>
    </Card>
  );
});

PolicyContent.displayName = "PolicyContent";

export default PolicyContent;
