"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { User } from "@/app/(DashboardLayout)/users/types";
import userApiRequests from "@/apiRequests/admin/users";
import { clientUserInfo } from "@/lib/http";

interface UserCommandSearchProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

const UserCommandSearch: React.FC<UserCommandSearchProps> = ({
  value,
  onValueChange,
  placeholder = "Search user...",
  disabled = false,
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const { user } = clientUserInfo.userInfo;
  const { orgId } = user;
  const abortControllerRef = useRef<AbortController | null>(null);

  const searchUsers = useCallback(async (keyword: string) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    if (!keyword.trim()) {
      setUsers([]);
      setLoading(false);
      return;
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    setLoading(true);

    try {
      const response = await userApiRequests.searchUsers(orgId, keyword, 20);

      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (response.payload && response.payload.docs) {
        setUsers(response.payload.docs);
      } else {
        setUsers([]);
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.log("Error searching users:", error);
        setUsers([]);
      }
    } finally {
      if (!abortControllerRef.current?.signal.aborted) {
        setLoading(false);
      }
    }
  }, [orgId]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchUsers(searchQuery);
    }, 600); // Tăng delay từ 300ms lên 600ms

    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [searchQuery, searchUsers]);

  // Effect to find and set selected user when value changes
  useEffect(() => {
    if (value) {
      // Try to find user in current users list first
      const foundUser = users.find((user) => user.id === value);
      if (foundUser) {
        setSelectedUser(foundUser);
      } else {
        // If not found and we have a value, fetch user details
        const fetchSelectedUser = async () => {
          try {
            const response = await userApiRequests.getUser(orgId, value);
            if (response.payload) {
              setSelectedUser(response.payload);
            }
          } catch (error) {
            console.log("Error fetching selected user:", error);
            setSelectedUser(null);
          }
        };
        fetchSelectedUser();
      }
    } else {
      setSelectedUser(null);
    }
  }, [value, users, orgId]);

  const handleSelect = (userId: string) => {
    console.log("=== DEBUG USER SEARCH ===");
    console.log("Selected userId:", userId);
    console.log("========================");

    // Prevent event bubbling and form submission
    setTimeout(() => {
      onValueChange(userId);
      setOpen(false);
      setSearchQuery(""); // Clear search when selecting
    }, 0);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchQuery(""); // Clear search when closing
      setUsers([]); // Clear users list when closing
    }
  };

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
          disabled={disabled}
        >
          {selectedUser ? (
            <span className="truncate">
              {selectedUser.firstName} {selectedUser.lastName} ({selectedUser.email})
            </span>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Type to search users..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>
              {loading ? "Searching..." : searchQuery ? "No users found." : "Type to search users..."}
            </CommandEmpty>
            {users.length > 0 && (
              <CommandGroup>
                {users.map((user) => (
                  <CommandItem
                    key={user.id}
                    value={`${user.firstName} ${user.lastName} ${user.email}`}
                    onSelect={() => {
                      handleSelect(user.id || "");
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === user.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium">
                        {user.firstName} {user.lastName}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {user.email}
                      </span>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default UserCommandSearch;
