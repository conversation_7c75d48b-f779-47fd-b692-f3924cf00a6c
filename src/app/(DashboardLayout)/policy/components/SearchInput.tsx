"use client";

import React, { memo, useCallback } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

// Enhanced loading spinner component - BRIGHT & VISIBLE
const LoadingSpinner = memo(() => (
  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 flex items-center justify-center">
    {/* Outer pulse ring - BRIGHTER */}
    <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/60 via-purple-500/60 to-blue-600/60 animate-ping"></div>

    {/* Secondary pulse ring - BRIGHTER */}
    <div
      className="absolute inset-0.5 rounded-full bg-gradient-to-r from-blue-600/50 via-purple-600/50 to-blue-700/50 animate-ping"
      style={{ animationDelay: '0.3s', animationDuration: '1.5s' }}
    ></div>

    {/* Main spinner - MUCH BRIGHTER */}
    <div className="relative h-4 w-4 rounded-full bg-gradient-to-tr from-blue-600 via-purple-600 to-blue-700 animate-spin shadow-xl shadow-blue-600/50">
      {/* Inner ring */}
      <div className="absolute inset-0.5 rounded-full bg-background"></div>

      {/* Inner gradient - BRIGHTER */}
      <div className="absolute inset-1 rounded-full bg-gradient-to-tr from-blue-500 via-purple-500 to-blue-600"></div>

      {/* Center dot - BIGGER & BRIGHTER */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-1 rounded-full bg-white shadow-sm"></div>
    </div>

    {/* Rotating accent - THICKER & BRIGHTER */}
    <div
      className="absolute inset-0.5 rounded-full border-2 border-transparent border-t-blue-600 border-r-purple-600 animate-spin"
      style={{ animationDuration: '0.6s', animationDirection: 'reverse' }}
    ></div>
  </div>
));

LoadingSpinner.displayName = "LoadingSpinner";

interface SearchInputProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  isLoading?: boolean;
}

const SearchInput = memo<SearchInputProps>(({
  value,
  onValueChange,
  placeholder = "Search...",
  className = "",
  isLoading = false,
}) => {
  // Direct input change handler - no debouncing here
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onValueChange(e.target.value);
  }, [onValueChange]);

  // Clear handler
  const handleClear = useCallback(() => {
    onValueChange("");
  }, [onValueChange]);

  return (
    <div className={`relative ${className}`}>
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4 transition-all duration-300 hover:text-blue-500 hover:scale-105" />
      )}

      <Input
        placeholder={placeholder}
        className={`pl-9 pr-9 w-full bg-background/50 backdrop-blur-sm border-border/50 transition-all duration-300 ${
          isLoading
            ? 'border-blue-500/80 dark:border-blue-600/80 shadow-xl shadow-blue-500/40 ring-2 ring-blue-500/40 bg-blue-50/50 dark:bg-blue-950/30 animate-pulse'
            : 'hover:border-blue-300/40 dark:hover:border-blue-700/40 focus:border-blue-500/60 focus:ring-2 focus:ring-blue-500/30 focus:shadow-md focus:shadow-blue-500/10'
        }`}
        value={value}
        onChange={handleInputChange}
      />

      {value && !isLoading && (
        <button
          onClick={handleClear}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-red-500 h-5 w-5 flex items-center justify-center transition-all duration-200 hover:scale-110 hover:bg-red-50 dark:hover:bg-red-950/30 rounded-full group shadow-sm hover:shadow-md"
        >
          <span className="text-lg font-medium group-hover:font-bold leading-none">×</span>
        </button>
      )}
    </div>
  );
});

SearchInput.displayName = "SearchInput";

export default SearchInput;
