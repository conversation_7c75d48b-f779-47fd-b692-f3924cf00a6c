"use client";

import React, { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Edit,
  Trash2,
  Shield,
  Settings,
  Users,
  Laptop,
  User as UserIcon,
} from "lucide-react";
import { Policy } from "../types";

interface PolicyGridProps {
  policies: Policy[];
  onEdit: (policy: Policy) => void;
  onDelete: (policyId: string) => void;
}

const PolicyRoleBadge = memo(({ role }: { role: string }) => {
  const getConfig = (role: string) => {
    switch (role) {
      case 'deviceAdmin':
        return { className: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400', icon: Settings, label: 'Device Admin' };
      case 'deviceShared':
        return { className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400', icon: Users, label: 'Device Shared' };
      default:
        return { className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400', icon: UserIcon, label: role };
    }
  };

  const config = getConfig(role);
  const IconComponent = config.icon;

  return (
    <Badge variant="secondary" className={`font-medium ${config.className}`}>
      <IconComponent className="w-3 h-3 mr-1" />
      {config.label}
    </Badge>
  );
});

PolicyRoleBadge.displayName = "PolicyRoleBadge";

const PolicyGrid = memo<PolicyGridProps>(({ policies, onEdit, onDelete }) => {
  return (
    <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {policies.map((policy) => (
          <Card key={policy.id} className="group hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border/50">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-3 flex-1">
                    <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
                      <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <PolicyRoleBadge role={policy.policyRole} />
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(policy.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Info */}
                <div className="grid grid-cols-1 gap-4">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
                    <div className="flex items-center gap-2 mb-1">
                      <Laptop className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400">Device</span>
                    </div>
                    <p className="text-sm font-bold text-blue-700 dark:text-blue-300 truncate" title={policy?.device?.deviceId}>
                      {policy?.device?.deviceId}
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 truncate" title={policy?.device?.deviceType}>
                      {policy?.device?.deviceType}
                    </p>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 rounded-lg p-3 border border-purple-200/30 dark:border-purple-800/30">
                    <div className="flex items-center gap-2 mb-1">
                      <UserIcon className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                      <span className="text-xs font-medium text-purple-600 dark:text-purple-400">User</span>
                    </div>
                    <p className="text-sm font-bold text-purple-700 dark:text-purple-300 truncate" title={`${policy.user.firstName} ${policy.user.lastName}`}>
                      {policy.user.firstName} {policy.user.lastName}
                    </p>
                    <p className="text-xs text-purple-600 dark:text-purple-400 truncate" title={policy.user.email}>
                      {policy.user.email}
                    </p>
                  </div>
                </div>

                {/* Footer */}
                <div className="flex items-center justify-end pt-2 border-t border-border/30">
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(policy)}
                      className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(policy.id)}
                      className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
});

PolicyGrid.displayName = "PolicyGrid";

export default PolicyGrid;
