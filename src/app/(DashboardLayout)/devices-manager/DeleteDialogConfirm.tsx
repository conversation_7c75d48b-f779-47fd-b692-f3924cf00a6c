"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Device } from './types';

interface DeleteConfirmationModalProps {
  device: Device;
  open: boolean;
  onClose: () => void;
  onConfirmDelete: (device: Device) => Promise<void>;
}

const DeleteConfirmationModal = ({
  device,
  open,
  onClose,
  onConfirmDelete
}: DeleteConfirmationModalProps) => {
  const [inputId, setInputId] = useState('');
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (inputId !== device.deviceId) {
      setError('Device-ID không chính xác. Vui lòng kiểm tra lại.');
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirmDelete(device);
      onClose();
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa thiết bị. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setInputId('');
    setError('');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Xác nhận xóa thiết bị</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <p className="mb-2">
              Để xóa thiết bị "<span className="font-medium">{device?.deviceType}</span>", 
              vui lòng nhập Device-ID sau để xác nhận:
            </p>
            <p className="font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded select-all">
              {device?.deviceId}
            </p>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
            <div className="text-sm">
              <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">
                Device Details:
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Type: <span className="font-medium">{device?.deviceType}</span>
              </p>
              {device?.firmware?.name && (
                <p className="text-blue-600 dark:text-blue-400">
                  Firmware: <span className="font-medium">{device.firmware.name}</span>
                </p>
              )}
            </div>
          </div>

          <Input
            value={inputId}
            onChange={(e) => {
              setInputId(e.target.value);
              setError('');
            }}
            placeholder="Nhập ID thiết bị để xác nhận"
            disabled={isDeleting}
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              type="submit"
              variant="destructive"
              disabled={!inputId || isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : 'Xác nhận xóa'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteConfirmationModal;