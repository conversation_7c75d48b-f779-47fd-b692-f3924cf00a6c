import React, { useState, useEffect, memo, useRef } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertTriangle, Upload } from "lucide-react";
import firmwaresApiRequests from "@/apiRequests/admin/firmwares";
import devicesApiRequests from "@/apiRequests/admin/devices";
import {
  Device,
  FirmwareUpgradeDialogProps,
  OTA_STATUS,
  OtaProgress,
} from "./types";
import { Firmware } from "@/app/(DashboardLayout)/firmware/types";
import { get } from "lodash";

export const getOTAStatus = (otaProgress: OtaProgress): OTA_STATUS => {
  if (otaProgress.status?.toLowerCase().includes('failed')) {
    return OTA_STATUS.FAILED;
  }

  if (Number(otaProgress.progress) === 100 || otaProgress.status?.toLowerCase().includes('success')) {
    return OTA_STATUS.SUCCESS;
  }

  if (Number(otaProgress.progress) > 0 && Number(otaProgress.progress) < 100 || otaProgress.status?.toLowerCase().includes('progress')) {
    return OTA_STATUS.IN_PROGRESS;
  }

  return OTA_STATUS.NONE;
};

interface LogEntry {
  message: string;
  timestamp: number;
}

interface VisibleLog {
  id: string;
  message: string;
  timestamp: number;
  fadeOut: boolean;
}

const MAX_DISPLAYED_LOGS = 5;

export const getLogShowing = (logs?: string[]): string => {
  if (!logs || !logs.length) return '...';
  const lastTwoLogs = logs.slice(-2).filter(Boolean);
  return ['...', ...lastTwoLogs].join(', ');
};

const FirmwareUpgradeDialog = ({
  device,
  open,
  onClose,
  orgId,
  onFinish,
  otaProgress,
}: FirmwareUpgradeDialogProps) => {
  const currentFirmware = device?.firmware;
  const [firmwares, setFirmwares] = useState<Firmware[]>([]);
  const [selectedFirmware, setSelectedFirmware] = useState<string>("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const logContainerRef = useRef<HTMLDivElement>(null);

  const otaStatus = otaProgress ? getOTAStatus(otaProgress) : null;

  useEffect(() => {
    const fetchFirmwares = async () => {
      try {
        setIsLoading(true);
        const response = await firmwaresApiRequests.getFirmwares(1, 1000);
        const data = get(response, 'payload.docs', []);
        setFirmwares(data);
      } catch (err) {
        setError("Không thể tải danh sách firmware. Vui lòng thử lại.");
      } finally {
        setIsLoading(false);
      }
    };

    if (open) {
      void fetchFirmwares();
    }
  }, [open]);

  useEffect(() => {
    if (otaProgress?.logs) {
      const newEntries = otaProgress.logs.map((log) => ({
        message: log,
        timestamp: Date.now(),
      }));
      setLogEntries(newEntries);
    }
  }, [otaProgress?.logs]);

  const [visibleLogs, setVisibleLogs] = useState([]);

  // useEffect(() => {
  //   // Update visible logs with animation states
  //   const newLogs = logEntries.slice(-MAX_DISPLAYED_LOGS).reverse();
  //   setVisibleLogs(prevLogs => {
  //     // Mark old logs for fade out
  //     const oldLogs = prevLogs.map(log => ({
  //       ...log,
  //       fadeOut: true
  //     }));

  //     // Add new logs with slide up animation
  //     const logsToAdd = newLogs.filter(
  //       newLog => !prevLogs.some(oldLog => oldLog.id === newLog.id)
  //     );

  //     return [...oldLogs, ...logsToAdd];
  //   });

  //   // Clean up faded out logs after animation
  //   const cleanup = setTimeout(() => {
  //     setVisibleLogs(logs => logs.filter(log => !log.fadeOut));
  //   }, 400); // Match animation duration

  //   return () => clearTimeout(cleanup);
  // }, [logEntries]);

  const handleUpdate = async (force: boolean = false) => {
    if (!selectedFirmware || !device) return;
    setIsLoading(true);
    setIsUpgrading(true);
    setError("");

    const payload: Partial<Device> = {
      config: device.config,
      timers: device.timers,
      firmwareId: selectedFirmware,
      deviceType: device.deviceType,
      deviceId: device.deviceId,
      // forceUpgrade: force,
    };

    try {
      await devicesApiRequests.updateDevice(device.id, payload);
    } catch (err) {
      const errorMsg = "Không thể bắt đầu cập nhật firmware. Vui lòng thử lại.";
      setError(errorMsg);
      setIsLoading(false);
      setIsUpgrading(false);
    }
  };

  const handleClose = () => {
    if (!isUpgrading) {
      setSelectedFirmware("");
      setError("");
      setLogEntries([]);
    }
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Cập nhật Firmware</DialogTitle>
          <DialogDescription>
            Cập nhật firmware cho thiết bị "{device?.deviceType}"
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current firmware info */}
          {currentFirmware && (
            <div className="text-sm space-y-1">
              <p className="font-medium">Firmware hiện tại:</p>
              <p>Tên: {currentFirmware.name}</p>
              <p>Phiên bản: {currentFirmware.version}</p>
              {currentFirmware.description && (
                <p>Mô tả: {currentFirmware.description}</p>
              )}
            </div>
          )}

          {/* Firmware selection */}
          {!isUpgrading && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Chọn firmware mới</label>
              <Select
                value={selectedFirmware}
                onValueChange={setSelectedFirmware}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn firmware" />
                </SelectTrigger>
                <SelectContent>
                  {firmwares?.map((fw) => (
                    <SelectItem key={fw.id} value={fw.id}>
                      {fw.name} (v{fw.version})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Selected firmware details */}
          {selectedFirmware && !isUpgrading && (
            <div className="text-sm space-y-1 bg-gray-50 p-3 rounded-md">
              <p className="font-medium">Chi tiết firmware đã chọn:</p>
              {firmwares
                .filter((fw) => fw.id === selectedFirmware)
                .map((fw) => (
                  <div key={fw.id}>
                    <p>Tên: {fw.name}</p>
                    <p>Phiên bản: {fw.version}</p>
                    {fw.description && <p>Mô tả: {fw.description}</p>}
                    <p>
                      Ngày tạo: {new Date(fw.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                ))}
            </div>
          )}

          {/* Progress and Logs */}
          {otaStatus && otaStatus === OTA_STATUS.IN_PROGRESS && (
            <div className="space-y-2">
              <Progress value={otaProgress?.progress} />
              <div className="text-sm text-gray-500 text-center">
                Đang cập nhật... {otaProgress?.progress}%
              </div>

              {/* Log display with fade effect */}
              {/* <div
                ref={logContainerRef}
                className="mt-4 space-y-2 max-h-40 overflow-y-auto animate-fade-up-short"
              >
                {logEntries.slice(-MAX_DISPLAYED_LOGS).reverse().map((entry, index) => (
                  <div
                    key={`${entry.timestamp}-${index}`}
                    className="text-xs text-gray-600"
                    style={{
                      animationDelay: `${index * 100}ms`,
                      opacity: 1 - (index * 0.2)
                    }}
                  >
                    {entry.message}
                  </div>
                ))}
              </div> */}

              <div className="mt-4">
        <h3 className="text-sm font-medium text-gray-600 mb-2">Logs:</h3>
        <div className="h-28 flex flex-col justify-end overflow-scroll bg-gray-50 rounded-md p-3 relative">
          <div className="space-y-2">
            {logEntries.map((entry, index) => (
              <div
                key={`${entry.timestamp}-${index}`}
                className={`text-sm text-gray-600 ${index === logEntries.length - 1 ? 'animate-slide-up-fade' : ''}`}
              >
                {entry.message}
              </div>
            ))}
          </div>
          {/* Gradient overlay for smooth top fade effect */}
          <div className="absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-gray-50 to-transparent pointer-events-none" />
        </div>
      </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Hủy
            </Button>

            {/* Force upgrade button when already upgrading */}
            {isUpgrading && otaStatus === OTA_STATUS.IN_PROGRESS && (
              <Button
                onClick={() => handleUpdate(true)}
                variant="destructive"
                className="gap-2"
              >
                <AlertTriangle className="w-4 h-4" />
                Force Upgrade
              </Button>
            )}

            {/* Normal upgrade button */}
            {!isUpgrading && (
              <Button
                onClick={() => handleUpdate(false)}
                disabled={!selectedFirmware || isLoading}
                className="gap-2"
              >
                <Upload className="w-4 h-4" />
                {isLoading ? "Đang cập nhật..." : "Cập nhật"}
              </Button>
            )}
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default memo(FirmwareUpgradeDialog);
