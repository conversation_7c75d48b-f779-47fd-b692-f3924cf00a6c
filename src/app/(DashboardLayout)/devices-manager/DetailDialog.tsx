"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { DeviceDetailDialogProps } from "./types";
import { IconRender } from "@/app/components/icon-picker";
import devicesApiRequests from "@/apiRequests/admin/devices";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {get} from "lodash";

const DeviceDetailDialog: React.FC<DeviceDetailDialogProps> = ({
  device,
  open,
  onOpenChange,
}) => {
  const [selectedValue, setSelectedValue] = useState<string>("");
  const [newValue, setNewValue] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [changedValues, setChangedValues] = useState<Set<string>>(new Set());
  const previousValues: any = useRef(get(device, 'value', {}));

  const {
    config,
    connectedAt,
    disconnectedAt,
    deviceId,
    deviceType,
    createdAt,
    updatedAt,
  } = device;
  const value: any = get(device, 'value', {});

  console.log('deviceDetail', device);
  const { toast } = useToast();

  const handleOverwriteValue = async () => {
    if (!selectedValue || !newValue) {
      toast({
        title: "Error",
        description: "Please select a value and enter a new value",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      await devicesApiRequests.overwriteValue([device.deviceId], {
        [selectedValue]: newValue,
      });
      toast({
        title: "Success",
        description: "Value successfully overwritten",
        variant: "default",
      });
      setNewValue("");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to overwrite value",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResetValue = async () => {
    try {
      setLoading(true);
      await devicesApiRequests.resetValue([device.deviceId]);
      toast({
        title: "Success",
        description: "Values successfully reset",
        variant: "default",
      });
      setSelectedValue("");
      setNewValue("");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reset values",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const newChangedValues = new Set<string>();
    Object.keys(value).forEach((key) => {
      if (value[key] !== previousValues.current[key]) {
        newChangedValues.add(key);
        // Remove the key after 1 second
        setTimeout(() => {
          setChangedValues((prev) => {
            const updated = new Set(prev);
            updated.delete(key);
            return updated;
          });
        }, 1000);
      }
    });

    if (newChangedValues.size > 0) {
      setChangedValues(newChangedValues);
    }

    previousValues.current = value;
  }, [value]);

  const formatDate = (dateString: string | Date): string => {
    if (!dateString || typeof dateString !== "string") return "-";
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm:ss");
    } catch {
      return "-";
    }
  };

  const renderInfoField = (
    label: string,
    value: string | undefined | null
  ): React.ReactNode => (
    <div>
      <span className="text-sm text-muted-foreground">{label}:</span>
      <p className="text-foreground">{value || "-"}</p>
    </div>
  );

  return (
    device && (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-3xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Device Details</DialogTitle>
          </DialogHeader>

          <ScrollArea className="h-[80vh] pr-4">
            <div className="space-y-6">
              {/* Basic Device Info */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                <div className="grid grid-cols-2 gap-2">
                  {renderInfoField("Device ID", deviceId)}
                  {renderInfoField("Device Type", deviceType)}
                  {renderInfoField("Created At", formatDate(createdAt))}
                  {renderInfoField("Updated At", formatDate(updatedAt))}
                </div>
              </div>

              {/* Organization Info */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Organization</h3>
                <div className="grid grid-cols-2 gap-2">
                  {renderInfoField("Org Name", device.org?.orgName)}
                  {renderInfoField("Domain", device.org?.domain)}
                </div>
              </div>

              {/* Device Relationships */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Device Relationships</h3>
                {renderInfoField("Parent Device ID", device.parentDevice?.deviceId)}
                {device.childrenDevices?.length > 0 && (
                  <div>
                    <span className="text-sm text-gray-500">
                      Children Devices:
                    </span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {device.childrenDevices.map((device) => (
                        <Badge key={device.id} variant="outline">
                          {device.deviceId}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Firmware Info */}
              {
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">
                    Firmware Information
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    {renderInfoField("Name", device.firmware?.name)}
                    {renderInfoField("Version", device.firmware?.version)}
                    {renderInfoField(
                      "Description",
                      device.firmware?.description
                    )}
                    {renderInfoField("Hash ID", device.firmware?.hashId)}
                  </div>
                </div>
              }

              {/* Timers */}
              {device.timers?.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Timers</h3>
                  <div className="space-y-4">
                    {device.timers.map((timer) => (
                      <div key={timer.id} className="border rounded-lg p-4">
                        <div className="grid grid-cols-2 gap-2">
                          {renderInfoField("Timer ID", timer.id)}
                          {renderInfoField("Count", timer.count.toString())}
                          {renderInfoField(
                            "Start Date",
                            formatDate(timer.dtstart)
                          )}
                          {renderInfoField("Until", formatDate(timer.until))}
                          {renderInfoField("Frequency", timer.freq.toString())}
                          {renderInfoField(
                            "Week Day",
                            timer.byweekday.toString()
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Values control */}
              {
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Values</h3>
                  </div>

                  {/* Overwrite Controls */}
                  {Object.keys(value).length > 0 && (
                    <div className="p-4 rounded space-y-4">
                      <Accordion
                        type="single"
                        collapsible
                        className="w-full border-none"
                      >
                        <AccordionItem value="item-1" className="border-none">
                          <AccordionTrigger className="p-0 focus-visible:outline-none">
                            Simulate Value
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="flex items-center gap-4">
                              <Select
                                value={selectedValue}
                                onValueChange={setSelectedValue}
                              >
                                <SelectTrigger className="w-[200px]">
                                  <SelectValue placeholder="Select value to overwrite" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.entries(value).map(([key]) => (
                                    <SelectItem key={key} value={key}>
                                      {key}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <Input
                                type="text"
                                value={newValue}
                                onChange={(e) => setNewValue(e.target.value)}
                                placeholder="Enter new value"
                                className="max-w-[200px]"
                              />
                              <Button
                                onClick={handleOverwriteValue}
                                disabled={
                                  loading || !selectedValue || !newValue
                                }
                              >
                                Overwrite
                              </Button>
                              <div className="space-x-2">
                                <Button
                                  variant="outline"
                                  onClick={handleResetValue}
                                  disabled={loading}
                                >
                                  Reset Values
                                </Button>
                              </div>
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    </div>
                  )}

                  {/* Values Display */}
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    {value && Object.keys(value).length > 0 ? (
                      Object.keys(value).map((key) => (
                        <div
                          key={key}
                          className={`flex items-center justify-between p-2 rounded transition-colors duration-1000 ${
                            changedValues.has(key) ? "text-red-400" : ""
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            <span className="text-muted-foreground">{key}</span>
                          </div>
                          <span className="text-foreground">{value[key] as React.ReactNode}</span>
                        </div>
                      ))
                    ) : (
                      <div className="flex items-center justify-between p-2 rounded">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">
                            No values
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              }

              {/* Parameters */}
              {
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Parameters</h3>
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    {config && config.parameter?.length > 0 ? (
                      config.parameter.map((param) => (
                        <div
                          key={param.field}
                          className="flex items-center justify-between p-2 rounded"
                        >
                          <div className="flex items-center gap-2">
                            {param.icon && (
                              <span className="text-gray-500">
                                {IconRender({ iconName: param.icon })}
                              </span>
                            )}
                            <span>{param.field}</span>
                          </div>
                          <span className="text-gray-500">({param.unit})</span>
                        </div>
                      ))
                    ) : (
                      <div className="flex items-center justify-between p-2 rounded">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500">
                            No parameters
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              }

              {/* Connection Status */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Connection Status</h3>
                <div className="grid grid-cols-2 gap-2">
                  {renderInfoField("Last Connected", formatDate(connectedAt))}
                  {renderInfoField(
                    "Last Disconnected",
                    formatDate(disconnectedAt)
                  )}
                </div>
              </div>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    )
  );
};

export default DeviceDetailDialog;
