"use client";

import React, { useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Shield, User, Settings } from "lucide-react";
import { Device } from "./types";
import { Policy, POLICY_ROLES, PolicyRole } from "../policy/types";
import UserCommandSearch from "../policy/components/UserCommandSearch";
import policyApiRequests from "@/apiRequests/admin/policies";

interface CreatePolicyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  device: Device | null;
  orgId: string;
}

const CreatePolicyDialog: React.FC<CreatePolicyDialogProps> = ({
  open,
  onOpenChange,
  device,
  orgId,
}) => {
  const [newPolicy, setNewPolicy] = useState<Partial<Policy>>({});
  const [isCreating, setIsCreating] = useState(false);
  const { toast } = useToast();

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (open && device) {
      setNewPolicy({
        deviceId: device.id,
        deviceName: device.deviceId,
      });
    } else {
      setNewPolicy({});
    }
  }, [open, device]);

  const handleCreatePolicy = useCallback(async () => {
    if (!device || !newPolicy.policyRole || !newPolicy.userId) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const policyPayload = {
        policyRole: newPolicy.policyRole,
        deviceId: device.id,
        userId: newPolicy.userId,
        deviceName: device.deviceId,
        attributePolicies: newPolicy.attributePolicies || {},
      };

      await policyApiRequests.createPolicy(policyPayload);
      
      toast({
        title: "Success",
        description: `Policy created successfully for device ${device.deviceId}`,
      });
      
      onOpenChange(false);
      setNewPolicy({});
    } catch (error) {
      console.log("Error creating policy:", error);
      toast({
        title: "Error",
        description: "Failed to create policy",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  }, [device, newPolicy, orgId, toast, onOpenChange]);

  const handleCancel = useCallback(() => {
    onOpenChange(false);
    setNewPolicy({});
  }, [onOpenChange]);

  if (!device) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="p-2 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-200/20 dark:border-blue-800/20">
              <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            Create Policy for Device
          </DialogTitle>
          <p className="text-sm text-muted-foreground mt-2">
            Create a new access policy for device <span className="font-medium text-foreground">{device.deviceId}</span>
          </p>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Device Info Display */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-4 border border-blue-200/30 dark:border-blue-800/30">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Settings className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  Device: {device.deviceId}
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  Type: {device.deviceType}
                </p>
              </div>
            </div>
          </div>

          {/* Policy Role Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Policy Role
            </label>
            <Select
              value={newPolicy.policyRole || ""}
              onValueChange={(value: PolicyRole) =>
                setNewPolicy({ ...newPolicy, policyRole: value })
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select policy role" />
              </SelectTrigger>
              <SelectContent>
                {POLICY_ROLES.map((role) => (
                  <SelectItem key={role.value} value={role.value}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${
                        role.value === 'deviceAdmin' 
                          ? 'bg-red-500' 
                          : 'bg-blue-500'
                      }`} />
                      {role.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* User Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Assign to User
            </label>
            <UserCommandSearch
              value={newPolicy.userId}
              onValueChange={(userId) => {
                setNewPolicy({ ...newPolicy, userId });
              }}
              placeholder="Search and select a user"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleCreatePolicy}
            disabled={
              !newPolicy.policyRole ||
              !newPolicy.userId ||
              isCreating
            }
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            {isCreating ? "Creating..." : "Create Policy"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePolicyDialog;
