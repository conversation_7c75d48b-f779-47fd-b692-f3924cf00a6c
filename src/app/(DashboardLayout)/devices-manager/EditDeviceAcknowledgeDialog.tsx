// "use client";
//
// import * as React from "react";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogFooter,
// } from "@/components/ui/dialog";
// import { Input } from "@/components/ui/input";
// import { Button } from "@/components/ui/button";
// import { DEVICE_TYPE, DEVICE_STATUS, Device, Parameter, Acknowledges } from "./types";
// import { Pencil, Save, Trash2 } from "lucide-react";
// import { IconPicker, IconRender } from "@/app/components/icon-picker";
// import devicesApiRequests from "@/apiRequests/admin/devices";
// import { set } from "lodash";
// import {clientUserInfo} from "@/lib/http";
//
// interface EditDeviceDialogProps {
//   device: Device | null;
//   open: boolean;
//   onOpenChange: (open: boolean) => void;
//   onEditDevice: () => void;
// }
//
// const EditDeviceDialog: React.FC<EditDeviceDialogProps> = ({
//   device,
//   open,
//   onOpenChange,
//   onEditDevice,
// }) => {
//   const [deviceId, setDeviceId] = React.useState(device?.deviceId || "");
//   const [orgName, setOrgName] = React.useState("");
//   const [parameterIndex, setParameterIndex] = React.useState(-1);
//   const [editingParameter, setEditingParameter] =
//     React.useState<Parameter | null>(null);
//   const [newAcknowledges, setNewAcknowledges] = React.useState<Acknowledges>({
//     id: "",
//     field: "",
//     qos: 0,
//   });
//   const [newDevice, setNewDevice] = React.useState<Partial<Device>>({});
//   const { user } = clientUserInfo.userInfo;
//   const { id: userId, orgId } = user;
//
//
//   React.useEffect(() => {
//     const fetchDevice = async () => {
//       if (!device) return;
//       const response = await devicesApiRequests.getDevice(orgId, device.id);
//       const deviceDetails = response.payload;
//       setDeviceId(deviceDetails.id);
//       setOrgName(deviceDetails.orgId.orgName);
//       setNewDevice(deviceDetails);
//     };
//     fetchDevice();
//   }, [device]);
//
//   // console.log("device: ", device);
//   // console.log("newDevice: ", newDevice);
//
//   const handleSubmit = async () => {
//     await devicesApiRequests.updateDevice(orgId, device?.id as string, newDevice);
//
//     onEditDevice();
//     onOpenChange(false);
//     // Reset form
//     setDeviceId("");
//     setOrgName("");
//   };
//
//   const handleRemoveParameter = (deviceId: string, paramField: string) => {
//     if (!newDevice) return;
//
//     const parameters = newDevice?.config?.parameter
//       ? [...newDevice.config.parameter]
//       : [];
//     const newData = {
//       ...newDevice,
//       config: {
//         ...newDevice.config,
//         parameter: parameters.filter((param) => param.field !== paramField),
//       },
//     };
//     setNewDevice(newData);
//     return device;
//   };
//
//   const handleUpdateParameter = async (
//     deviceId: string,
//     oldField: string,
//     updatedParameter: Parameter
//   ) => {
//     if (!newDevice) return;
//
//     const parameters = newDevice?.config?.parameter
//       ? [...newDevice.config.parameter]
//       : [];
//     const newData = {
//       ...newDevice,
//       config: {
//         ...newDevice.config,
//         parameter: parameters.map((param) =>
//           param.field === oldField ? updatedParameter : param
//         ),
//       },
//     };
//     // console.log("newData: ", newData);
//     setNewDevice(newData);
//     // await devicesApiRequests.updateDevice(deviceId, newDevice);
//     setEditingParameter(null);
//     return device;
//   };
//
//   const handleAddAcknowledges = async () => {
//     if (newDevice && newAcknowledges.field) {
//       const acknowledges = newDevice.acknowledges
//         ? [...newDevice.acknowledges]
//         : [];
//       const newData = {
//         ...newDevice,
//         acknowledges: [...acknowledges, newAcknowledges],
//       };
//       setNewDevice(newData);
//       await devicesApiRequests.updateDevice(orgId, deviceId, newDevice);
//
//       setNewAcknowledges({ id: "", field: "", qos: 0 });
//     }
//   };
//
//   return (
//     <Dialog open={open} onOpenChange={() => onOpenChange(!open)}>
//       <DialogContent aria-describedby={undefined} className="max-w-md">
//         <DialogHeader>
//           <DialogTitle>Device Configuration</DialogTitle>
//         </DialogHeader>
//         <div className="space-y-4">
//           <div className="space-y-2">
//             <h3 className="font-medium">Parameters</h3>
//             {newDevice?.config?.parameter?.map((param, indexParam) => (
//               <div
//                 key={param.field}
//                 className="flex items-center justify-between p-2 bg-gray-50 rounded"
//               >
//                 {parameterIndex === indexParam && editingParameter ? (
//                   <div className="flex gap-2 w-full">
//                     <Input
//                       value={editingParameter.field}
//                       onChange={(e) =>
//                         setEditingParameter({
//                           ...editingParameter,
//                           field: e.target.value,
//                         })
//                       }
//                       placeholder="Field name"
//                     />
//                     <Input
//                       value={editingParameter.icon}
//                       onChange={(e) =>
//                         setEditingParameter({
//                           ...editingParameter,
//                           icon: e.target.value,
//                         })
//                       }
//                       placeholder="Icon (emoji)"
//                     />
//                     <Input
//                       value={editingParameter.unit}
//                       onChange={(e) =>
//                         setEditingParameter({
//                           ...editingParameter,
//                           unit: e.target.value,
//                         })
//                       }
//                       placeholder="Unit"
//                     />
//                     <Button
//                       onClick={() => {
//                         handleUpdateParameter(
//                           deviceId,
//                           param.field,
//                           editingParameter
//                         );
//                         setEditingParameter(null);
//                       }}
//                     >
//                       Save
//                     </Button>
//                   </div>
//                 ) : (
//                   <>
//                     <div className="flex items-center gap-2">
//                       {param.icon && (
//                         <span className="text-gray-500">
//                           {IconRender({ iconName: param.icon })}
//                         </span>
//                       )}
//                       <span className="font-medium">{param.field}</span>
//                       <span className="text-gray-500">({param.unit})</span>
//                     </div>
//                     <div className="flex gap-2">
//                       <Button
//                         variant="ghost"
//                         size="icon"
//                         onClick={() => {
//                           setParameterIndex(indexParam);
//                           setEditingParameter(param);
//                         }}
//                       >
//                         <Pencil className="h-4 w-4" />
//                       </Button>
//                       <Button
//                         variant="ghost"
//                         size="icon"
//                         onClick={() =>
//                           handleRemoveParameter(deviceId, param.field)
//                         }
//                       >
//                         <Trash2 className="h-4 w-4 text-destructive" />
//                       </Button>
//                     </div>
//                   </>
//                 )}
//               </div>
//             ))}
//           </div>
//           <div className="space-y-2">
//             <h3 className="font-medium">Add Parameter</h3>
//             <div className="flex gap-2">
//               <Input
//                 placeholder="ID"
//                 value={newAcknowledges.id}
//                 onChange={(e) =>
//                   setNewAcknowledges({ ...newAcknowledges, id: e.target.value })
//                 }
//               />
//               <Input
//                 placeholder="field"
//                 value={newAcknowledges.field}
//                 onChange={(e) =>
//                   setNewAcknowledges({ ...newAcknowledges, field: e.target.value })
//                 }
//               />
//               <Input
//                 placeholder="Qos"
//                 value={newAcknowledges.qos.toString()}
//                 onChange={(e) =>
//                   setNewAcknowledges({ ...newAcknowledges, qos: Number(e.target.value) })
//                 }
//               />
//               <Button onClick={handleAddAcknowledges}>Add</Button>
//             </div>
//           </div>
//         </div>
//         <DialogFooter>
//           {/* <Button variant={"destructive"} onClick={handleDeleteDevice}>
//             <Trash2 className="mr-2 h-4 w-4" /> Delete
//           </Button> */}
//           <Button onClick={handleSubmit}>
//             <Save className="mr-2 h-4 w-4" />
//             Save
//           </Button>
//         </DialogFooter>
//       </DialogContent>
//     </Dialog>
//   );
// };
//
// export default React.memo(EditDeviceDialog);
