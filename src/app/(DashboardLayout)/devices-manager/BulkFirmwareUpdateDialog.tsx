"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import {
  CircleFadingArrowUp,
  Check,
  AlertTriangle,
  Loader2,
  Package,
  Calendar,
  FileText,
  Search,
  RefreshCw,
} from "lucide-react";
import { format } from "date-fns";
import firmwaresApiRequests from "@/apiRequests/admin/firmwares";
import { Firmware } from "@/app/(DashboardLayout)/firmware/types";
import get from "lodash/get";

interface BulkFirmwareUpdateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDeviceIds: Set<string>;
  selectedDevicesCount: number;
  onConfirmUpdate: (firmwareId: string) => Promise<void>;
  orgId: string;
}

const BulkFirmwareUpdateDialog: React.FC<BulkFirmwareUpdateDialogProps> = ({
  open,
  onOpenChange,
  selectedDeviceIds,
  selectedDevicesCount,
  onConfirmUpdate,
  orgId,
}) => {
  const [firmwares, setFirmwares] = useState<Firmware[]>([]);
  const [selectedFirmware, setSelectedFirmware] = useState<Firmware | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateProgress, setUpdateProgress] = useState(0);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const ITEMS_PER_PAGE = 10;

  // Fetch firmwares when dialog opens or search changes
  useEffect(() => {
    if (open) {
      resetAndFetchFirmwares();
    }
  }, [open, orgId]);

  // Debounced search effect
  useEffect(() => {
    if (!open) return;

    const timer = setTimeout(() => {
      resetAndFetchFirmwares();
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const resetAndFetchFirmwares = () => {
    setCurrentPage(1);
    setFirmwares([]);
    fetchFirmwares(1, true);
  };

  const fetchFirmwares = async (page: number = currentPage, reset: boolean = false) => {
    try {
      if (reset) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      const filters = searchQuery ? { name: searchQuery } : {};
      const response = await firmwaresApiRequests.getFirmwares(page, ITEMS_PER_PAGE, filters);

      if (response.status === 200 && response.payload) {
        const newFirmwares = get(response, "payload.docs", []);
        const totalPages = get(response, "payload.totalPages", 1);
        const hasNextPage = get(response, "payload.hasNextPage", false);

        if (reset) {
          setFirmwares(newFirmwares);
        } else {
          setFirmwares(prev => [...prev, ...newFirmwares]);
        }

        setTotalPages(totalPages);
        setHasMore(hasNextPage);
        setCurrentPage(page);
      }
    } catch (error) {
      console.log("Error fetching firmwares:", error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (hasMore && !isLoadingMore) {
      fetchFirmwares(currentPage + 1, false);
    }
  };

  const handleConfirmUpdate = async () => {
    if (!selectedFirmware) return;

    try {
      setIsUpdating(true);
      setUpdateProgress(0);

      // Simulate progress (in real implementation, you might get progress from API)
      const progressInterval = setInterval(() => {
        setUpdateProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      await onConfirmUpdate(selectedFirmware.id);

      // Complete progress
      setUpdateProgress(100);
      clearInterval(progressInterval);

      // Close dialog after short delay
      setTimeout(() => {
        onOpenChange(false);
        resetDialog();
      }, 1000);

    } catch (error) {
      console.log("Error updating firmware:", error);
      setIsUpdating(false);
      setUpdateProgress(0);
    }
  };

  const resetDialog = () => {
    setSelectedFirmware(null);
    setIsUpdating(false);
    setUpdateProgress(0);
    setSearchQuery("");
    setCurrentPage(1);
    setFirmwares([]);
    setHasMore(false);
  };

  const handleClose = () => {
    if (!isUpdating) {
      onOpenChange(false);
      resetDialog();
    }
  };

  const formatFileSize = (firmware: Firmware) => {
    // Since Firmware type doesn't have size, we'll show device type instead
    return firmware.deviceType || "All devices";
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CircleFadingArrowUp className="h-5 w-5 text-blue-600" />
            Bulk Firmware Update
          </DialogTitle>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">
              Update firmware for {selectedDevicesCount} selected devices
            </p>
            {!isUpdating && firmwares.length > 0 && (
              <p className="text-xs text-muted-foreground">
                Showing {firmwares.length} firmwares {hasMore && `(${currentPage}/${totalPages} pages)`}
              </p>
            )}
          </div>
        </DialogHeader>

        {isUpdating ? (
          // Update Progress View
          <div className="py-8">
            <div className="text-center mb-6">
              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <CircleFadingArrowUp className="h-8 w-8 text-blue-600 animate-spin" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Updating Firmware</h3>
              <p className="text-sm text-muted-foreground">
                Updating {selectedDevicesCount} devices to {selectedFirmware?.name} v{selectedFirmware?.version}
              </p>
            </div>

            <div className="space-y-4">
              <Progress value={updateProgress} className="h-3" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Progress: {updateProgress}%</span>
                <span>{updateProgress === 100 ? "Complete!" : "Updating..."}</span>
              </div>
            </div>
          </div>
        ) : (
          // Firmware Selection View
          <div className="space-y-4">
            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search firmwares by name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span className="text-sm text-muted-foreground">Loading firmwares...</span>
              </div>
            ) : (
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {firmwares.length === 0 ? (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-sm text-muted-foreground">No firmwares available</p>
                  </div>
                ) : (
                  firmwares.map((firmware) => (
                    <Card
                      key={firmware.id}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                        selectedFirmware?.id === firmware.id
                          ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20"
                          : "hover:bg-muted/50"
                      }`}
                      onClick={() => setSelectedFirmware(firmware)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="font-semibold">{firmware.name}</h4>
                              <Badge variant="secondary">v{firmware.version}</Badge>
                              {selectedFirmware?.id === firmware.id && (
                                <Check className="h-4 w-4 text-blue-600" />
                              )}
                            </div>
                            
                            {firmware.description && (
                              <p className="text-sm text-muted-foreground mb-2">
                                {firmware.description}
                              </p>
                            )}

                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {format(new Date(firmware.createdAt), "MMM dd, yyyy")}
                              </div>
                              <div className="flex items-center gap-1">
                                <FileText className="h-3 w-3" />
                                {formatFileSize(firmware)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}

                {/* Load More Button */}
                {hasMore && (
                  <div className="flex justify-center pt-4">
                    <Button
                      variant="outline"
                      onClick={handleLoadMore}
                      disabled={isLoadingMore}
                      className="w-full"
                    >
                      {isLoadingMore ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Loading more...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Load More ({currentPage}/{totalPages})
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </div>
            )}

            {selectedFirmware && (
              <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-amber-800 dark:text-amber-200 mb-1">
                      Confirm Bulk Update
                    </p>
                    <p className="text-amber-700 dark:text-amber-300">
                      This will update {selectedDevicesCount} devices to firmware "{selectedFirmware.name}" v{selectedFirmware.version}. 
                      This action cannot be undone.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <DialogFooter>
          {!isUpdating && (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleConfirmUpdate}
                disabled={!selectedFirmware || isLoading}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <CircleFadingArrowUp className="h-4 w-4 mr-2" />
                Update {selectedDevicesCount} Devices
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BulkFirmwareUpdateDialog;
