"use client";

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DEVICE_TYPE, Device } from './types';
import {isEmpty} from "lodash";
import {useToast} from "@/hooks/use-toast";
import {isHexString} from "@/lib/data"; // Adjust import path as needed

interface AddDeviceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddDevice: (device: Partial<Device>) => void;
}

const AddDeviceDialog: React.FC<AddDeviceDialogProps> = ({ open, onOpenChange, onAddDevice }) => {
  const [deviceType, setDeviceType] = React.useState<DEVICE_TYPE>(DEVICE_TYPE.SENSOR);
  const [deviceId, setDeviceId] = React.useState('');
  const { toast } = useToast();

  const handleSubmit = () => {
    if (isEmpty(deviceId)) {
      toast({
        variant: "destructive",
        title: "deviceId not empty",
        description: "Failed to add device",
      });
    }

    if (!isHexString(deviceId)) {
      toast({
        variant: "destructive",
        title: "deviceId must be a hex string (6 bytes)",
        description: "Failed to add device",
      });
    }

    const newDevice: Partial<Device> = {
      deviceId: deviceId,
      deviceType: deviceType,
    };

    onAddDevice(newDevice);
    onOpenChange(false);
    setDeviceId('');
    setDeviceType(DEVICE_TYPE.SENSOR);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Device</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="deviceId" className="text-right">
              Device ID
            </label>
            <Input
              id="deviceId"
              value={deviceId}
              onChange={(e) => setDeviceId(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="deviceType" className="text-right">
              Device Type
            </label>
            <Select
              value={deviceType}
              onValueChange={(value) => setDeviceType(value as DEVICE_TYPE)}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select device type" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(DEVICE_TYPE).map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="orgName" className="text-right">
              Organization
            </label>
            <Input
              id="orgName"
              value={orgName}
              onChange={(e) => setOrgName(e.target.value)}
              className="col-span-3"
            />
          </div> */}
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={!deviceId}>
            Create Device
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddDeviceDialog;