import {Firmware} from "@/app/(DashboardLayout)/firmware/types";
import {Organization} from "@/app/(DashboardLayout)/organizations/types";

export enum DEVICE_TYPE {
  INVERTER = "INVERTER",
  RGB_LIGHT = "RGB_LIGHT",
  VT_1_PORT = "VT_1_PORT",
  CB_1_PORT = "CB_1_PORT",
  CB_2_PORT = "CB_2_PORT",
  CB_3_PORT = "CB_3_PORT",
  CB_4_PORT = "CB_4_PORT",
  CB_5_PORT = "CB_5_PORT",
  CB_6_PORT = "CB_6_PORT",
  CB_7_PORT = "CB_7_PORT",
  CB_8_PORT = "CB_8_PORT",
  HUB = "HUB",
  SPEAKER = "SPEAKER",
  SENSOR = "SENSOR",
}

export enum DEVICE_STATUS {
  ONLINE = "ONLINE",
  OFFLINE = "OFFLINE",
  MAINTENANCE = "MAINTENANCE",
  ERROR = "ERROR",
}

export enum OTA_STATUS {
  IN_PROGRESS = "IN_PROGRESS",
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
  NONE = "NONE",
}

export interface Parameter {
  field: string;
  icon: string;
  unit: string;
}

export interface Config {
  parameter: Parameter[];
}

export interface Timer {
  id: string;
  tid: number;
  count: number;
  byweekday: number;
  until: string;
  dtstart: string;
  freq: number;
  afterId: number;
  createdAt: string;
  updatedAt: string;
  value: Record<string, unknown>;
}

export interface OtaProgress {
  logs: string[];
  progress: number;
  status: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Device {
  config: Config;
  orgId: string;
  org: Organization;
  timers: Timer[];
  value: Record<string, unknown>;
  childrenDeviceIds: string[];
  childrenDevices: Device[];
  parentDeviceId: string;
  parentDevice: Device;
  firmwareId: string;
  firmware: Firmware;
  otaProgress: OtaProgress | null;
  deviceType: DEVICE_TYPE;
  status: DEVICE_STATUS;
  deviceId: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  disconnectedAt: string;
  connectedAt: string;
}

export type ViewMode = "grid" | "table";

export interface File {
  id: string;
  path: string;
}

export interface Timer {
  id: string;
  tid: number;
  count: number;
  byweekday: number;
  until: string;
  dtstart: string;
  freq: number;
  afterId: number;
  createdAt: string;
  updatedAt: string;
  value: Record<string, unknown>;
}

export interface DeviceDetailDialogProps {
  device: Device;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface FirmwareUpgradeDialogProps {
  device: Device | null;
  open: boolean;
  onClose: () => void;
  orgId: string;
  onFinish: () => void;
  otaProgress: OtaProgress | null;
}
