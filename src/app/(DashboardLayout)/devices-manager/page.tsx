import DeviceManagement from "./DevicesManager";
import { Metadata } from "next";
import PermissionGuard from "@/components/PermissionGuard";

export const metadata: Metadata = {
  title: "Devices Manager",
  openGraph: {
    title: "Devices Manager",
  },
}

function ProtectedDeviceManagement() {
  return (
    <PermissionGuard
      // requiredPermissions={[
      //   { resource: 'device', action: 'update' },
      //   { resource: 'device', action: 'create' }
      // ]}
      // fallbackToLegacy={true}
      showAccessDenied={true}
    >
      <DeviceManagement />
    </PermissionGuard>
  );
}

export default ProtectedDeviceManagement;