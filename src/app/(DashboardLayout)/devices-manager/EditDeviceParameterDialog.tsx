"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DEVICE_TYPE, DEVICE_STATUS, Device, Parameter } from "./types";
import { Pencil, Save, Trash2 } from "lucide-react";
import { IconPicker, IconRender } from "@/app/components/icon-picker";
import devicesApiRequests from "@/apiRequests/admin/devices";
import {clientUserInfo} from "@/lib/http";

interface EditDeviceDialogProps {
  device: Device;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEditDevice: () => void;
}

const EditDeviceDialog: React.FC<EditDeviceDialogProps> = ({
  device,
  open,
  onOpenChange,
  onEditDevice,
}) => {
  const [parameterIndex, setParameterIndex] = React.useState(-1);
  const [editingParameter, setEditingParameter] =
    React.useState<Parameter | null>(null);
  const [newParameter, setNewParameter] = React.useState<Parameter>({
    field: "",
    icon: "",
    unit: "",
  });
  const [newDevice, setNewDevice] = React.useState<Partial<Device>>(device || {});
  const { user } = clientUserInfo.userInfo;
  const { orgId } = user;
  const deviceId = device.id;

  const handleSubmit = async () => {
    await devicesApiRequests.updateDevice(deviceId, newDevice);

    onEditDevice();
    onOpenChange(false);
  };

  const handleRemoveParameter = (paramField: string) => {
    if (!newDevice) return;

    const parameters = newDevice?.config?.parameter
      ? [...newDevice.config.parameter]
      : [];
    const newData = {
      ...newDevice,
      config: {
        ...newDevice.config,
        parameter: parameters.filter((param) => param.field !== paramField),
      },
    };
    setNewDevice(newData);
    return device;
  };

  const handleUpdateParameter = async (
    oldField: string,
    updatedParameter: Parameter
  ) => {
    if (!newDevice) return;

    const parameters = newDevice?.config?.parameter
      ? [...newDevice.config.parameter]
      : [];
    const newData = {
      ...newDevice,
      config: {
        ...newDevice.config,
        parameter: parameters.map((param) =>
          param.field === oldField ? updatedParameter : param
        ),
      },
    };

    setNewDevice(newData);
    setEditingParameter(null);
    return device;
  };

  const handleAddParameter = async () => {
    if (newDevice && newParameter.field) {
      const parameters = newDevice?.config?.parameter
        ? [...newDevice.config.parameter]
        : [];
      const newData = {
        ...newDevice,
        config: {
          ...newDevice.config,
          parameter: [...parameters, newParameter],
        },
      };
      setNewDevice(newData);
      await devicesApiRequests.updateDevice(deviceId, newDevice);

      setNewParameter({ field: "", icon: "", unit: "" });
    }
  };

  return (
    <Dialog open={open} onOpenChange={() => onOpenChange(!open)}>
      <DialogContent aria-describedby={undefined} className="max-w-md">
        <DialogHeader>
          <DialogTitle>Device Configuration</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 overflow-auto max-h-[80vh]">
          <div className="space-y-2">
            <h3 className="font-medium">Parameters</h3>
            {newDevice?.config?.parameter?.map((param, indexParam) => (
              <div
                key={param.field}
                className="flex items-center justify-between p-2 bg-gray-50 rounded"
              >
                {parameterIndex === indexParam && editingParameter ? (
                  <div className="flex gap-2 w-full">
                    <Input
                      value={editingParameter.field}
                      onChange={(e) =>
                        setEditingParameter({
                          ...editingParameter,
                          field: e.target.value,
                        })
                      }
                      placeholder="Field name"
                    />

                    <IconPicker
                      selectedIcon={editingParameter.icon}
                      onSelectIcon={(icon: any) =>
                        setNewParameter({ ...editingParameter, icon })
                      }
                    />
                    <Input
                      value={editingParameter.unit}
                      onChange={(e) =>
                        setEditingParameter({
                          ...editingParameter,
                          unit: e.target.value,
                        })
                      }
                      placeholder="Unit"
                    />
                    <Button
                      onClick={() => {
                        handleUpdateParameter(
                          param.field,
                          editingParameter
                        );
                        setEditingParameter(null);
                      }}
                    >
                      Save
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center gap-2">
                      {param.icon && (
                        <span className="text-gray-500">
                          {IconRender({ iconName: param.icon })}
                        </span>
                      )}
                      <span className="font-medium">{param.field}</span>
                      <span className="text-gray-500">({param.unit})</span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setParameterIndex(indexParam);
                          setEditingParameter(param);
                        }}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() =>
                          handleRemoveParameter(param.field)
                        }
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">Add Parameter</h3>
            <div className="flex gap-2">
              <Input
                placeholder="Field name"
                value={newParameter.field}
                onChange={(e) =>
                  setNewParameter({ ...newParameter, field: e.target.value })
                }
              />
              {/* <Input
                placeholder="Icon"
                value={newParameter.icon}
                onChange={(e) =>
                  setNewParameter({ ...newParameter, icon: e.target.value })
                }
              /> */}
              <IconPicker
                selectedIcon={newParameter.icon}
                onSelectIcon={(icon: any) =>
                  setNewParameter({ ...newParameter, icon })
                }
              />
              <Input
                placeholder="Unit"
                value={newParameter.unit}
                onChange={(e) =>
                  setNewParameter({ ...newParameter, unit: e.target.value })
                }
              />
              <Button onClick={handleAddParameter}>Add</Button>
            </div>
          </div>
        </div>
        <DialogFooter>
          {/* <Button variant={"destructive"} onClick={handleDeleteDevice}>
            <Trash2 className="mr-2 h-4 w-4" /> Delete
          </Button> */}
          <Button onClick={handleSubmit}>
            <Save className="mr-2 h-4 w-4" />
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default React.memo(EditDeviceDialog);
