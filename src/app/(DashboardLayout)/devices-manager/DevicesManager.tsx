"use client";

import React, { useEffect, useMemo, useRef, useState, useCallback, memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertTriangle,
  CircleFadingArrowUp,
  Lightbulb,
  Router,
  Settings,
  Trash,
  User as UserIcon,
  Wifi,
  WifiOff,
  Wrench,
  Zap,
  Thermometer,
  Plug,
  HelpCircle,
  Plus,
  Server,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  Layers,
  Shield,
  Check,
  X,
  MoreVertical,
} from "lucide-react";
import {
  Device,
  DEVICE_STATUS,
  DEVICE_TYPE,
  OTA_STATUS,
  ViewMode,
} from "./types";

import mqtt from "mqtt";
import get from "lodash/get";
import { format } from "date-fns";
import { clientUserInfo } from "@/lib/http";
import AddDeviceDialog from "./AddDeviceDialog";
import EditDeviceDialog from "./EditDeviceParameterDialog";
import DeviceDetailDialog from "./DetailDialog";
import devicesApiRequests from "@/apiRequests/admin/devices";
import usersApiRequests from "@/apiRequests/admin/users";
import { User } from "../users/types";
import DeleteConfirmationModal from "./DeleteDialogConfirm";
import FirmwareUpdateDialog, {
  getOTAStatus,
} from "./UpgradeFirmwareDialog";
import { BROKER_URL } from "@/config/config";
import CustomPagination from "@/app/components/Pagination";
import { Progress } from "@/components/ui/progress";
import SearchInput from "../policy/components/SearchInput";
import { ViewToggle, VIEW_TOGGLE_PRESETS } from "@/app/components/view-toggle";
import { useDevicesManagerViewMode } from "@/hooks/useViewMode";
import CreatePolicyDialog from "./CreatePolicyDialog";
import BulkFirmwareUpdateDialog from "./BulkFirmwareUpdateDialog";
import { useCurrentOrgId } from "@/hooks/useCurrentOrgId";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {isEmpty} from "lodash";

const getDeviceIcon = (deviceType: DEVICE_TYPE) => {
  switch (deviceType) {
    case DEVICE_TYPE.INVERTER:
      return <Zap className="h-4 w-4 text-muted-foreground" />;
    case DEVICE_TYPE.HUB:
      return <Router className="h-4 w-4 text-muted-foreground" />;
    case DEVICE_TYPE.SENSOR:
      return <Thermometer className="h-4 w-4 text-muted-foreground" />;
    case DEVICE_TYPE.RGB_LIGHT:
      return <Lightbulb className="h-4 w-4 text-amber-500" />;
    case DEVICE_TYPE.VT_1_PORT:
      return <Plug className="h-4 w-4 text-muted-foreground" />;
    default:
      return <HelpCircle className="h-4 w-4 text-muted-foreground" />;
  }
};

const getStatusIcon = (status: DEVICE_STATUS) => {
  switch (status) {
    case DEVICE_STATUS.ONLINE:
      return <Wifi className="h-4 w-4 text-emerald-500" />;
    case DEVICE_STATUS.OFFLINE:
      return <WifiOff className="h-4 w-4 text-muted-foreground" />;
    case DEVICE_STATUS.ERROR:
      return <AlertTriangle className="h-4 w-4 text-destructive" />;
    case DEVICE_STATUS.MAINTENANCE:
      return <Wrench className="h-4 w-4 text-amber-500" />;
    default:
      return <WifiOff className="h-4 w-4 text-muted-foreground" />;
  }
};

const getStatusColor = (status: DEVICE_STATUS) => {
  switch (status) {
    case DEVICE_STATUS.ONLINE:
      return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400";
    case DEVICE_STATUS.OFFLINE:
      return "bg-muted text-muted-foreground";
    case DEVICE_STATUS.ERROR:
      return "bg-destructive/10 text-destructive dark:bg-destructive/20";
    case DEVICE_STATUS.MAINTENANCE:
      return "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400";
  }
};

// Utility function to check if the device is online
const isDeviceOnline = (connectedAt?: Date, disconnectedAt?: Date): boolean => {
  if (!connectedAt) return false;
  if (!disconnectedAt) return true;
  return new Date(connectedAt) > new Date(disconnectedAt);
};

interface SearchParams {
  name?: string;
  deviceType?: string;
  deviceId?: string;
  userId?: string;
}

// Helper functions for localStorage operations
const getFromLocalStorage = (key: string, defaultValue: any = null) => {
  if (typeof window === 'undefined') return defaultValue;

  try {
    const item = localStorage.getItem(key);
    return item !== null ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.warn(`Failed to parse ${key} from localStorage:`, error);
    return defaultValue;
  }
};

const setToLocalStorage = (key: string, value: any) => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.warn(`Failed to save ${key} to localStorage:`, error);
  }
};

const DeviceManagement = memo(() => {
  const { viewMode, setViewMode } = useDevicesManagerViewMode();
  const currentOrgId = useCurrentOrgId(); // Add organization context
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>("");
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [groupByType, setGroupByType] = useState<boolean>(false);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const [isOpenAddDeviceDialog, setIsOpenAddDeviceDialog] = useState(false);
  const [isOpenSettingsDialog, setIsOpenSettingsDialog] = useState(false);
  const [isOpenDetailDialog, setIsOpenDetailDialog] = useState(false);
  const [isOpenDeleteDialog, setIsOpenDeleteDialog] = useState(false);
  const [isOpenFirmwareUpgradeDialog, setIsOpenFirmwareUpgradeDialog] =
    useState(false);
  const [isOpenCreatePolicyDialog, setIsOpenCreatePolicyDialog] = useState(false);
  const [devices, setDevices] = useState<Device[]>([]);
  const [selectedDeviceIndex, setSelectedDeviceIndex] = useState<number | null>(
    null
  );

  // Load settings from localStorage on component mount
  useEffect(() => {
    const savedGroupByType = getFromLocalStorage('deviceManager_groupByType', false);
    const savedExpandedGroups = getFromLocalStorage('deviceManager_expandedGroups', []);

    setGroupByType(savedGroupByType);
    setExpandedGroups(new Set(savedExpandedGroups));
  }, []);

  // Optimized localStorage operations with debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      setToLocalStorage('deviceManager_groupByType', groupByType);
    }, 300);
    return () => clearTimeout(timer);
  }, [groupByType]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setToLocalStorage('deviceManager_expandedGroups', Array.from(expandedGroups));
    }, 300);
    return () => clearTimeout(timer);
  }, [expandedGroups]);

  // Only create a new state for currentOtaProgress when has selected device to avoid unnecessary re-render
  const currentDeviceSelected = useRef<Device | null>(null);
  const currentOtaProgress = useMemo(() => {
    console.log("selectedDeviceIndex: ", selectedDeviceIndex);
    if (selectedDeviceIndex != null && selectedDeviceIndex >= 0) {
      currentDeviceSelected.current = devices[selectedDeviceIndex];
      const otaProgress = devices[selectedDeviceIndex].otaProgress;
      if (
        otaProgress &&
        otaProgress.progress >= 0 &&
        otaProgress.progress < 100
      ) {
        return otaProgress;
      }
    }

    return null;
  }, [selectedDeviceIndex, devices]);

  const [limit, setLimit] = useState(50);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDocs, setTotalDocs] = useState(0);
  const [hastPrevPage, setHasPrevPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [payload, setPayload] = useState({});
  const { user } = clientUserInfo.userInfo;
  const { id: userId, orgId } = user;

  const [searchParams, setSearchParams] = useState<SearchParams>({
    name: "",
    deviceType: "",
    deviceId: "",
    userId: "",
  });

  // Users state for filter dropdown
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userSearchQuery, setUserSearchQuery] = useState<string>("");
  const [isUserSearching, setIsUserSearching] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);

  // Multi-select state
  const [selectedDeviceIds, setSelectedDeviceIds] = useState<Set<string>>(new Set());
  const [isBulkActionsOpen, setIsBulkActionsOpen] = useState(false);
  const [isBulkFirmwareUpdateOpen, setIsBulkFirmwareUpdateOpen] = useState(false);

  // Optimized multi-select handlers with useCallback
  const handleDeviceSelect = useCallback((deviceId: string, checked: boolean) => {
    setSelectedDeviceIds(prev => {
      const newSelected = new Set(prev);
      if (checked) {
        newSelected.add(deviceId);
      } else {
        newSelected.delete(deviceId);
      }
      return newSelected;
    });
  }, []);

  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedDeviceIds(new Set(devices.map(device => device.id)));
    } else {
      setSelectedDeviceIds(new Set());
    }
  }, [devices]);

  const clearSelection = () => {
    setSelectedDeviceIds(new Set());
    setIsBulkActionsOpen(false);
  };

  // Open bulk firmware update dialog
  const handleOpenBulkFirmwareUpdate = () => {
    setIsBulkFirmwareUpdateOpen(true);
    setIsBulkActionsOpen(false);
  };

  // Bulk firmware update handler
  const handleBulkFirmwareUpdate = async (firmwareId: string) => {
    if (selectedDeviceIds.size === 0) return;

    try {
      const updates = Array.from(selectedDeviceIds).map(deviceId => ({
        id: deviceId,
        firmwareId: firmwareId
      }));

      await devicesApiRequests.bulkUpdateFirmware(updates);

      // Refresh devices list
      setIsLoading(!isLoading);

      // Clear selection
      clearSelection();

      // Show success message (you can add toast notification here)
      console.log(`Successfully updated firmware for ${updates.length} devices`);
    } catch (error) {
      console.log("Error updating firmware:", error);
      // Show error message (you can add toast notification here)
    }
  };

  // Search users with debounce
  const searchUsers = async (keyword: string) => {
    if (!keyword.trim()) {
      setUsers([]);
      return;
    }

    try {
      setIsUserSearching(true);
      const result = await usersApiRequests.searchUsers(orgId, keyword.trim(), 20);
      if (result.status === 200 && result.payload) {
        setUsers(get(result, "payload.docs", []));
      }
    } catch (error) {
      console.log("Error searching users:", error);
    } finally {
      setIsUserSearching(false);
    }
  };

  // Debounce user search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (userSearchQuery) {
        searchUsers(userSearchQuery);
      } else {
        setUsers([]);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [userSearchQuery, orgId]);

  // Handle clear user filter
  const handleClearUserFilter = () => {
    setSelectedUserId("");
    setSelectedUser(null);
    setUserSearchQuery("");
    setUsers([]);
  };

  // Debounce search query with loading state
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set searching state if query is different from debounced
    if (searchQuery !== debouncedSearchQuery) {
      setIsSearching(true);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setIsSearching(false);
    }, 300);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [searchQuery, debouncedSearchQuery]);

  // Memoized search change handler
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    // Reset to page 1 when searching
    setPage(1);
  }, []);

  const handlePageChange = (page: number) => {
    setPage(page);
  };

  // Optimized device filtering with memoization
  const filteredDevices = useMemo(() => {
    if (!debouncedSearchQuery && !selectedUserId) {
      return devices;
    }
    
    return devices.filter(device => {
      // User filter - check if device has userId property from API response  
      if (selectedUserId && (device as any).userId !== selectedUserId) {
        return false;
      }
      
      // Search filter  
      if (debouncedSearchQuery) {
        const query = debouncedSearchQuery.toLowerCase();
        return (
          device.deviceId?.toLowerCase().includes(query) ||
          device.deviceType?.toLowerCase().includes(query) ||
          device.firmware?.version?.toLowerCase().includes(query) ||
          device.org?.orgName?.toLowerCase().includes(query)
        );
      }
      
      return true;
    });
  }, [devices, debouncedSearchQuery, selectedUserId]);

  // Optimized device grouping with memoization
  const groupedDevices = useMemo(() => {
    const devicesToGroup = filteredDevices;
    
    if (!groupByType) return { ungrouped: devicesToGroup };

    const groups: Record<string, Device[]> = {};
    devicesToGroup.forEach(device => {
      const type = device.deviceType;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(device);
    });

    return groups;
  }, [filteredDevices, groupByType]);

  // Memoized selected device counts for performance
  const selectedDeviceCount = useMemo(() => selectedDeviceIds.size, [selectedDeviceIds]);
  const totalDeviceCount = useMemo(() => filteredDevices.length, [filteredDevices]);

  // Optimized group expansion with useCallback
  const toggleGroup = useCallback((groupType: string) => {
    setExpandedGroups(prev => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(groupType)) {
        newExpanded.delete(groupType);
      } else {
        newExpanded.add(groupType);
      }
      return newExpanded;
    });
  }, []);

  // Get device type display name and color
  const getDeviceTypeInfo = (deviceType: DEVICE_TYPE) => {
    switch (deviceType) {
      case DEVICE_TYPE.INVERTER:
        return { name: "Inverters", color: "from-yellow-500 to-orange-500", bgColor: "bg-yellow-50 dark:bg-yellow-950/20", borderColor: "border-yellow-200 dark:border-yellow-800" };
      case DEVICE_TYPE.HUB:
        return { name: "Hubs", color: "from-blue-500 to-cyan-500", bgColor: "bg-blue-50 dark:bg-blue-950/20", borderColor: "border-blue-200 dark:border-blue-800" };
      case DEVICE_TYPE.SENSOR:
        return { name: "Sensors", color: "from-purple-500 to-pink-500", bgColor: "bg-purple-50 dark:bg-purple-950/20", borderColor: "border-purple-200 dark:border-purple-800" };
      case DEVICE_TYPE.RGB_LIGHT:
        return { name: "RGB Lights", color: "from-amber-500 to-yellow-500", bgColor: "bg-amber-50 dark:bg-amber-950/20", borderColor: "border-amber-200 dark:border-amber-800" };
      case DEVICE_TYPE.VT_1_PORT:
        return { name: "VT 1-Port", color: "from-green-500 to-emerald-500", bgColor: "bg-green-50 dark:bg-green-950/20", borderColor: "border-green-200 dark:border-green-800" };
      default:
        return { name: deviceType, color: "from-gray-500 to-slate-500", bgColor: "bg-gray-50 dark:bg-gray-950/20", borderColor: "border-gray-200 dark:border-gray-800" };
    }
  };

  const handleAddDevice = async ({ deviceId, deviceType }: Partial<Device>) => {
    const newDevice: Partial<Device> = {
      deviceId: deviceId,
      deviceType: deviceType,
    };

    await devicesApiRequests.createDevice(newDevice);
    setIsLoading(!isLoading);
  };

  // Optimized device action handlers with useCallback
  const handleEditDevice = useCallback((index: number) => {
    setSelectedDeviceIndex(index);
    setIsOpenSettingsDialog(true);
  }, []);

  const handleOpenDetail = useCallback((index: number) => {
    setSelectedDeviceIndex(index);
    setIsOpenDetailDialog(true);
  }, []);

  const handleDeleteDevice = useCallback(async (device: Device) => {
    if (device) {
      await devicesApiRequests.deleteDevice(device.id);
      setIsLoading(!isLoading);
    }
  }, [isLoading]);

  const handleOpenDeleteDialog = useCallback((index: number) => {
    setSelectedDeviceIndex(index);
    setIsOpenDeleteDialog(true);
  }, []);

  const handleOpenUpgradeDialog = useCallback((index: number) => {
    setSelectedDeviceIndex(index);
    setIsOpenFirmwareUpgradeDialog(true);
  }, []);

  const handleOpenCreatePolicyDialog = useCallback((index: number) => {
    setSelectedDeviceIndex(index);
    setIsOpenCreatePolicyDialog(true);
  }, []);

  useEffect(() => {
    if (
      !isOpenAddDeviceDialog &&
      !isOpenSettingsDialog &&
      !isOpenDetailDialog &&
      !isOpenDeleteDialog &&
      !isOpenFirmwareUpgradeDialog &&
      !isOpenCreatePolicyDialog
    ) {
      setSelectedDeviceIndex(null);
    }
  }, [
    isOpenAddDeviceDialog,
    isOpenSettingsDialog,
    isOpenDetailDialog,
    isOpenDeleteDialog,
    isOpenFirmwareUpgradeDialog,
    isOpenCreatePolicyDialog,
  ]);

  // Optimized device fetching with cleanup and reduced dependencies
  useEffect(() => {
    const abortController = new AbortController();
    
    const fetchDevices = async () => {
      try {
        setLoading(true);

        let response;
        if (debouncedSearchQuery && debouncedSearchQuery.trim()) {
          // Use search API when there's a search query (using organization context)
          response = await devicesApiRequests.searchDevices(debouncedSearchQuery, limit);
        } else {
          // Use regular getDevices API when no search (using organization context)
          let filters: any = {};

          // If user filter is selected, only use userId filter
          if (selectedUserId) {
            filters = { userId: selectedUserId };
          } else {
            // Use other filters when no user is selected
            filters = {
              name: searchParams.name,
              deviceType: searchParams.deviceType,
              deviceId: searchParams.deviceId,
            };
          }

          response = await devicesApiRequests.getDevices(page, limit, filters);
        }

        // Check if component is still mounted before setting state
        if (!abortController.signal.aborted) {
          const devices = get(response, "payload.docs", []).map(
            (device: Device) => {
              return {
                ...device,
                status: isDeviceOnline(
                  new Date(device.connectedAt),
                  new Date(device.disconnectedAt)
                )
                  ? DEVICE_STATUS.ONLINE
                  : DEVICE_STATUS.OFFLINE,
              };
            }
          );

          setDevices(devices);
          setTotalDocs(get(response, "payload.totalDocs", 0));
          setTotalPages(get(response, "payload.totalPages", 1));
          setLimit(get(response, "payload.limit", 10));
          setHasPrevPage(get(response, "payload.hasPrevPage", false));
          setHasNextPage(get(response, "payload.hasNextPage", false));
        }
      } catch (error) {
        if (!abortController.signal.aborted) {
          console.log("Error fetching devices: ", error);
        }
      } finally {
        if (!abortController.signal.aborted) {
          setLoading(false);
        }
      }
    };

    fetchDevices();
    
    return () => {
      abortController.abort();
    };
  }, [limit, page, debouncedSearchQuery, selectedUserId, isLoading, currentOrgId]); // Added currentOrgId to refetch when organization changes

  // Memoized payload handlers to avoid recreating functions
  const payloadHandler = useCallback((payload: any, currentDevices: Device[]) => {
    const deviceIds: string[] = get(payload, "headers.deviceIds", []);
    const body = get(payload, "body", {});
    
    // Only process if there are relevant device IDs
    if (!deviceIds.length) return currentDevices;
    
    // Use Set for faster lookup
    const deviceIdSet = new Set(deviceIds);
    
    return currentDevices.map((device) => {
      if (deviceIdSet.has(device.deviceId)) {
        const connectedAt = get(body, "connectedAt") || device.connectedAt;
        const disconnectedAt = get(body, "disconnectedAt") || device.disconnectedAt;
        const status = isDeviceOnline(connectedAt, disconnectedAt)
          ? DEVICE_STATUS.ONLINE
          : DEVICE_STATUS.OFFLINE;

        return {
          ...device,
          ...body,
          status,
        };
      }
      return device;
    });
  }, []);

  const handlePayloadWithReduce = useCallback((
    payloadArray: any[],
    currentDevices: Device[]
  ): Device[] => {
    return payloadArray.reduce((acc: Device[], payloadItem: any) => {
      const deviceIds: string[] = get(payloadItem, "headers.deviceIds", []);
      const body = get(payloadItem, "body", {});
      
      if (!deviceIds.length) return acc;
      
      const deviceIdSet = new Set(deviceIds);

      return acc.map((device) => {
        if (deviceIdSet.has(device.deviceId)) {
          const connectedAt = get(body, "connectedAt") || device.connectedAt;
          const disconnectedAt = get(body, "disconnectedAt") || device.disconnectedAt;
          const status = isDeviceOnline(connectedAt, disconnectedAt)
            ? DEVICE_STATUS.ONLINE
            : DEVICE_STATUS.OFFLINE;

          return {
            ...device,
            ...body,
            status,
          };
        }
        return device;
      });
    }, currentDevices);
  }, []);

  // Optimized payload processing with dependencies
  useEffect(() => {
    if (!payload || Object.keys(payload).length === 0) return;
    
    // Use functional update to get current devices state
    setDevices(currentDevices => {
      if (Array.isArray(payload)) {
        return handlePayloadWithReduce(payload, currentDevices);
      } else {
        return payloadHandler(payload, currentDevices);
      }
    });
  }, [payload, payloadHandler, handlePayloadWithReduce]);

  // Optimized MQTT connection with proper cleanup and connection management
  const mqttClientRef = useRef<any>(null);
  const isConnectedRef = useRef(false);
  
  useEffect(() => {
    // Prevent multiple connections
    if (mqttClientRef.current && isConnectedRef.current) {
      return;
    }

    const client = mqtt.connect(BROKER_URL, {
      clientId: `U-${Date.now()}-${userId}`,
      username: userId,
      reconnectPeriod: 5000, // Reconnect every 5 seconds if disconnected
      connectTimeout: 10000, // 10 second timeout
    });

    mqttClientRef.current = client;

    client.on("connect", () => {
      console.log("Connected to MQTT broker");
      isConnectedRef.current = true;
      client.subscribe(`user-on/${userId}`, (err) => {
        if (!err) {
          console.log("Subscribed to topic");
        } else {
          console.error("Failed to subscribe:", err);
        }
      });
    });

    client.on("disconnect", () => {
      console.log("Disconnected from MQTT broker");
      isConnectedRef.current = false;
    });

    client.on("error", (error) => {
      console.error("MQTT connection error:", error);
      isConnectedRef.current = false;
    });

    // Optimized message handler with error handling
    const handleMessage = (topic: string, message: Buffer) => {
      try {
        const messageStr = message.toString();
        if (messageStr.trim()) {
          const parsedPayload = JSON.parse(messageStr);
          // Only update if payload is different to prevent unnecessary re-renders
          setPayload(prevPayload => {
            const isSame = JSON.stringify(prevPayload) === JSON.stringify(parsedPayload);
            return isSame ? prevPayload : parsedPayload;
          });
        }
      } catch (error) {
        console.error("Error parsing MQTT message:", error);
      }
    };

    client.on("message", handleMessage);

    return () => {
      if (client) {
        client.removeAllListeners();
        client.end(true); // Force close
        mqttClientRef.current = null;
        isConnectedRef.current = false;
      }
    };
  }, [userId]);

  const renderInfoField = (
    label: string,
    value: string | undefined | null
  ): React.ReactNode => (
    <div>
      <span className="text-xs text-muted-foreground">{label}:</span>
      <p className="text-xs text-foreground">{value || "-"}</p>
    </div>
  );

  // Get device type specific styling using current theme colors
  const getDeviceTypeStyle = (deviceType: DEVICE_TYPE) => {
    // Base styling using theme variables
    const baseStyle = {
      gradient: "from-primary/8 via-primary/12 to-primary/8",
      border: "border-primary/15 dark:border-primary/25",
      iconBg: "from-primary/20 to-primary/30",
      iconColor: "text-primary",
      glowColor: "shadow-primary/15"
    };

    // Slight variations for different device types while maintaining theme consistency
    switch (deviceType) {
      case DEVICE_TYPE.INVERTER:
        return {
          ...baseStyle,
          gradient: "from-primary/8 via-accent/10 to-primary/8",
          iconBg: "from-primary/20 to-accent/25",
        };
      case DEVICE_TYPE.HUB:
        return {
          ...baseStyle,
          gradient: "from-primary/10 via-primary/15 to-primary/10",
          iconBg: "from-primary/25 to-primary/35",
        };
      case DEVICE_TYPE.SENSOR:
        return {
          ...baseStyle,
          gradient: "from-secondary/20 via-primary/8 to-secondary/20",
          iconBg: "from-secondary/30 to-primary/25",
        };
      case DEVICE_TYPE.RGB_LIGHT:
        return {
          ...baseStyle,
          gradient: "from-accent/15 via-primary/8 to-accent/15",
          iconBg: "from-accent/25 to-primary/25",
        };
      case DEVICE_TYPE.VT_1_PORT:
        return {
          ...baseStyle,
          gradient: "from-primary/8 via-secondary/15 to-primary/8",
          iconBg: "from-primary/20 to-secondary/25",
        };
      default:
        return {
          gradient: "from-muted/30 via-muted/20 to-muted/30",
          border: "border-border/30",
          iconBg: "from-muted/40 to-muted/50",
          iconColor: "text-muted-foreground",
          glowColor: "shadow-muted/10"
        };
    }
  };

  // Enhanced device icon with theme-based styling
  const getEnhancedDeviceIcon = (deviceType: DEVICE_TYPE) => {
    const iconClass = "h-6 w-6";
    // All icons use primary color from current theme for consistency
    const iconColorClass = "text-primary";

    switch (deviceType) {
      case DEVICE_TYPE.INVERTER:
        return <Zap className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.HUB:
        return <Router className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.SENSOR:
        return <Thermometer className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.RGB_LIGHT:
        return <Lightbulb className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.VT_1_PORT:
        return <Plug className={`${iconClass} ${iconColorClass}`} />;
      default:
        return <HelpCircle className={`${iconClass} text-muted-foreground`} />;
    }
  };

  // Optimized memoized device card component with enhanced design
  const DeviceCard = memo<{ device: Device; index: number }>(({ device, index }) => {
    const typeStyle = getDeviceTypeStyle(device.deviceType);
    const isUpdating = !isEmpty(device.otaProgress) && getOTAStatus(device.otaProgress) === OTA_STATUS.IN_PROGRESS;
    const [showUpdateDetails, setShowUpdateDetails] = useState(false);

    // Determine display status - prioritize updating status
    const displayStatus = isUpdating ? "UPDATING" : (device.status || "OFFLINE");
    const displayStatusColor = isUpdating
      ? "bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-800/50"
      : getStatusColor(device.status || "OFFLINE");

    return (
      <Card
        key={device.deviceId}
        className={`group relative overflow-hidden cursor-pointer transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl ${typeStyle.glowColor} bg-gradient-to-br ${typeStyle.gradient} backdrop-blur-sm border ${typeStyle.border} hover:border-opacity-50 device-card-glow ${device.status === DEVICE_STATUS.ONLINE ? 'online-glow' : ''} ${isUpdating ? 'updating-glow' : ''}`}
        onClick={() => handleOpenDetail(index)}
      >
        {/* Animated background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Glassmorphism overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-transparent backdrop-blur-[2px] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Updating overlay for visual emphasis */}
        {isUpdating && (
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-orange-400/5 to-orange-600/5 opacity-50" />
        )}

        {/* Selection Checkbox */}
        <div className="absolute top-3 left-3 z-10">
          <Checkbox
            checked={selectedDeviceIds.has(device.id)}
            onCheckedChange={(checked) => {
              handleDeviceSelect(device.id, checked as boolean);
            }}
            onClick={(e) => e.stopPropagation()}
            className="bg-white/90 border-gray-300 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 shadow-sm"
          />
        </div>

        <CardContent className="relative p-4 sm:p-6 h-full flex flex-col">
          {/* Header Section */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3 sm:gap-4 min-w-0 flex-1">
              {/* Enhanced Icon Container */}
              <div className={`relative p-3 sm:p-4 bg-gradient-to-br ${typeStyle.iconBg} rounded-2xl border border-white/20 backdrop-blur-sm group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 flex-shrink-0`}>
                {getEnhancedDeviceIcon(device.deviceType)}

                {/* Status indicator dot */}
                <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-900 ${
                  device.status === DEVICE_STATUS.ONLINE
                    ? 'bg-green-500 shadow-lg shadow-green-500/50'
                    : 'bg-gray-400'
                } ${device.status === DEVICE_STATUS.ONLINE ? 'animate-pulse' : ''}`} />
              </div>

              {/* Device Info */}
              <div className="min-w-0 flex-1 space-y-1">
                <h3 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-200 truncate" title={device.deviceType}>
                  {device.deviceType}
                </h3>
                <div className="flex items-center gap-2">
                  <span className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm border ${displayStatusColor} ${isUpdating ? 'animate-pulse' : device.status === DEVICE_STATUS.ONLINE ? 'status-float' : ''}`}>
                    {isUpdating ? (
                      <CircleFadingArrowUp className="h-3 w-3 animate-spin" />
                    ) : (
                      getStatusIcon(device.status)
                    )}
                    <span className="hidden sm:inline font-semibold">{displayStatus}</span>
                  </span>
                  {isUpdating && device.otaProgress && (
                    <>
                      <span className="text-xs font-bold text-orange-600 dark:text-orange-400 bg-orange-100/50 dark:bg-orange-900/20 px-2 py-1 rounded-full">
                        {device.otaProgress.progress}%
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowUpdateDetails(!showUpdateDetails);
                        }}
                        className="h-6 w-6 p-0 text-orange-600 dark:text-orange-400 hover:bg-orange-100/50 dark:hover:bg-orange-900/20 rounded-full transition-all duration-200"
                      >
                        {showUpdateDetails ? (
                          <ChevronUp className="h-3 w-3" />
                        ) : (
                          <ChevronDown className="h-3 w-3" />
                        )}
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Action Menu */}
            <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-lg transition-all duration-200"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="backdrop-blur-md bg-background/80">
                  <DropdownMenuItem
                    className="flex items-center gap-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenCreatePolicyDialog(index);
                    }}
                  >
                    <Shield className="h-4 w-4" />
                    Create Policy
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    disabled={device.deviceType !== DEVICE_TYPE.SENSOR}
                    className="flex items-center gap-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditDevice(index);
                    }}
                  >
                    <Wrench className="h-4 w-4" />
                    Config Parameters
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="flex items-center gap-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenUpgradeDialog(index);
                    }}
                  >
                    <CircleFadingArrowUp className="h-4 w-4" />
                    Upgrade Firmware
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="flex items-center gap-2 text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenDeleteDialog(index);
                    }}
                  >
                    <Trash className="h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Device Details - Always show */}
          <div className="space-y-3 pt-2 border-t border-white/10">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Device ID</span>
              <span className="text-sm font-mono text-foreground/80 truncate max-w-[120px]" title={device.deviceId}>
                {device.deviceId}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Firmware</span>
              <span className={`text-sm truncate max-w-[120px] ${device.firmware?.name ? 'text-foreground/80' : 'text-muted-foreground/60 italic'}`} title={device.firmware?.name || "No firmware"}>
                {device.firmware?.name || "No firmware"}
              </span>
            </div>
          </div>

          {/* OTA Progress - Expandable when updating */}
          {isUpdating && device.otaProgress && showUpdateDetails && (
            <div className="space-y-3 pt-3 border-t border-orange-200/30 dark:border-orange-800/30 bg-gradient-to-r from-orange-50/50 to-orange-100/50 dark:from-orange-950/20 dark:to-orange-900/30 rounded-lg p-3 -mx-2 border animate-in slide-in-from-top-2 duration-300">
              <div className="flex items-center justify-between">
                <span className="text-xs font-bold text-orange-600 dark:text-orange-400 uppercase tracking-wide flex items-center gap-1">
                  <CircleFadingArrowUp className="h-3 w-3 animate-spin" />
                  Updating Firmware
                </span>
                <span className="text-sm font-bold text-orange-600 dark:text-orange-400">
                  {device.otaProgress.progress}%
                </span>
              </div>
              <Progress
                value={device.otaProgress.progress}
                className="h-2 bg-orange-200/50 dark:bg-orange-800/30"
              />
              {device.otaProgress.status && (
                <p className="text-xs text-orange-600/80 dark:text-orange-400/80 font-medium">
                  Status: {device.otaProgress.status}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  });

  const GroupedGridView: React.FC = () => (
    <div className="p-6 pb-24 space-y-8">
      {Object.entries(groupedDevices).map(([groupType, groupDevices]) => {
        const typeInfo = getDeviceTypeInfo(groupType as DEVICE_TYPE);
        const isExpanded = expandedGroups.has(groupType);
        const onlineCount = groupDevices.filter(d => d.status === DEVICE_STATUS.ONLINE).length;

        return (
          <div key={groupType} className={`rounded-2xl border-2 ${typeInfo.borderColor} ${typeInfo.bgColor} overflow-hidden`}>
            {/* Group Header */}
            <div
              className={`p-6 bg-gradient-to-r ${typeInfo.color} text-white cursor-pointer hover:opacity-90 transition-all duration-200`}
              onClick={() => toggleGroup(groupType)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                    {getDeviceIcon(groupType as DEVICE_TYPE)}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">{typeInfo.name}</h3>
                    <p className="text-white/80 text-sm">
                      {groupDevices.length} devices • {onlineCount} online
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="text-2xl font-bold">{groupDevices.length}</div>
                    <div className="text-white/80 text-xs">Total</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-white hover:bg-white/20 transition-colors"
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-5 w-5" />
                    ) : (
                      <ChevronRight className="h-5 w-5" />
                    )}
                  </Button>
                </div>
              </div>
            </div>

            {/* Group Content */}
            {isExpanded && (
              <div className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 items-start">
                  {groupDevices.map((device) => {
                    // Find the original index in the devices array
                    const originalIndex = devices.findIndex(d => d.deviceId === device.deviceId);
                    return (
                      <DeviceCard key={device.deviceId} device={device} index={originalIndex} />
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  const GridView: React.FC = () => (
    <div className="p-6 pb-24">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 items-start">
        {filteredDevices.map((device, index) => (
          <DeviceCard key={device.deviceId} device={device} index={index} />
        ))}
      </div>
    </div>
  );

  const TableView: React.FC = () => (
    <div className="p-6 pb-24">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={selectedDeviceIds.size === devices.length && devices.length > 0}
                  onCheckedChange={handleSelectAll}
                  className="border-gray-300 data-[state=checked]:bg-blue-600"
                />
              </TableHead>
              <TableHead>Device Info</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Organization</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredDevices.map((device: Device, index: number) => (
              <TableRow key={device.deviceId} className="hover:bg-muted/30 transition-colors">
                <TableCell>
                  <Checkbox
                    checked={selectedDeviceIds.has(device.id)}
                    onCheckedChange={(checked) => {
                      handleDeviceSelect(device.id, checked as boolean);
                    }}
                    className="border-gray-300 data-[state=checked]:bg-blue-600"
                  />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-3 min-w-0">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex-shrink-0">
                      {getDeviceIcon(device.deviceType)}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="font-medium truncate" title={device.deviceId}>{device.deviceId}</div>
                      <div className="text-sm text-muted-foreground truncate" title={device.firmware?.name || "No firmware"}>
                        {device.firmware?.name || "No firmware"}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400">
                    {getDeviceIcon(device.deviceType)}
                    {device.deviceType}
                  </span>
                </TableCell>
                <TableCell>
                  {(() => {
                    const isUpdating = !isEmpty(device.otaProgress) && getOTAStatus(device.otaProgress) === OTA_STATUS.IN_PROGRESS;
                    const displayStatus = isUpdating ? "UPDATING" : device.status;
                    const displayStatusColor = isUpdating
                      ? "bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900/30 dark:text-orange-400 dark:border-orange-800/50"
                      : getStatusColor(device.status);

                    return (
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs flex items-center gap-1 ${displayStatusColor} ${isUpdating ? 'animate-pulse' : ''}`}>
                          {isUpdating ? (
                            <CircleFadingArrowUp className="h-3 w-3 animate-spin" />
                          ) : (
                            getStatusIcon(device.status)
                          )}
                          {displayStatus}
                        </span>
                        {isUpdating && device.otaProgress && (
                          <span className="text-xs font-bold text-orange-600 dark:text-orange-400 bg-orange-100/50 dark:bg-orange-900/20 px-2 py-1 rounded-full">
                            {device.otaProgress.progress}%
                          </span>
                        )}
                      </div>
                    );
                  })()}
                </TableCell>
                <TableCell>
                  <span className="truncate block" title={device.org.orgName}>{device.org.orgName}</span>
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {device.createdAt
                    ? format(new Date(device.createdAt), "MMM dd, yyyy")
                    : "-"}
                </TableCell>
                <TableCell>
                  <div className="flex gap-1">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          className="flex items-center gap-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenCreatePolicyDialog(index);
                          }}
                        >
                          <Shield className="h-4 w-4" />
                          Create Policy
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          disabled={device.deviceType !== DEVICE_TYPE.SENSOR}
                          className="flex items-center gap-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditDevice(index);
                          }}
                        >
                          <Wrench className="h-4 w-4" />
                          Config Parameters
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="flex items-center gap-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenUpgradeDialog(index);
                          }}
                        >
                          <CircleFadingArrowUp className="h-4 w-4" />
                          Upgrade Firmware
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="flex items-center gap-2 text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleOpenDeleteDialog(index);
                          }}
                        >
                          <Trash className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );

  return (
    <div className="p-6">
      {/* Modern Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
            <Server className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Device Manager
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Advanced device configuration and firmware management
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Enhanced Search Input */}
          <SearchInput
            value={searchQuery}
            onValueChange={handleSearchChange}
            placeholder="Search devices..."
            className="flex-1 sm:flex-none sm:w-64"
            isLoading={isSearching}
          />

          {/* User Filter Dropdown */}
          <div className="relative">
            <Popover open={isUserDropdownOpen} onOpenChange={setIsUserDropdownOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isUserDropdownOpen}
                  className="w-full sm:w-48 justify-between bg-background/50 backdrop-blur-sm border-border/50"
                >
                  <div className="flex items-center gap-2">
                    <UserIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">
                      {selectedUser ?
                        `${selectedUser.firstName} ${selectedUser.lastName}`.trim() || selectedUser.email
                        : "Filter by user..."
                      }
                    </span>
                  </div>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
                <Command key={users.length} shouldFilter={false}>
                  <CommandInput
                    placeholder="Search users..."
                    value={userSearchQuery}
                    onValueChange={setUserSearchQuery}
                  />
                  <CommandList>
                    <CommandEmpty>
                      {isUserSearching ? (
                        <div className="flex items-center justify-center p-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                          <span className="ml-2 text-sm text-muted-foreground">Searching...</span>
                        </div>
                      ) : userSearchQuery ? (
                        "No users found."
                      ) : (
                        "Type to search users..."
                      )}
                    </CommandEmpty>
                    <CommandGroup>
                      {selectedUser && (
                        <CommandItem
                          value="__clear__"
                          onSelect={() => {
                            handleClearUserFilter();
                            setIsUserDropdownOpen(false);
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-gray-400" />
                            <span className="text-muted-foreground">All users</span>
                          </div>
                        </CommandItem>
                      )}
                      {users.map((user) => (
                        <CommandItem
                          key={user.id}
                          value={`${user.firstName} ${user.lastName} ${user.email}`}
                          onSelect={() => {
                            setSelectedUserId(user.id || "");
                            setSelectedUser(user);
                            setUserSearchQuery("");
                            setIsUserDropdownOpen(false);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs font-medium">
                              {user.firstName?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || "U"}
                            </div>
                            <div className="flex flex-col">
                              <span className="font-medium text-sm">
                                {user.firstName} {user.lastName}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {user.email}
                              </span>
                            </div>
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Controls Row - Compact on mobile */}
          <div className="flex items-center gap-2 justify-between sm:justify-start">
            {/* View Toggle */}
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={(mode) => setViewMode(mode as ViewMode)}
              options={VIEW_TOGGLE_PRESETS.tableGrid}
            />

            {/* Group By Type Toggle */}
            {viewMode === "grid" && (
              <Button
                variant={groupByType ? "secondary" : "outline"}
                size="sm"
                onClick={() => {
                  const newGroupByType = !groupByType;
                  setGroupByType(newGroupByType);

                  // Only expand all groups by default when enabling grouping for the first time
                  // (when there's no saved expanded state)
                  if (newGroupByType && expandedGroups.size === 0) {
                    const allTypes = [...new Set(devices.map(d => d.deviceType))];
                    setExpandedGroups(new Set(allTypes));
                  }
                }}
                className="h-8 transition-all duration-200 bg-background/50 backdrop-blur-sm border-border/50"
              >
                <Layers className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Group by Type</span>
                <span className="sm:hidden">Group</span>
              </Button>
            )}

            {/* Add Device Button */}
            <Button
              onClick={() => setIsOpenAddDeviceDialog(true)}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add Device</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Devices</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {devices.length}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Server className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/30 border-green-200/50 dark:border-green-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Online Devices</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {devices.filter(d => d.status === DEVICE_STATUS.ONLINE).length}
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                <Wifi className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Sensors</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {devices.filter(d => d.deviceType === DEVICE_TYPE.SENSOR).length}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Thermometer className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/30 border-orange-200/50 dark:border-orange-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Updating</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {devices.filter(d => d.otaProgress && getOTAStatus(d.otaProgress) === OTA_STATUS.IN_PROGRESS).length}
                </p>
              </div>
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <CircleFadingArrowUp className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions Toolbar */}
      {selectedDeviceIds.size > 0 && (
        <Card className="mb-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedDeviceIds.size === devices.length && devices.length > 0}
                    onCheckedChange={handleSelectAll}
                    className="border-blue-300 data-[state=checked]:bg-blue-600"
                  />
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    {selectedDeviceIds.size} of {devices.length} devices selected
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSelection}
                  className="text-blue-600 hover:text-blue-700 hover:bg-blue-100 dark:text-blue-400 dark:hover:bg-blue-900/20"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <DropdownMenu open={isBulkActionsOpen} onOpenChange={setIsBulkActionsOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="default"
                      size="sm"
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                    >
                      <MoreVertical className="h-4 w-4 mr-2" />
                      Bulk Actions
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem
                      onClick={handleOpenBulkFirmwareUpdate}
                      className="flex items-center gap-2"
                    >
                      <CircleFadingArrowUp className="h-4 w-4" />
                      Update Firmware
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-xl">
        <CardContent className="p-0 relative">
          {loading && (
            <div className="absolute inset-0 bg-background/60 backdrop-blur-sm z-10 flex items-center justify-center">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                Updating devices...
              </div>
            </div>
          )}
          {viewMode === "grid" ? (
            groupByType ? <GroupedGridView /> : <GridView />
          ) : (
            <TableView />
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      <CustomPagination
        viewMode={viewMode}
        totalDocs={totalDocs}
        limit={limit}
        page={page}
        totalPages={totalPages}
        hasPrevPage={hastPrevPage}
        hasNextPage={hasNextPage}
        onPageChange={handlePageChange}
        onLimitChange={(limit) => setLimit(limit)}
      />

      {/* Dialogs */}
      {selectedDeviceIndex != null && (
        <EditDeviceDialog
          device={devices[selectedDeviceIndex]}
          open={isOpenSettingsDialog}
          onOpenChange={() => setIsOpenSettingsDialog(!isOpenSettingsDialog)}
          onEditDevice={() => setIsLoading(!isLoading)}
        />
      )}

      <AddDeviceDialog
        open={isOpenAddDeviceDialog}
        onOpenChange={() => setIsOpenAddDeviceDialog(!isOpenAddDeviceDialog)}
        onAddDevice={handleAddDevice}
      />

      {selectedDeviceIndex != null && (
        <DeviceDetailDialog
          device={devices[selectedDeviceIndex]}
          open={isOpenDetailDialog}
          onOpenChange={() => setIsOpenDetailDialog(!isOpenDetailDialog)}
        />
      )}

      {selectedDeviceIndex != null && (
        <DeleteConfirmationModal
          device={devices[selectedDeviceIndex]}
          open={isOpenDeleteDialog}
          onClose={() => setIsOpenDeleteDialog(!isOpenDeleteDialog)}
          onConfirmDelete={handleDeleteDevice}
        />
      )}

      {selectedDeviceIndex != null && (
        <FirmwareUpdateDialog
          device={currentDeviceSelected.current}
          open={isOpenFirmwareUpgradeDialog}
          orgId={orgId}
          otaProgress={currentOtaProgress}
          onClose={() =>
            setIsOpenFirmwareUpgradeDialog(!isOpenFirmwareUpgradeDialog)
          }
          onFinish={() => setIsLoading(!isLoading)}
        />
      )}

      {selectedDeviceIndex != null && (
        <CreatePolicyDialog
          open={isOpenCreatePolicyDialog}
          onOpenChange={setIsOpenCreatePolicyDialog}
          device={devices[selectedDeviceIndex]}
          orgId={orgId}
        />
      )}

      {/* Bulk Firmware Update Dialog */}
      <BulkFirmwareUpdateDialog
        open={isBulkFirmwareUpdateOpen}
        onOpenChange={setIsBulkFirmwareUpdateOpen}
        selectedDeviceIds={selectedDeviceIds}
        selectedDevicesCount={selectedDeviceIds.size}
        onConfirmUpdate={handleBulkFirmwareUpdate}
        orgId={orgId}
      />
    </div>
  );
});

DeviceManagement.displayName = 'DeviceManagement';

export default DeviceManagement;
