"use client";

import React, {useEffect} from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {CalendarIcon, Ellipsis, Loader2} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import sensorsApiRequests from "@/apiRequests/sensors";
import { useToast } from "@/hooks/use-toast";
import moment from "moment";
import {clientUserInfo} from "@/lib/http";
import {Device, DEVICE_STATUS, DEVICE_TYPE} from "@/app/(DashboardLayout)/devices-manager/types";
import userDevicesApiRequests from "@/apiRequests/user/devices";
import {get} from "lodash";
import {UserDevice} from "@/app/(DashboardLayout)/devices/Devices";
interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (dateRange: { from: Date; to: Date; deviceIds: string[] }) => void;
  title?: string;
  className?: string;
}

const ExportDialog: React.FC<ExportDialogProps> = ({
  open,
  onOpenChange,
  onExport,
  title = "Xuất dữ liệu",
  className = "",
}) => {
  const [dateFrom, setDateFrom] = React.useState<Date>();
  const [dateTo, setDateTo] = React.useState<Date>();
  const [devices, setDevices] = React.useState<UserDevice[]>([]);
  const [selectedDevices, setSelectedDevices] = React.useState<UserDevice[]>([]);
  const [searchQuery, setSearchQuery] = React.useState<string>("");
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [exporting, setExporting] = React.useState<boolean>(false);
  const [cancelRequest, setCancelRequest] = React.useState<() => void>(() => () => {});
  const [selectAll, setSelectAll] = React.useState<boolean>(false);
  const { user } = clientUserInfo.userInfo;
  const {id: userId } = user;
  const { toast } = useToast();

  useEffect(() => {
    if (!open) {
      handleCancelExport();
    }
  }, [open]);
  
  const handleCancelExport = () => {
    cancelRequest();
    setExporting(false);
  };

  const fetchDevices = React.useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await userDevicesApiRequests.getUserDevices(userId, 1, 1000, {
        deviceType: DEVICE_TYPE.SENSOR,
        deviceId: searchQuery,
        keyword: searchQuery,
      });

      const devices = get(response, "payload.docs", []).map((policy: any) => ({
        ...policy.device,
        deviceName: policy.deviceName,
      }));
      setDevices(devices || []);

    } catch (error) {
      console.log("Failed to fetch devices:", error);
      setDevices([]);
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery]);

  React.useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchDevices();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [fetchDevices]);

  const handleDeviceToggle = (device: UserDevice) => {
    if (!device) return;

    // Currently only allow selecting one device for low performance
    setSelectedDevices([device]);
    // setSelectedDevices((prev) => {
    //   const isSelected = prev.some((d) => d.id === device.id);
    //   if (isSelected) {
    //     return prev.filter((d) => d.id !== device.id);
    //   } else {
    //     return [...prev, device];
    //   }
    // });
  };

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    if (!selectAll) {
      setSelectedDevices(devices || []);
    } else {
      setSelectedDevices([]);
    }
  };

  const handleExport = async () => {
    if (dateFrom && dateTo && selectedDevices.length > 0) {
      try {
        setExporting(true);
        await sensorsApiRequests.export({
          from: moment(dateFrom).startOf('day').toISOString(),
          to: moment(dateTo).endOf('day').toISOString(),
          deviceIds: selectedDevices.map((device) => device.id),
        }, (cancelFn) => setCancelRequest(() => cancelFn));

        toast({
          title: "Export success",
          description: "File successfully exported.",
        });
        onExport({
          from: dateFrom,
          to: dateTo,
          deviceIds: selectedDevices.map((device) => device.id),
        });
        onOpenChange(false);
      } catch (error: any) {
        if (error.includes('signal is aborted without reason')) {
          toast({
            variant: "destructive",
            title: "Export failed",
            description: "Export was cancelled.",
          });
        } else {
          toast({
            variant: "destructive",
            title: "Export failed",
            description: get(error, "message", "Failed to export data."),
          });
        }
      } finally {
        setExporting(false);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn("w-full max-w-md sm:max-w-[500px]", className)}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Date Range Section - Stack on mobile, side by side on desktop */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Date From Picker */}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">Từ ngày</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">
                      {dateFrom ? format(dateFrom, "dd/MM/yyyy") : "Chọn ngày"}
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateFrom}
                    onSelect={setDateFrom}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Date To Picker */}
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">Đến ngày</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">
                      {dateTo ? format(dateTo, "dd/MM/yyyy") : "Chọn ngày"}
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateTo}
                    onSelect={setDateTo}
                    initialFocus
                    disabled={(date) => (dateFrom ? date < dateFrom : false)}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Device Selection */}
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Thiết bị</label>
            <Command className="border rounded-md">
              <CommandInput
                placeholder="Tìm kiếm thiết bị..."
                value={searchQuery}
                onValueChange={setSearchQuery}
                className="text-sm"
              />
              <CommandList className="max-h-[200px] overflow-y-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  <CommandGroup>
                    {devices && devices.length > 0 ? (
                      devices.map(
                        (device) =>
                          device && (
                            <CommandItem
                              key={device.id}
                              value={device.deviceName}
                              onSelect={() => handleDeviceToggle(device)}
                              className="cursor-pointer"
                            >
                              <div className="flex items-center space-x-2 w-full">
                                <Checkbox
                                  checked={selectedDevices.some(
                                    (d) => d.id === device.id
                                  )}
                                  className="flex-shrink-0"
                                />
                                <span className="text-sm truncate flex-1">
                                  {device.deviceName}
                                </span>
                              </div>
                            </CommandItem>
                          )
                      )
                    ) : (
                      <CommandEmpty>Không tìm thấy thiết bị</CommandEmpty>
                    )}
                  </CommandGroup>
                )}
              </CommandList>
            </Command>
            {selectedDevices.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2 max-h-[100px] overflow-y-auto">
                {selectedDevices.map((device) => (
                  <Badge
                    key={device.id}
                    variant="secondary"
                    className="flex items-center gap-1 text-xs"
                  >
                    <span className="truncate max-w-[120px]">
                      {device.deviceName}
                    </span>
                    <button
                      className="ml-1 hover:text-destructive flex-shrink-0"
                      onClick={() => handleDeviceToggle(device)}
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="mt-4 space-y-3">
            {exporting ? (
              <div className="space-y-3">
                <div className="flex items-center justify-center text-sm text-muted-foreground">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                  Đang xuất dữ liệu...
                </div>
                <Button
                  onClick={handleCancelExport}
                  variant="outline"
                  className="w-full"
                >
                  Hủy xuất dữ liệu
                </Button>
              </div>
            ) : (
              <Button
                onClick={handleExport}
                disabled={!dateFrom || !dateTo || selectedDevices.length === 0}
                className="w-full"
              >
                Xuất dữ liệu
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ExportDialog;
