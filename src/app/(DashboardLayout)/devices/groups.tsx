import React, { useEffect, useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  Di<PERSON>Trigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Edit,
  Trash2,
  Activity,
  Power,
  SlidersHorizontal,
  Search,
  Share2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import userDevicesApiRequests from "@/apiRequests/user/devices";
import groupsApiRequests from "@/apiRequests/user/groups";
import { clientUserInfo } from "@/lib/http";
import { useToast } from "@/hooks/use-toast";
import { Group } from "./types";

import ShareDialog from "./ShareDialog";
import DeleteGroupConfirmation from "./ConfirmDeleteDialog";
import {
  Device,
  DEVICE_TYPE,
} from "@/app/(DashboardLayout)/devices-manager/types";

interface DeviceCardProps {
  device: Device;
  onClick?: () => void;
  showDetails?: boolean;
  isAssignable?: boolean;
}

interface SensorValuesProps {
  values: Record<string, any>;
  config: any;
}

interface ParameterInfo {
  unit: string;
  field?: string;
}

const formatDeviceType = (type: string): string => {
  return type
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

const formatDateTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString();
};

const isDeviceOnline = (device: Device): boolean => {
  return Boolean(
    device.connectedAt &&
      (!device.disconnectedAt ||
        new Date(device.connectedAt) > new Date(device.disconnectedAt))
  );
};

const DeviceGroupManagement: React.FC = () => {
  const [groups, setGroups] = useState<Group[]>([]);
  const [devices, setDevices] = useState<Device[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [newGroupName, setNewGroupName] = useState<string>("");
  const [editGroupName, setEditGroupName] = useState<string>("");
  const [showNewGroupDialog, setShowNewGroupDialog] = useState<boolean>(false);
  const [showEditNameDialog, setShowEditNameDialog] = useState<boolean>(false);
  const [showAssignDialog, setShowAssignDialog] = useState<boolean>(false);
  const [showShareDialog, setShowShareDialog] = useState<boolean>(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [limit] = useState<number>(50);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [total, setTotal] = useState<number>(0);

  const [assignedDevices, setAssignedDevices] = useState<Device[]>([]);
  const [unassignedDevices, setUnassignedDevices] = useState<Device[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [selectedGroupForShare, setSelectedGroupForShare] =
    useState<Group | null>(null);

  const [groupToDelete, setGroupToDelete] = useState<Group | null>(null);

  const [searchQueryKeyword, setSearchQueryKeyword] = useState<string>("");
  const [searchQueryDeviceId, setSearchQueryDeviceId] = useState<string>("");
  const [searchQueryDeviceType, setSearchQueryDeviceType] =
    useState<string>("");
  const { user } = clientUserInfo.userInfo;
  const { id: userId, orgId } = user;

  const { toast } = useToast();

  const fetchDevices = useCallback(async () => {
    try {
      const response = await userDevicesApiRequests.getUserDevices(
        userId,
        1,
        1000,
        {
          keyword: searchQueryKeyword,
          deviceId: searchQueryDeviceId,
          deviceType: searchQueryDeviceType,
        }
      );

      if (response?.payload?.docs) {
        const fetchedDevices = response.payload.docs
          .filter((doc: any) => doc?.device)
          .map((doc: any) => doc.device);

        // Update both devices and unassigned devices while preserving assigned ones
        setDevices(fetchedDevices || []);

        // Update unassigned devices while keeping current assignments
        if (selectedGroup) {
          const assignedDeviceIds = new Set(assignedDevices.map((d) => d.id));
          setUnassignedDevices(
            fetchedDevices.filter(
              (device: Device) => !assignedDeviceIds.has(device.id)
            )
          );
        }
      } else {
        setDevices([]);
        setUnassignedDevices([]);
      }
    } catch (error) {
      console.log("Failed to fetch devices:", error);
      setDevices([]);
      setUnassignedDevices([]);
    }
  }, [
    searchQueryDeviceType,
    searchQueryDeviceId,
    searchQueryKeyword,
    selectedGroup,
    assignedDevices,
  ]);

  const handleCreateGroup = async (): Promise<void> => {
    if (!newGroupName.trim()) return;
    try {
      const response = await groupsApiRequests.createGroup(userId, {
        name: newGroupName,
        deviceIds: [],
      });
      setGroups((prevGroups) => [...prevGroups, response.payload]);
      setNewGroupName("");
      setShowNewGroupDialog(false);
    } catch (error) {
      console.log("Error creating group:", error);
    }
  };

  const handleUpdateGroupName = async (): Promise<void> => {
    if (!selectedGroup || !editGroupName.trim()) return;
    try {
      const updatedGroup = {
        name: editGroupName,
      };
      await groupsApiRequests.updateGroup(
        userId,
        selectedGroup.id,
        updatedGroup
      );
      setGroups((prevGroups) =>
        prevGroups.map((group) =>
          group.id === selectedGroup.id
            ? { ...group, name: editGroupName }
            : group
        )
      );
      setShowEditNameDialog(false);
    } catch (error) {
      console.log("Error updating group name:", error);
    }
  };

  // const handleDeleteGroup = async (groupId: string): Promise<void> => {
  //   try {
  //     await groupsApiRequests.deleteGroup(groupId);
  //     setGroups((prevGroups) =>
  //       prevGroups.filter((group) => group.id !== groupId)
  //     );
  //   } catch (error) {
  //     console.log("Error deleting group:", error);
  //   }
  // };

  const handleDeleteGroup = async (group: Group): Promise<void> => {
    setGroupToDelete(group);
    setShowDeleteConfirmation(true);
  };

  const handleConfirmDelete = async (group: Group): Promise<void> => {
    try {
      await groupsApiRequests.deleteGroup(userId, group.id);
      setGroups((prevGroups) => prevGroups.filter((g) => g.id !== group.id));
      toast({
        title: "Thành công",
        description: "Đã xóa nhóm thành công",
      });
    } catch (error) {
      console.log("Error deleting group:", error);
      throw error; // Propagate error to handle in the confirmation dialog
    }
  };

  const handleEditGroup = async (group: Group): Promise<void> => {
    setSelectedGroup(group);

    try {
      const groupResponse = await groupsApiRequests.getGroup(userId, group.id);
      const groupWithDevices = groupResponse.payload;

      const assignedDevs = groupWithDevices.devices || [];
      setAssignedDevices(assignedDevs);

      const assignedDeviceIds = new Set(assignedDevs.map((d: Device) => d.id));
      setUnassignedDevices(
        devices.filter((device) => !assignedDeviceIds.has(device.id))
      );

      setShowAssignDialog(true);
    } catch (error) {
      console.log("Error loading group devices:", error);
    }
  };

  const handleAssignDevices = async (): Promise<void> => {
    if (!selectedGroup) return;
    try {
      const updatedGroup = {
        ...selectedGroup,
        deviceIds: assignedDevices.map((device) => device.id),
      };
      await groupsApiRequests.updateGroup(
        userId,
        selectedGroup.id,
        updatedGroup
      );

      setGroups((prevGroups) =>
        prevGroups.map((group) =>
          group.id === selectedGroup.id
            ? { ...group, devices: assignedDevices }
            : group
        )
      );

      setShowAssignDialog(false);
    } catch (error) {
      console.log("Error updating group:", error);
    }
  };

  const handleShareDevice = async (
    device?: Device,
    groupId?: string
  ): Promise<void> => {
    if (device) {
      setSelectedDevice(device);
      setSelectedGroupForShare(null);
    } else if (groupId) {
      const group = groups.find((g) => g.id === groupId);
      if (group) {
        setSelectedGroupForShare(group);
        setSelectedDevice(null);
      }
    }
    setShowShareDialog(true);
  };

  useEffect(() => {
    const loadGroups = async () => {
      try {
        const response = await groupsApiRequests.getGroups(userId, page, limit);
        response?.payload?.docs && setGroups(response.payload.docs);
        response?.payload?.totalDocs && setTotal(response.payload.totalDocs);
        response?.payload?.totalPages &&
          setTotalPages(response.payload.totalPages);
      } catch (error) {
        console.log("Error loading groups:", error);
      }
    };
    loadGroups();
  }, [page, limit]);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchDevices();
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [fetchDevices]);

  const DeviceFilters: React.FC = () => {
    const [localSearchValue, setLocalSearchValue] =
      useState(searchQueryKeyword);

    useEffect(() => {
      const debounceTimer = setTimeout(() => {
        setSearchQueryKeyword(localSearchValue);
      }, 800);

      return () => clearTimeout(debounceTimer);
    }, [localSearchValue]);

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 p-4 bg-gray-50 rounded-lg">
          <Search className="h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search by name or ID"
            value={localSearchValue}
            onChange={(e) => setLocalSearchValue(e.target.value)}
            className="flex-1"
          />
          <Select
            value={searchQueryDeviceType || "all"}
            onValueChange={(value) =>
              setSearchQueryDeviceType(value === "all" ? "" : value)
            }
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Device Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {Object.values(DEVICE_TYPE).map((type) => (
                <SelectItem key={type} value={type}>
                  {formatDeviceType(type)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };

  const SensorValues: React.FC<SensorValuesProps> = ({ values, config }) => {
    if (!values || Object.keys(values).length === 0) return null;

    const getParameterInfo = (field: string): ParameterInfo => {
      if (!config?.parameter) return { unit: "" };
      return (
        config.parameter.find((p: ParameterInfo) => p.field === field) || {
          unit: "",
        }
      );
    };

    const parameterGroups: Record<string, string[]> = {
      voltage: ["u1", "u2", "u3"],
      current: ["i1", "i2", "i3"],
      power: ["p", "q", "s", "pf"],
      other: ["f", "kwh"],
    };

    return (
      <Accordion type="single" collapsible className="w-full">
        {Object.entries(parameterGroups).map(([group, params]) => {
          const hasValues = params.some((param) => values[param]);
          if (!hasValues) return null;

          return (
            <AccordionItem value={group} key={group}>
              <AccordionTrigger className="text-sm">
                {group.charAt(0).toUpperCase() + group.slice(1)} Parameters
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-2">
                  {params.map((param) => {
                    if (!values[param]) return null;
                    const { unit } = getParameterInfo(param);
                    return (
                      <div key={param} className="flex justify-between">
                        <span className="text-gray-600">
                          {param.toUpperCase()}:
                        </span>
                        <span>
                          {values[param]}
                          {unit}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    );
  };

  const DeviceCard: React.FC<DeviceCardProps> = ({
    device,
    onClick,
    showDetails = false,
    isAssignable = false,
  }) => {
    const [expandedValues, setExpandedValues] = useState(false);

    return (
      <div
        className={`flex flex-col p-3 rounded-lg border ${
          isAssignable ? "hover:bg-gray-100 cursor-pointer" : ""
        } mb-2`}
      >
        <div
          className="flex justify-between items-start"
          onClick={isAssignable ? onClick : undefined}
        >
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="font-medium">{device.deviceId}</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Badge
                      variant={isDeviceOnline(device) ? "default" : "secondary"}
                    >
                      {isDeviceOnline(device) ? (
                        <Activity className="w-3 h-3 mr-1" />
                      ) : (
                        <Power className="w-3 h-3 mr-1" />
                      )}
                      {isDeviceOnline(device) ? "Online" : "Offline"}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Last Connected: {formatDateTime(device?.connectedAt)}</p>
                    {device.disconnectedAt && (
                      <p>
                        Last Disconnected:{" "}
                        {formatDateTime(device?.disconnectedAt)}
                      </p>
                    )}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="text-sm text-gray-500 mt-1">
              <div>{formatDeviceType(device.deviceType)}</div>
              {showDetails && (
                <>
                  <div>Created: {formatDateTime(device?.createdAt)}</div>
                  <div>Updated: {formatDateTime(device?.updatedAt)}</div>
                </>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="ml-auto"
            onClick={(e) => {
              e.stopPropagation();
              handleShareDevice(device);
            }}
          >
            <Share2 className="h-4 w-4" />
          </Button>
          {device.deviceType === "SENSOR" && (
            <Button
              variant="ghost"
              size="sm"
              className="ml-2"
              onClick={(e) => {
                e.stopPropagation();
                setExpandedValues(!expandedValues);
              }}
            >
              <SlidersHorizontal className="h-4 w-4" />
            </Button>
          )}
        </div>

        {expandedValues && device.deviceType === "SENSOR" && (
          <div className="mt-3 border-t pt-3">
            <SensorValues values={device.value} config={device.config} />
          </div>
        )}
      </div>
    );
  };

  const AssignDevicesDialogContent: React.FC = () => (
    <div className="space-y-6">
      <DeviceFilters />

      <div className="grid grid-cols-2 gap-6">
        <div>
          <h3 className="text-sm font-semibold mb-3">
            Available Devices ({unassignedDevices.length})
          </h3>
          <div className="border rounded-lg p-3 max-h-[500px] overflow-y-auto">
            {unassignedDevices.map((device) => (
              <DeviceCard
                key={device.id}
                device={device}
                onClick={() => {
                  setAssignedDevices((prev) => [...prev, device]);
                  setUnassignedDevices((prev) =>
                    prev.filter((d) => d.id !== device.id)
                  );
                }}
                isAssignable={true}
              />
            ))}
          </div>
        </div>
        <div>
          <h3 className="text-sm font-semibold mb-3">
            Assigned Devices ({assignedDevices.length})
          </h3>
          <div className="border rounded-lg p-3 max-h-[500px] overflow-y-auto">
            {assignedDevices.map((device) => (
              <DeviceCard
                key={device.id}
                device={device}
                onClick={() => {
                  setUnassignedDevices((prev) => [...prev, device]);
                  setAssignedDevices((prev) =>
                    prev.filter((d) => d.id !== device.id)
                  );
                }}
                isAssignable={true}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-4 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Device Groups</h1>
          <p className="text-gray-500 mt-1">
            Manage your device groups and assignments
          </p>
        </div>
        <Dialog open={showNewGroupDialog} onOpenChange={setShowNewGroupDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Group
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Group</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <Input
                placeholder="Group Name"
                value={newGroupName}
                onChange={(e) => setNewGroupName(e.target.value)}
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowNewGroupDialog(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateGroup}>Create</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6">
        {groups.map((group) => (
          <Card key={group.id}>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>{group.name}</CardTitle>
                  <p className="text-sm text-gray-500 mt-1">
                    {group.devices?.length || 0} devices assigned
                  </p>
                </div>
                <div className="flex gap-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            handleShareDevice(undefined, group.id);
                          }}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Share group</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            setSelectedGroup(group);
                            setEditGroupName(group.name);
                            setShowEditNameDialog(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Edit group name</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleEditGroup(group)}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Assign devices</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleDeleteGroup(group)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Delete group</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            </CardHeader>
            {group.devices && group.devices.length > 0 && (
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {group.devices.map((device) => (
                    <DeviceCard
                      key={device.id}
                      device={device}
                      showDetails={true}
                    />
                  ))}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Edit Name Dialog */}
      <Dialog open={showEditNameDialog} onOpenChange={setShowEditNameDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Group Name</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Group Name"
              value={editGroupName}
              onChange={(e) => setEditGroupName(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowEditNameDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateGroupName}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assign Devices Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Assign Devices to {selectedGroup?.name}</DialogTitle>
          </DialogHeader>
          <AssignDevicesDialogContent />
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAssignDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleAssignDevices}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ShareDialog
        open={showShareDialog}
        onOpenChange={setShowShareDialog}
        device={selectedDevice || undefined}
        group={selectedGroupForShare || undefined}
        onShare={(selectedUsers) => {
          // Handle successful share
          toast({
            title: "Success",
            description: `Successfully shared with ${selectedUsers.length} users`,
          });
        }}
      />

      {groupToDelete && (
        <DeleteGroupConfirmation
          group={groupToDelete}
          open={showDeleteConfirmation}
          onClose={() => {
            setShowDeleteConfirmation(false);
            setGroupToDelete(null);
          }}
          onConfirmDelete={handleConfirmDelete}
        />
      )}
    </div>
  );
};

export default DeviceGroupManagement;
