"use client";

import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import dynamic from "next/dynamic";
import PermissionGuard from "@/components/PermissionGuard";

// Dynamic import with loading component
const SmartHomeDashboard = dynamic(() => import("./Devices"), {
  loading: () => (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex flex-col md:flex-row items-center justify-between mb-6">
        <Skeleton className="h-8 w-48" />
        <div className="flex gap-4">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-48 w-full" />
        ))}
      </div>
    </div>
  ),
  ssr: false
});

export default function Devices() {
  return (
    <PermissionGuard
      // requiredPermissions={[
      //   { resource: 'device', action: 'get' }
      // ]}
      // fallbackToLegacy={true}
      showAccessDenied={true}
    >
      <Suspense fallback={<div>Loading devices...</div>}>
        <SmartHomeDashboard />
      </Suspense>
    </PermissionGuard>
  )
}