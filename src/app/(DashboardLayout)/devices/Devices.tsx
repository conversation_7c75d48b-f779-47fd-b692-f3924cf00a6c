"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingPage } from "@/components/ui/loading-states";

import { AnimatedContainer } from "@/components/ui/animations";
import { getDeviceStatusColor } from "@/lib/device-colors";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import {
  Settings,
  WifiOff,
  Wifi,
  AlertTriangle,
  Wrench,
  Zap,
  Lightbulb,
  Thermometer,
  Router,
  HelpCircle,
  Plug,
  TrendingUp,
  ChevronLeft,
  SlidersHorizontal,
  Download,
  Search,
  Smartphone,
  Activity,
  Shield,
} from "lucide-react";
import { Group } from "./types";
import groupsApiRequests from "@/apiRequests/user/groups";
import DeviceGroupManagement from "./groups";
import ExportDialog from "./ExportDialog";
import { useIframeDialog, IFRAME_CONFIGS } from "@/context/iframe-dialog-provider";
import { get, isEmpty } from "lodash";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import mqtt from "mqtt";
import { BROKER_URL } from "@/config/config";
import { clientUserInfo } from "@/lib/http";
import userDevicesApiRequests from "@/apiRequests/user/devices";
import {
  Device,
  DEVICE_STATUS,
  DEVICE_TYPE,
} from "@/app/(DashboardLayout)/devices-manager/types";
import { PolicyRole } from "@/app/(DashboardLayout)/policy/types";
import CustomPagination from "@/app/components/Pagination";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { IconRender } from "@/app/components/icon-picker";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { ViewToggle, VIEW_TOGGLE_PRESETS } from "@/app/components/view-toggle";


interface DeviceParameter {
  field: string;
  icon: string;
  unit: string;
}

export interface UserDevice extends Device {
  deviceName: string;
  userId: string;
  device: any;
}

interface DeviceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  device: UserDevice;
}

interface DeviceCardProps {
  device: UserDevice;
}

interface ViewMode {
  type: "devices" | "groups" | "groupManagement" | "deviceManagement";
  selectedTypes: DEVICE_TYPE[];
}

interface updateDevice {
  deviceName: string;
  policyRole: PolicyRole;
  attributePolicies: {};
  userId: string;
  deviceId: string;
}

const isDeviceOnline = (connectedAt?: Date, disconnectedAt?: Date): boolean => {
  if (!connectedAt) return false;
  if (!disconnectedAt) return true;
  return new Date(connectedAt) > new Date(disconnectedAt);
};

const SmartHomeDashboard: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>({
    type: "groups",
    selectedTypes: [],
  });
  const [displayMode, setDisplayMode] = useState<"table" | "grid">("grid");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [devices, setDevices] = useState<UserDevice[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [totalDocs, setTotalDocs] = useState<number>(0);
  const [hasPrevPage, setHasPrevPage] = useState<boolean>(false);
  const [hasNextPage, setHasNextPage] = useState<boolean>(false);
  const [payload, setPayload] = useState({});
  const [openExportDialog, setOpenExportDialog] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const { user } = clientUserInfo.userInfo;
  const { id: userId } = user;
  const { openIframeDialog } = useIframeDialog();

  const SettingsDropdown = React.forwardRef<HTMLButtonElement>((props, ref) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            ref={ref}
            {...props}
            className="p-2 hover:bg-background hover:shadow-sm rounded-md transition-all duration-200"
          >
            <Settings className="w-4 h-4 text-muted-foreground" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>Settings</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={() =>
                setViewMode({ ...viewMode, type: "groupManagement" })
              }
            >
              Groups management
              <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                setViewMode({ ...viewMode, type: "deviceManagement" })
              }
            >
              Devices management
              <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={() => setOpenExportDialog((prev) => !prev)}
            >
              Export Data
              <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  });

  SettingsDropdown.displayName = "SettingsDropdown";

  // Helper function to get display text for view mode
  const getViewModeDisplayText = (mode: string, isMobile: boolean = false) => {
    switch (mode) {
      case "devices":
        return isMobile ? "📱 Devices" : "📱 Devices View";
      case "groups":
        return isMobile ? "📂 Groups" : "📂 Groups View";
      case "deviceManagement":
        return isMobile ? "⚙️ Manage" : "⚙️ Device Management";
      default:
        return "Select view";
    }
  };

  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const res = await userDevicesApiRequests.getUserDevices(
          userId,
          page,
          limit
        );
        const devices = get(res.payload, "docs", []).map((policy: any) => ({
          ...policy.device,
          deviceName: policy.deviceName,
          status: isDeviceOnline(
            new Date(policy.device.connectedAt),
            new Date(policy.device.disconnectedAt)
          )
            ? DEVICE_STATUS.ONLINE
            : DEVICE_STATUS.OFFLINE,
        }));
        setDevices(devices);
        setTotalDocs(get(res, "payload.totalDocs", 0));
        setTotalPages(get(res, "payload.totalPages", 0));
        setHasNextPage(get(res, "payload.hasNextPage", false));
        setHasPrevPage(get(res, "payload.hasPrevPage", false));
      } catch (error) {
        // todo: toast error
        console.log("Error fetching devices:", error);
      } finally {
        setLoading(false);
      }
    };

    const fetchGroups = async () => {
      try {
        const res = await groupsApiRequests.getGroups(userId, page, limit);
        setGroups(get(res, "payload.docs", []));
        setTotalDocs(get(res, "payload.totalDocs", 0));
        setTotalPages(get(res, "payload.totalPages", 0));
        setHasNextPage(get(res, "payload.hasNextPage", false));
        setHasPrevPage(get(res, "payload.hasPrevPage", false));
      } catch (error) {
        // todo: toast error
        console.log("Error fetching groups:", error);
      } finally {
        setLoading(false);
      }
    };

    void fetchDevices();
    void fetchGroups();
  }, [page, limit, loading]);

  useEffect(() => {
    if (Array.isArray(payload)) {
      const newDevices = payload.reduce((acc: Device[], payloadItem: any) => {
        const deviceIds: string[] = get(payloadItem, "headers.deviceIds", []);
        const body = get(payloadItem, "body", {});

        return acc.map((device) => {
          if (deviceIds.includes(device.deviceId)) {
            const newData = {
              ...device,
              ...body,
            };

            const connectedAt = get(newData, "connectedAt");
            const disconnectedAt = get(newData, "disconnectedAt");
            const status = isDeviceOnline(
              connectedAt ? new Date(connectedAt) : undefined,
              disconnectedAt ? new Date(disconnectedAt) : undefined
            )
              ? DEVICE_STATUS.ONLINE
              : DEVICE_STATUS.OFFLINE;

            return {
              ...device,
              ...body,
              status,
            };
          }

          return device;
        });
      }, devices);

      setDevices(newDevices);
    } else {
      const deviceIds: string[] = get(payload, "headers.deviceIds", []);
      const body = get(payload, "body", {});

      const newDevices = devices.map((device) => {
        if (deviceIds.includes(device.deviceId)) {
          const newData = {
            ...device,
            ...body,
          };

          const connectedAt = get(newData, "connectedAt");
          const disconnectedAt = get(newData, "disconnectedAt");
          const status = isDeviceOnline(
            connectedAt ? new Date(connectedAt) : undefined,
            disconnectedAt ? new Date(disconnectedAt) : undefined
          )
            ? DEVICE_STATUS.ONLINE
            : DEVICE_STATUS.OFFLINE;

          return {
            ...device,
            ...body,
            status,
          };
        }

        return device;
      });

      setDevices(newDevices);
    }
  }, [payload]);

  useEffect(() => {
    const client = mqtt.connect(BROKER_URL, {
      clientId: `U-${Date.now()}-${userId}`,
      username: userId,
    });

    client.on("connect", () => {
      console.log("Connected to MQTT broker");
      client.subscribe(`user-on/${userId}`, (err) => {
        if (!err) {
          console.log("Subscribed to topic");
        }
      });
    });

    client.on("message", (_, message) => {
      try {
        const payload = JSON.parse(message.toString());
        setPayload(payload);
      } catch (error) {
        console.log("Error parsing message:", error);
      }
    });

    return () => {
      client.end();
    };
  }, [userId]);

  // Get device type specific styling using current theme colors
  const getDeviceTypeStyle = (deviceType: DEVICE_TYPE) => {
    // Base styling using theme variables
    const baseStyle = {
      gradient: "from-primary/8 via-primary/12 to-primary/8",
      border: "border-primary/15 dark:border-primary/25",
      iconBg: "from-primary/20 to-primary/30",
      iconColor: "text-primary",
      glowColor: "shadow-primary/15"
    };

    // Slight variations for different device types while maintaining theme consistency
    switch (deviceType) {
      case DEVICE_TYPE.INVERTER:
        return {
          ...baseStyle,
          gradient: "from-primary/8 via-accent/10 to-primary/8",
          iconBg: "from-primary/20 to-accent/25",
        };
      case DEVICE_TYPE.HUB:
        return {
          ...baseStyle,
          gradient: "from-primary/10 via-primary/15 to-primary/10",
          iconBg: "from-primary/25 to-primary/35",
        };
      case DEVICE_TYPE.SENSOR:
        return {
          ...baseStyle,
          gradient: "from-secondary/20 via-primary/8 to-secondary/20",
          iconBg: "from-secondary/30 to-primary/25",
        };
      case DEVICE_TYPE.RGB_LIGHT:
        return {
          ...baseStyle,
          gradient: "from-accent/15 via-primary/8 to-accent/15",
          iconBg: "from-accent/25 to-primary/25",
        };
      case DEVICE_TYPE.VT_1_PORT:
        return {
          ...baseStyle,
          gradient: "from-primary/8 via-secondary/15 to-primary/8",
          iconBg: "from-primary/20 to-secondary/25",
        };
      default:
        return {
          gradient: "from-muted/30 via-muted/20 to-muted/30",
          border: "border-border/30",
          iconBg: "from-muted/40 to-muted/50",
          iconColor: "text-muted-foreground",
          glowColor: "shadow-muted/10"
        };
    }
  };

  // Enhanced device icon with theme-based styling
  const getEnhancedDeviceIcon = (deviceType: DEVICE_TYPE) => {
    const iconClass = "h-6 w-6";
    // All icons use primary color from current theme for consistency
    const iconColorClass = "text-primary";

    switch (deviceType) {
      case DEVICE_TYPE.INVERTER:
        return <Zap className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.HUB:
        return <Router className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.SENSOR:
        return <Thermometer className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.RGB_LIGHT:
        return <Lightbulb className={`${iconClass} ${iconColorClass}`} />;
      case DEVICE_TYPE.VT_1_PORT:
        return <Plug className={`${iconClass} ${iconColorClass}`} />;
      default:
        return <HelpCircle className={`${iconClass} text-muted-foreground`} />;
    }
  };

  const getDeviceIcon = (deviceType: DEVICE_TYPE) => {
    const iconProps = "h-6 w-6";
    switch (deviceType) {
      case DEVICE_TYPE.INVERTER:
        return <Zap className={iconProps} />;
      case DEVICE_TYPE.HUB:
        return <Router className={iconProps} />;
      case DEVICE_TYPE.SENSOR:
        return <Thermometer className={iconProps} />;
      case DEVICE_TYPE.RGB_LIGHT:
        return <Lightbulb className={iconProps + " text-amber-500"} />;
      case DEVICE_TYPE.VT_1_PORT:
        return <Plug className={iconProps} />;
      default:
        return <HelpCircle className={iconProps} />;
    }
  };

  const getStatusIcon = (status: DEVICE_STATUS) => {
    const statusColors = getDeviceStatusColor(status);
    switch (status) {
      case DEVICE_STATUS.ONLINE:
        return <Wifi className={`h-4 w-4 ${statusColors.icon}`} />;
      case DEVICE_STATUS.OFFLINE:
        return <WifiOff className={`h-4 w-4 ${statusColors.icon}`} />;
      case DEVICE_STATUS.ERROR:
        return <AlertTriangle className={`h-4 w-4 ${statusColors.icon}`} />;
      case DEVICE_STATUS.MAINTENANCE:
        return <Wrench className={`h-4 w-4 ${statusColors.icon}`} />;
    }
  };

  const getStatusColor = (status: DEVICE_STATUS) => {
    switch (status) {
      case DEVICE_STATUS.ONLINE:
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case DEVICE_STATUS.OFFLINE:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      case DEVICE_STATUS.ERROR:
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case DEVICE_STATUS.MAINTENANCE:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
    }
  };

  const handlePageChange = (page: number) => {
    setPage(page);
  };

  const handleUpdateDevice = (deviceId: string, body: updateDevice) => {
    userDevicesApiRequests.updateUserDevice(userId, deviceId, body);
  };

  // DeviceDialog edit
  const [open, setOpen] = useState(false);
  const [deviceSelected, setDeviceSelected] = useState<UserDevice | null>(null);

  const handleDeviceClick = (device: UserDevice) => {
    setDeviceSelected(device);
    setOpen(true);
  };

  const DeviceDialog: React.FC<DeviceDialogProps> = ({ device }) => {
    const [deviceName, setDeviceName] = useState<string>(
      device?.deviceName || ""
    );
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="w-full max-w-md sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Cập nhật thiết bị</DialogTitle>
            <DialogDescription>
              Thay đổi thông tin thiết bị của bạn tại đây. Nhấn lưu khi hoàn tất.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* Mobile-friendly form layout */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  Tên thiết bị
                </Label>
                <Input
                  id="name"
                  className="w-full"
                  value={deviceName}
                  onChange={(e) => {
                    setDeviceName(e.target.value);
                  }}
                  placeholder="Nhập tên thiết bị..."
                />
              </div>
            </div>
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              className="w-full sm:w-auto"
            >
              Hủy
            </Button>
            <Button
              type="submit"
              onClick={() => {
                setOpen(false);
                setLoading(true);
                handleUpdateDevice(device.id, {
                  deviceName: deviceName,
                  policyRole: "deviceShared",
                  attributePolicies: {},
                  userId: device.userId,
                  deviceId: device.id,
                });
              }}
              className="w-full sm:w-auto"
            >
              Lưu thay đổi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // Enhanced Device Management with modern design
  const DeviceManagement: React.FC = () => {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6 w-full">
        {devices
          .filter(
            (device) =>
              viewMode.selectedTypes.length === 0 ||
              viewMode.selectedTypes.includes(device.deviceType)
          )
          .map((device) => {
            const typeStyle = getDeviceTypeStyle(device.deviceType);
            const statusColors = getDeviceStatusColor(device.status);

            return (
              <Card
                key={device.id}
                className={`group relative overflow-hidden cursor-pointer transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl ${typeStyle.glowColor} bg-gradient-to-br ${typeStyle.gradient} backdrop-blur-sm border ${typeStyle.border} hover:border-opacity-50 device-card-glow ${device.status === DEVICE_STATUS.ONLINE ? 'online-glow' : ''}`}
                onClick={() => handleDeviceClick(device)}
              >
                {/* Animated background gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Glassmorphism overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-transparent backdrop-blur-[2px] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <CardContent className="relative p-4 sm:p-6 space-y-4">
                  {/* Header Section */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3 sm:gap-4 min-w-0 flex-1">
                      {/* Enhanced Icon Container */}
                      <div className={`relative p-3 sm:p-4 bg-gradient-to-br ${typeStyle.iconBg} rounded-2xl border border-white/20 backdrop-blur-sm group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 flex-shrink-0`}>
                        {getEnhancedDeviceIcon(device.deviceType)}

                        {/* Status indicator dot */}
                        <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-900 ${
                          device.status === DEVICE_STATUS.ONLINE
                            ? 'bg-green-500 shadow-lg shadow-green-500/50'
                            : 'bg-gray-400'
                        } ${device.status === DEVICE_STATUS.ONLINE ? 'animate-pulse' : ''}`} />
                      </div>

                      {/* Device Info */}
                      <div className="min-w-0 flex-1 space-y-1">
                        <h3 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-200 truncate" title={device.deviceName}>
                          {device.deviceName}
                        </h3>
                        <p className="text-sm text-muted-foreground truncate" title={device.deviceType}>
                          {device.deviceType}
                        </p>
                        <div className="flex items-center gap-2">
                          <span className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm border border-white/20 ${statusColors.bg} ${statusColors.text} ${device.status === DEVICE_STATUS.ONLINE ? 'status-float' : ''}`}>
                            {getStatusIcon(device.status)}
                            <span className="hidden sm:inline">{device.status}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Hint */}
                  <div className="pt-3 border-t border-white/10">
                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                      <Settings className="w-3 h-3" />
                      <span>Click to edit device settings</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
      </div>
    );
  };

  // Enhanced DeviceCard with modern design
  const DeviceCard: React.FC<DeviceCardProps> = ({ device }) => {
    const [isOn, setIsOn] = useState(device.status === DEVICE_STATUS.ONLINE);
    const typeStyle = getDeviceTypeStyle(device.deviceType);

    const DeviceControls: React.FC = () => {
      if (device.deviceType === DEVICE_TYPE.RGB_LIGHT) {
        return (
          <div className="mt-4 w-full space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Brightness
              </label>
              <span className="text-sm font-bold text-foreground">
                {get(device.value, "brightness", 100) as number}%
              </span>
            </div>
            <Slider
              defaultValue={[get(device.value, "brightness", 100)] as number[]}
              max={100}
              step={1}
              className="mt-2"
            />
          </div>
        );
      }
      return null;
    };

    const renderParameters = () => {
      if (!device.config.parameter?.length) {
        return (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 text-center text-sm text-muted-foreground border border-white/20">
            No parameters available
          </div>
        );
      }

      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {device.config.parameter.map((param: DeviceParameter) => (
            <div
              key={param.field}
              className="bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/20 hover:bg-white/20 transition-all duration-200 group/param"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-primary group-hover/param:scale-110 transition-transform duration-200">
                    {IconRender({ iconName: param.icon })}
                  </span>
                  <span className="text-xs font-medium text-foreground">{param.field}</span>
                </div>
                <div className="text-right">
                  <span className="text-sm font-bold text-foreground">
                    {get(device.value, param.field) as string}
                  </span>
                  <span className="text-xs text-muted-foreground ml-1">{param.unit}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    };

    const statusColors = getDeviceStatusColor(device.status);

    return (
      <Card className={`w-full h-full group relative overflow-hidden cursor-pointer transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl ${typeStyle.glowColor} bg-gradient-to-br ${typeStyle.gradient} backdrop-blur-sm border ${typeStyle.border} hover:border-opacity-50 device-card-glow ${device.status === DEVICE_STATUS.ONLINE ? 'online-glow' : ''}`}>
        {/* Animated background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Glassmorphism overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-transparent backdrop-blur-[2px] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        <CardContent className="relative p-4 sm:p-6 h-full flex flex-col space-y-4">
          {/* Header Section */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 sm:gap-4 min-w-0 flex-1">
              {/* Enhanced Icon Container */}
              <div className={`relative p-3 sm:p-4 bg-gradient-to-br ${typeStyle.iconBg} rounded-2xl border border-white/20 backdrop-blur-sm group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 flex-shrink-0`}>
                {getEnhancedDeviceIcon(device.deviceType)}

                {/* Status indicator dot */}
                <div className={`absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-900 ${
                  device.status === DEVICE_STATUS.ONLINE
                    ? 'bg-green-500 shadow-lg shadow-green-500/50'
                    : 'bg-gray-400'
                } ${device.status === DEVICE_STATUS.ONLINE ? 'animate-pulse' : ''}`} />
              </div>

              {/* Device Info */}
              <div className="min-w-0 flex-1 space-y-1">
                <h3 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors duration-200 truncate" title={!isEmpty(device.deviceName) ? device.deviceName : device.deviceId}>
                  {!isEmpty(device.deviceName) ? device.deviceName : device.deviceId}
                </h3>
                <p className="text-sm text-muted-foreground truncate" title={device.org.orgName}>
                  {device.org.orgName}
                </p>
                <div className="flex items-center gap-2">
                  <span className={`inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm border border-white/20 ${statusColors.bg} ${statusColors.text} ${device.status === DEVICE_STATUS.ONLINE ? 'status-float' : ''}`}>
                    {getStatusIcon(device.status)}
                    <span className="hidden sm:inline">{device.status}</span>
                  </span>
                </div>
              </div>
            </div>

            {/* Control Switch */}
            <div className="flex items-center gap-2">
              <Switch
                checked={isOn}
                onCheckedChange={setIsOn}
                disabled={device.status === DEVICE_STATUS.OFFLINE}
                className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-green-500 data-[state=checked]:to-emerald-600 transition-all duration-300"
              />
            </div>
          </div>

          {/* Device Content */}
          <div className="flex-1 space-y-4">
            {isOn && (
              <div className="space-y-4 pt-3 border-t border-white/10">
                <DeviceControls />
                {renderParameters()}
              </div>
            )}

            {!isOn && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center border border-white/20 mt-3">
                <div className="space-y-2">
                  <div className="w-8 h-8 mx-auto bg-muted/50 rounded-full flex items-center justify-center">
                    <Zap className="w-4 h-4 text-muted-foreground" />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Turn on the device to view controls and parameters
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const TypeSelector: React.FC = () => {
    return (
      <div className="mb-8">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex items-center gap-2 px-4 py-2 bg-background border border-border rounded-lg hover:bg-accent hover:border-border transition-all duration-200 shadow-sm">
              <SlidersHorizontal className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium text-foreground">Filter Devices</span>
              {viewMode.selectedTypes.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {viewMode.selectedTypes.length}
                </Badge>
              )}
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 h-[200px] overflow-auto">
            <DropdownMenuLabel>Device Types</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {Object.values(DEVICE_TYPE).map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                checked={viewMode.selectedTypes.includes(type)}
                onCheckedChange={(checked) => {
                  setViewMode((prev) => ({
                    ...prev,
                    selectedTypes: checked
                      ? [...prev.selectedTypes, type]
                      : prev.selectedTypes.filter((t) => t !== type),
                  }));
                }}
              >
                {type.replace("_", " ")}
              </DropdownMenuCheckboxItem>
            ))}
            {viewMode.selectedTypes.length > 0 && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() =>
                    setViewMode((prev) => ({
                      ...prev,
                      selectedTypes: [],
                    }))
                  }
                >
                  Clear filters
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  };

  const GroupView: React.FC = () => {
    return (
      <div className="space-y-6">
        {groups.map((group) => (
          <Card key={group.id}>
            <CardHeader>
              <CardTitle>{group.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                {devices
                  .filter(
                    (device) =>
                      group.devices.some((d: any) => d.id === device.id) &&
                      (viewMode.selectedTypes.length === 0 ||
                        viewMode.selectedTypes.includes(device.deviceType))
                  )
                  .map((device) => (
                    <DeviceCard key={device.id} device={device} />
                  ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const DeviceView: React.FC = () => {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6 w-full">
        {devices
          .filter(
            (device) =>
              viewMode.selectedTypes.length === 0 ||
              viewMode.selectedTypes.includes(device.deviceType)
          )
          .map((device, index) => (
            <AnimatedContainer
              key={device.id}
              variant="fadeInUp"
              delay={index * 0.1}
            >
              <DeviceCard device={device} />
            </AnimatedContainer>
          ))}
      </div>
    );
  };

  if (loading) {
    return <LoadingPage type="dashboard" />;
  }

  return (
    <div className="w-full min-h-screen p-4 md:p-6 lg:p-8">
      {/* Modern Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          {viewMode.type === "groupManagement" && (
            <button
              className="p-2 hover:bg-accent rounded-full transition-colors"
              onClick={() => {
                setViewMode({ type: "groups", selectedTypes: [] });
                setLoading((pre) => !pre);
              }}
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
          )}
          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
            <Smartphone className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Device Management
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Monitor and control your smart IoT devices
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Search */}
          <div className="relative flex-1 sm:flex-none">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search devices..."
              className="pl-9 w-full sm:w-64 bg-background/50 backdrop-blur-sm border-border/50"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Controls Row - Compact on mobile */}
          <div className="flex items-center gap-2 justify-between sm:justify-start">
            {/* View Toggle */}
            <ViewToggle
              viewMode={displayMode}
              onViewModeChange={(mode) => setDisplayMode(mode as "table" | "grid")}
              options={VIEW_TOGGLE_PRESETS.tableGrid}
            />

            {/* Mode Selector */}
            {viewMode.type !== "groupManagement" && (
              <Select
                value={viewMode.type}
                onValueChange={(value: "devices" | "groups") =>
                  setViewMode((prev) => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger className="w-fit min-w-[120px] sm:w-[160px] bg-background/50 backdrop-blur-sm border-border/50">
                  <SelectValue placeholder="Select view">
                    <span className="hidden sm:inline">{getViewModeDisplayText(viewMode.type, false)}</span>
                    <span className="sm:hidden">{getViewModeDisplayText(viewMode.type, true)}</span>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="devices">
                    <span className="hidden sm:inline">📱 Devices View</span>
                    <span className="sm:hidden">📱 Devices</span>
                  </SelectItem>
                  <SelectItem value="groups">
                    <span className="hidden sm:inline">📂 Groups View</span>
                    <span className="sm:hidden">📂 Groups</span>
                  </SelectItem>
                  <SelectItem value="deviceManagement">
                    <span className="hidden sm:inline">⚙️ Device Management</span>
                    <span className="sm:hidden">⚙️ Manage</span>
                  </SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* Action Buttons */}
            <div className="flex items-center gap-2 bg-muted/50 backdrop-blur-sm rounded-lg p-1 border border-border/50">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="p-2 hover:bg-background hover:shadow-sm rounded-md transition-all duration-200"
                      onClick={() => setOpenExportDialog((prev) => !prev)}
                    >
                      <Download className="w-4 h-4 text-muted-foreground" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Export data</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      className="p-2 hover:bg-background hover:shadow-sm rounded-md transition-all duration-200"
                      onClick={() => openIframeDialog(IFRAME_CONFIGS.GRAFANA_DASHBOARD)}
                    >
                      <TrendingUp className="w-4 h-4 text-muted-foreground" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>View analytics</p>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <SettingsDropdown />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Settings</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Devices</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {devices.length}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Smartphone className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/30 border-green-200/50 dark:border-green-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Online Devices</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {devices.filter(d => d.status === DEVICE_STATUS.ONLINE).length}
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                <Wifi className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Device Groups</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {groups.length}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Shield className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/30 border-orange-200/50 dark:border-orange-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Active Sensors</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {devices.filter(d => d.deviceType === DEVICE_TYPE.SENSOR && d.status === DEVICE_STATUS.ONLINE).length}
                </p>
              </div>
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <Activity className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {viewMode.type === "groupManagement" ? (
        <DeviceGroupManagement />
      ) : viewMode.type === "deviceManagement" ? (
        <DeviceManagement />
      ) : (
        <>
          <TypeSelector />
          {viewMode.type === "groups" ? <GroupView /> : <DeviceView />}
        </>
      )}

      <ExportDialog
        open={openExportDialog}
        onOpenChange={() => setOpenExportDialog(!openExportDialog)}
        onExport={(dateRange) => console.log("Exporting data", dateRange)}
        title="Export Data"
      />

      <CustomPagination
        viewMode={viewMode.type}
        totalDocs={totalDocs}
        limit={limit}
        page={page}
        totalPages={totalPages}
        hasPrevPage={hasPrevPage}
        hasNextPage={hasNextPage}
        onPageChange={handlePageChange}
        onLimitChange={(limit) => setLimit(limit)}
      />

      <DeviceDialog
        open={open}
        onOpenChange={setOpen}
        device={deviceSelected as UserDevice}
      />
    </div>
  );
};

export default SmartHomeDashboard;
