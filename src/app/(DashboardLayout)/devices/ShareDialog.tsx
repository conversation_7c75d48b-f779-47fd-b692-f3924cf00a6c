import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import usersApiRequests from "@/apiRequests/admin/users";
import policiesApiRequests from "@/apiRequests/admin/policies";
import { clientUserInfo } from "@/lib/http";
import { Device } from "@/app/(DashboardLayout)/devices-manager/types";
import { Group } from "@/app/(DashboardLayout)/devices/types";
import { User } from "@/app/(DashboardLayout)/users/types";

interface ShareDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  device?: Device;
  group?: Group;
  onShare: (selectedUsers: User[]) => void;
}

type PolicyRole = "deviceShared";

interface BulkCreatePolicyProps {
  deviceName: string;
  policyRole: PolicyRole;
  attributePolicies: Record<string, any>;
  userId: string;
  deviceId: string;
}

const ShareDialog: React.FC<ShareDialogProps> = ({
  open,
  onOpenChange,
  device,
  group,
  onShare,
}) => {
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [devices, setDevices] = useState<Device[]>([]);
  const [searchQueryAvailable, setSearchQueryAvailable] = useState("");
  const [searchQuerySelected, setSearchQuerySelected] = useState("");
  const { toast } = useToast();
  const { user } = clientUserInfo.userInfo;
  const { id: userId, orgId } = user;

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await usersApiRequests.getUsers(1, 100);
        if (response?.payload?.docs) {
          console.log("response.payload.docs: ", response.payload.docs);
          setAvailableUsers(response.payload.docs);
        }
      } catch (error) {
        console.log("Error fetching users:", error);
        toast({
          title: "Error",
          description: "Failed to load users. Please try again.",
          variant: "destructive",
        });
      }
    };

    if (open) {
      fetchUsers();
      if (group) {
        setDevices(group.devices);
      } else if (device) {
        setDevices([device]);
      }
    } else {
      // Reset states when dialog closes
      setAvailableUsers([]);
      setSelectedUsers([]);
      setSearchQueryAvailable("");
      setSearchQuerySelected("");
    }
  }, [open, toast]);

  const handleBulkCreatePolicy = async (
    policies: BulkCreatePolicyProps[]
  ): Promise<void> => {
    try {
      await policiesApiRequests.bulkCreatePolicy(orgId, policies);
      toast({
        title: "Success",
        description: "Policy created successfully",
      });
    } catch (error: any) {
      console.log("Error creating policy:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to create policy. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    if (!selectedUsers.length) {
      toast({
        title: "Error",
        description: "Please select at least one user to share with",
        variant: "destructive",
      });
      return;
    }

    const policies: BulkCreatePolicyProps[] = [];

    selectedUsers.map((user) => {
      devices.map((device) => {
        policies.push({
          deviceName: device?.deviceId || group?.name || "",
          policyRole: "deviceShared" as const,
          attributePolicies: {},
          userId: user.id || "",
          deviceId: device?.id || group?.id || "",
        });
      });
    });

    // const policies = selectedUsers.map((user) => ({
    //   deviceName: device?.deviceId || group?.name || "",
    //   policyRole: "deviceShared" as const,
    //   attributePolicies: {},
    //   userId: user.id,
    //   deviceId: device?.id || group?.id || "",
    // }));

    try {
      await handleBulkCreatePolicy(policies);
      onShare(selectedUsers);
      onOpenChange(false);
    } catch (error) {
      console.log("Error sharing:", error);
      toast({
        title: "Error",
        description: "Failed to share. Please try again.",
        variant: "destructive",
      });
    }
  };

  const moveToSelected = (user: User) => {
    setAvailableUsers((prev) => prev.filter((u) => u.id !== user.id));
    setSelectedUsers((prev) => [...prev, user]);
  };

  const moveToAvailable = (user: User) => {
    setSelectedUsers((prev) => prev.filter((u) => u.id !== user.id));
    setAvailableUsers((prev) => [...prev, user]);
  };

  const filteredAvailableUsers = availableUsers.filter((user) =>
    `${user.firstName} ${user.lastName} ${user.email}`
      .toLowerCase()
      .includes(searchQueryAvailable.toLowerCase())
  );

  const filteredSelectedUsers = selectedUsers.filter((user) =>
    `${user.firstName} ${user.lastName} ${user.email}`
      .toLowerCase()
      .includes(searchQuerySelected.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Share {device ? "Device" : "Group"}:{" "}
            {device?.deviceId || group?.name}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-6 h-[500px] py-4">
          {/* Available Users */}
          <div className="flex flex-col h-full">
            <h3 className="text-sm font-semibold mb-3">Available Users</h3>
            <div className="flex items-center gap-2 mb-3">
              <Search className="h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search users..."
                value={searchQueryAvailable}
                onChange={(e) => setSearchQueryAvailable(e.target.value)}
                className="flex-1"
              />
            </div>
            <div className="border rounded-lg overflow-y-auto flex-1">
              {filteredAvailableUsers.map((user) => (
                <div
                  key={user.id}
                  className="p-3 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                  onClick={() => moveToSelected(user)}
                >
                  <div className="font-medium">
                    {user.firstName} {user.lastName}
                  </div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Selected Users */}
          <div className="flex flex-col h-full">
            <h3 className="text-sm font-semibold mb-3">Selected Users</h3>
            <div className="flex items-center gap-2 mb-3">
              <Search className="h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search selected users..."
                value={searchQuerySelected}
                onChange={(e) => setSearchQuerySelected(e.target.value)}
                className="flex-1"
              />
            </div>
            <div className="border rounded-lg overflow-y-auto flex-1">
              {filteredSelectedUsers.map((user) => (
                <div
                  key={user.id}
                  className="p-3 hover:bg-gray-100 cursor-pointer border-b last:border-b-0"
                  onClick={() => moveToAvailable(user)}
                >
                  <div className="font-medium">
                    {user.firstName} {user.lastName}
                  </div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleShare}>Share with Selected Users</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ShareDialog;
