import React from 'react';
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Maximize2, Minimize2 } from 'lucide-react';

export type IframeDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  iframeSrc: string;
  className?: string;
};

const IframeDialog = ({
  open,
  onOpenChange,
  title = "Nội dung iframe",
  iframeSrc,
  className = "",
}: IframeDialogProps) => {
  const [isFullscreen, setIsFullscreen] = React.useState(true);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={`
        ${isFullscreen
          ? 'w-screen h-screen max-w-none m-0 rounded-none p-0'
          : 'w-full max-w-4xl h-[60vh] sm:h-[70vh] p-0'
        }
        transition-all duration-300
        ${className}
      `}>
        <DialogHeader className={`
          flex flex-row items-center justify-between
          ${isFullscreen ? 'p-4' : 'p-4 sm:p-6'}
          border-b
        `}>
          <DialogTitle className="text-sm sm:text-base truncate pr-2">
            {title}
          </DialogTitle>
          <div className="flex gap-2 flex-shrink-0">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleFullscreen}
              className="h-8 w-8"
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>
        </DialogHeader>
        <div className="w-full h-full overflow-hidden">
          <iframe
            src={iframeSrc}
            className={`
              w-full border-0
              ${isFullscreen
                ? 'h-[calc(100vh-80px)]'
                : 'h-[calc(60vh-80px)] sm:h-[calc(70vh-80px)]'
              }
            `}
            title={title}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IframeDialog;