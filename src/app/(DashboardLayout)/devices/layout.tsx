import type { Metadata } from "next";

export const metadata: Metadata = {
  title: 'Devices | Smart Home Dashboard',
  description: 'Manage and monitor your IoT devices',
  openGraph: {
    title: 'Devices | Smart Home Dashboard',
    description: 'Manage and monitor your IoT devices',
  },
}

export default function DevicesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
