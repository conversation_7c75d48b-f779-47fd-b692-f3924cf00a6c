import {Device, DEVICE_TYPE} from "@/app/(DashboardLayout)/devices-manager/types";

// export const homeDevices: HomeDevices = [
//   {
//     id: "light-living",
//     name: "<PERSON><PERSON><PERSON> phòng khách",
//     type: "light",
//     category: "lighting",
//     location: "living-room",
//     icon: "light-bulb",
//     status: {
//       power: true,
//       brightness: 80,
//       color: "#FFE5B4",
//       lastUpdated: "2025-01-11T10:30:00Z"
//     },
//     controls: {
//       supportDimming: true,
//       supportColorChange: true,
//       schedules: [
//         {
//           id: "morning",
//           time: "06:00",
//           action: "on",
//           brightness: 60
//         },
//         {
//           id: "evening",
//           time: "18:00",
//           action: "on",
//           brightness: 100
//         }
//       ]
//     },
//     stats: {
//       dailyUsage: 2.5,
//       monthlyUsage: 75,
//       onTime: 8
//     }
//   },
//   {
//     id: "ac-bedroom",
//     name: "<PERSON><PERSON><PERSON><PERSON> hòa phòng ngủ",
//     type: "air-conditioner",
//     category: "climate",
//     location: "bedroom",
//     icon: "air-conditioner",
//     status: {
//       power: true,
//       temperature: 24,
//       mode: "cool",
//       fanSpeed: 2,
//       lastUpdated: "2025-01-11T10:30:00Z"
//     },
//     controls: {
//       minTemp: 16,
//       maxTemp: 30,
//       supportedModes: ["cool", "heat", "auto", "dry", "fan"],
//       schedules: [
//         {
//           id: "sleep",
//           time: "22:00",
//           action: "on",
//           temperature: 26,
//           mode: "auto"
//         }
//       ]
//     },
//     stats: {
//       dailyUsage: 5.8,
//       monthlyUsage: 174,
//       runtime: 6
//     }
//   },
//   {
//     id: "tv-living",
//     name: "TV phòng khách",
//     type: "television",
//     category: "entertainment",
//     location: "living-room",
//     icon: "tv",
//     status: {
//       power: false,
//       volume: 30,
//       channel: "Netflix",
//       lastUpdated: "2025-01-11T10:30:00Z"
//     },
//     controls: {
//       supportedApps: ["Netflix", "YouTube", "Amazon Prime"],
//       supportVoiceControl: true,
//       schedules: [] // Empty schedules array
//     },
//     stats: {
//       dailyUsage: 0.8,
//       monthlyUsage: 24,
//       watchTime: 4
//     }
//   },
//   {
//     id: "washer",
//     name: "Máy giặt",
//     type: "washer",
//     category: "appliance",
//     location: "laundry",
//     icon: "washing-machine",
//     status: {
//       power: false,
//       mode: "normal",
//       timeRemaining: 0,
//       lastUpdated: "2025-01-11T10:30:00Z"
//     },
//     controls: {
//       supportedModes: ["quick", "normal", "heavy", "delicate"],
//       schedules: [
//         {
//           id: "morning-wash",
//           time: "09:00",
//           action: "on",
//           mode: "normal"
//         }
//       ]
//     },
//     stats: {
//       dailyUsage: 1.2,
//       monthlyUsage: 36,
//       cyclesCompleted: 2
//     }
//   },
//   {
//     id: "fridge",
//     name: "Tủ lạnh",
//     type: "refrigerator",
//     category: "appliance",
//     location: "kitchen",
//     icon: "fridge",
//     status: {
//       power: true,
//       temperature: 4,
//       freezerTemp: -18,
//       doorOpen: false,
//       lastUpdated: "2025-01-11T10:30:00Z"
//     },
//     controls: {
//       tempRange: {
//         min: 2,
//         max: 8
//       },
//       freezerRange: {
//         min: -24,
//         max: -16
//       },
//       schedules: [] // Empty schedules array
//     },
//     stats: {
//       dailyUsage: 1.5,
//       monthlyUsage: 45,
//       doorOpenCount: 12
//     }
//   }
// ];

// Basic types
export type DeviceType = 'light' | 'air-conditioner' | 'television' | 'washer' | 'refrigerator';
export type DeviceCategory = 'lighting' | 'climate' | 'entertainment' | 'appliance';
export type LocationType = 'living-room' | 'bedroom' | 'kitchen' | 'laundry';
export type IconType = 'light-bulb' | 'air-conditioner' | 'tv' | 'washing-machine' | 'fridge';

// Schedule type
interface Schedule {
  id: string;
  time: string;
  action: 'on' | 'off';
  [key: string]: any; // For additional schedule-specific properties
}

// Base device status and controls
interface BaseDeviceStatus {
  power: boolean;
  lastUpdated: string;
}

interface BaseDeviceControls {
  schedules: Schedule[];
}

interface BaseDeviceStats {
  dailyUsage: number;  // kWh
  monthlyUsage: number;  // kWh
}

// Light device specific types
interface LightStatus extends BaseDeviceStatus {
  brightness: number;  // 0-100
  color: string;  // hex color
}

interface LightControls extends BaseDeviceControls {
  supportDimming: boolean;
  supportColorChange: boolean;
}

interface LightStats extends BaseDeviceStats {
  onTime: number;  // hours today
}

// Air conditioner specific types
interface AirConditionerStatus extends BaseDeviceStatus {
  temperature: number;
  mode: 'cool' | 'heat' | 'auto' | 'dry' | 'fan';
  fanSpeed: number;  // 1-5
}

interface AirConditionerControls extends BaseDeviceControls {
  minTemp: number;
  maxTemp: number;
  supportedModes: string[];
}

interface AirConditionerStats extends BaseDeviceStats {
  runtime: number;  // hours today
}

// Television specific types
interface TelevisionStatus extends BaseDeviceStatus {
  volume: number;  // 0-100
  channel: string;
}

interface TelevisionControls extends BaseDeviceControls {
  supportedApps: string[];
  supportVoiceControl: boolean;
}

interface TelevisionStats extends BaseDeviceStats {
  watchTime: number;  // hours today
}

// Washer specific types
interface WasherStatus extends BaseDeviceStatus {
  mode: string;
  timeRemaining: number;
}

interface WasherControls extends BaseDeviceControls {
  supportedModes: string[];
}

interface WasherStats extends BaseDeviceStats {
  cyclesCompleted: number;  // today
}

// Refrigerator specific types
interface RefrigeratorStatus extends BaseDeviceStatus {
  temperature: number;
  freezerTemp: number;
  doorOpen: boolean;
}

interface RefrigeratorControls extends BaseDeviceControls {
  tempRange: {
    min: number;
    max: number;
  };
  freezerRange: {
    min: number;
    max: number;
  };
}

interface RefrigeratorStats extends BaseDeviceStats {
  doorOpenCount: number;  // times today
}

// Union types for all possible status, controls and stats
type DeviceStatus = 
  | LightStatus 
  | AirConditionerStatus 
  | TelevisionStatus 
  | WasherStatus 
  | RefrigeratorStatus;

type DeviceControls = 
  | LightControls 
  | AirConditionerControls 
  | TelevisionControls 
  | WasherControls 
  | RefrigeratorControls;

type DeviceStats = 
  | LightStats 
  | AirConditionerStats 
  | TelevisionStats 
  | WasherStats 
  | RefrigeratorStats;

// Type for the full array of devices
export type HomeDevices = Device[];

// Helper function to create a base device controls object
export const createBaseControls = (schedules: Schedule[] = []): BaseDeviceControls => ({
  schedules
});

// Helper functions to create device-specific controls
export const createRefrigeratorControls = (
  tempRange: { min: number; max: number },
  freezerRange: { min: number; max: number },
  schedules: Schedule[] = []
): RefrigeratorControls => ({
  ...createBaseControls(schedules),
  tempRange,
  freezerRange
});

// Type guard functions
// export const isLightDevice = (device: Device): device is Device & { status: LightStatus; controls: LightControls; stats: LightStats } => {
//   return device.deviceType === DEVICE_TYPE.RGB_LIGHT;
// };

// export const isAirConditioner = (device: Device): device is Device & { status: AirConditionerStatus; controls: AirConditionerControls; stats: AirConditionerStats } => {
//   return device.deviceType === 'air-conditioner';
// };
//
// export const isTelevision = (device: Device): device is Device & { status: TelevisionStatus; controls: TelevisionControls; stats: TelevisionStats } => {
//   return device.deviceType === 'television';
// };
//
// export const isWasher = (device: Device): device is Device & { status: WasherStatus; controls: WasherControls; stats: WasherStats } => {
//   return device.deviceType === 'washer';
// };
//
// export const isRefrigerator = (device: Device): device is Device & { status: RefrigeratorStatus; controls: RefrigeratorControls; stats: RefrigeratorStats } => {
//   return device.deviceType === 'refrigerator';
// };