import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Group } from './types';

interface DeleteGroupConfirmationProps {
  group: Group;
  open: boolean;
  onClose: () => void;
  onConfirmDelete: (group: Group) => Promise<void>;
}

const DeleteGroupConfirmation = ({
  group,
  open,
  onClose,
  onConfirmDelete
}: DeleteGroupConfirmationProps) => {
  const [inputName, setInputName] = useState('');
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (inputName !== group.name) {
      setError('Tên nhóm không chính xác. Vui lòng kiểm tra lại.');
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirmDelete(group);
      onClose();
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa nhóm. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setInputName('');
    setError('');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Xác nhận xóa nhóm</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <p className="mb-2">
              Để xóa nhóm "<span className="font-medium">{group?.name}</span>", 
              vui lòng nhập tên nhóm sau để xác nhận:
            </p>
            <p className="font-mono bg-gray-100 p-2 rounded select-all">
              {group?.name}
            </p>
            {group?.devices && group.devices.length > 0 && (
              <p className="mt-2 text-amber-600">
                Lưu ý: Nhóm này đang có {group.devices.length} thiết bị. 
                Xóa nhóm sẽ hủy gán tất cả thiết bị khỏi nhóm.
              </p>
            )}
          </div>

          <Input
            value={inputName}
            onChange={(e) => {
              setInputName(e.target.value);
              setError('');
            }}
            placeholder="Nhập tên nhóm để xác nhận"
            disabled={isDeleting}
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              type="submit"
              variant="destructive"
              disabled={!inputName || isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : 'Xác nhận xóa'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteGroupConfirmation;