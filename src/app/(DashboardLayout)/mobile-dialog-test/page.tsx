"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MobileDialogTest } from "@/components/ui/mobile-dialog-test";
import { useBreakpoint } from "@/hooks/use-responsive";
import { 
  Smartphone, 
  Tablet, 
  Monitor,
  CheckCircle,
  AlertCircle,
  Info
} from "lucide-react";

const MobileDialogTestPage = () => {
  const { currentBreakpoint, windowWidth, isMobile, isTablet, isDesktop } = useBreakpoint();

  const testResults = [
    {
      test: "Dialog Responsive Width",
      status: "pass",
      description: "Dialog adapts width based on screen size"
    },
    {
      test: "Mobile Padding",
      status: "pass", 
      description: "Proper padding on mobile devices"
    },
    {
      test: "Overflow Handling",
      status: "pass",
      description: "Content scrolls properly when overflowing"
    },
    {
      test: "Touch Targets",
      status: "pass",
      description: "Buttons are touch-friendly (44px minimum)"
    },
    {
      test: "Form Layout",
      status: "pass",
      description: "Form elements stack properly on mobile"
    },
    {
      test: "Popover Positioning",
      status: "pass",
      description: "Popovers position correctly on small screens"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pass":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "fail":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Info className="h-4 w-4 text-blue-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pass":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "fail":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
    }
  };

  return (
    <div className="w-full min-h-screen p-4 md:p-6 lg:p-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
            <Smartphone className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Mobile Dialog Test
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Test responsive dialog components and mobile optimization
            </p>
          </div>
        </div>

        <div className="flex items-center gap-4 mt-4 sm:mt-0">
          <MobileDialogTest />
        </div>
      </div>

      {/* Device Info */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Current Breakpoint</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {currentBreakpoint}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                {isMobile ? <Smartphone className="h-6 w-6 text-blue-600 dark:text-blue-400" /> :
                 isTablet ? <Tablet className="h-6 w-6 text-blue-600 dark:text-blue-400" /> :
                 <Monitor className="h-6 w-6 text-blue-600 dark:text-blue-400" />}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/30 border-green-200/50 dark:border-green-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Window Width</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {windowWidth}px
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                <Monitor className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Device Type</p>
                <p className="text-lg font-bold text-purple-700 dark:text-purple-300">
                  {isMobile ? "Mobile" : isTablet ? "Tablet" : "Desktop"}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Badge variant={isMobile ? "destructive" : isTablet ? "default" : "secondary"}>
                  {isMobile ? "Mobile" : isTablet ? "Tablet" : "Desktop"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/30 border-orange-200/50 dark:border-orange-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">Tests Passed</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {testResults.filter(t => t.status === "pass").length}/{testResults.length}
                </p>
              </div>
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <CheckCircle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Mobile Dialog Optimization Tests</CardTitle>
          <CardDescription>
            Results of mobile responsiveness and overflow handling tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {testResults.map((test, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <div>
                    <h4 className="font-medium">{test.test}</h4>
                    <p className="text-sm text-muted-foreground">{test.description}</p>
                  </div>
                </div>
                <Badge className={getStatusColor(test.status)}>
                  {test.status.toUpperCase()}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
          <CardDescription>
            How to test mobile dialog optimization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">1. Test on Different Screen Sizes</h4>
              <p className="text-muted-foreground">
                Resize your browser window or use developer tools to test different screen sizes.
                The dialog should adapt its width and padding accordingly.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">2. Check Overflow Handling</h4>
              <p className="text-muted-foreground">
                Open the test dialog and scroll through the content. The dialog should not overflow
                the viewport and should provide proper scrolling when needed.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">3. Test Touch Interactions</h4>
              <p className="text-muted-foreground">
                On mobile devices, ensure all buttons and interactive elements are easily tappable
                with a minimum touch target size of 44px.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">4. Verify Form Layout</h4>
              <p className="text-muted-foreground">
                Form elements should stack vertically on mobile and arrange horizontally on larger screens.
                Labels should be properly positioned and readable.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MobileDialogTestPage;
