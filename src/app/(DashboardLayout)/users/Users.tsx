"use client";

import React, { useEffect, useState, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Edit,
  Lock,
  MoreVertical,
  Trash,
  UserPlus,
  Users,
  UserCheck,
  Shield,
  Calendar,
} from "lucide-react";

import {
  StatusEnum,
  User,
  UserCardProps,
  UserModalProps,
  UserModelMode,
} from "./types";
import { format } from "date-fns";
import usersApiRequests from "@/apiRequests/admin/users";
import rolesApiRequests from "@/apiRequests/admin/roles";
import { Role } from "@/app/(DashboardLayout)/settings/roles/types";
import UserRoleManager from "@/components/UserRoleManager";
import { get, isEmpty, isNil, omitBy } from "lodash";
import { clientUserInfo } from "@/lib/http";
import CustomPagination from "@/app/components/Pagination";
import {Organization} from "@/app/(DashboardLayout)/organizations/types";
import { ViewToggle, VIEW_TOGGLE_PRESETS } from "@/app/components/view-toggle";
import { useUsersViewMode } from "@/hooks/useViewMode";
import UserDeleteConfirm from "./UserDeleteConfirm";
import { useToast } from "@/hooks/use-toast";
import SearchInput from "@/app/(DashboardLayout)/policy/components/SearchInput";
import { useOrganization } from "@/context/organization-context";

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);

  // Search state - isolated with debouncing
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>("");
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  const [editUserIndex, setEditUserIndex] = useState<number>(-1);
  const { viewMode, setViewMode } = useUsersViewMode();
  const [userModelMode, setUserModelMode] = useState<UserModelMode>(
    UserModelMode.close
  );
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDocs, setTotalDocs] = useState(0);
  const [hastPrevPage, setHasPrevPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingUser, setDeletingUser] = useState<User | null>(null);
  const { toast } = useToast();
  const { user } = clientUserInfo.userInfo;
  const { orgId } = user;

  // Debounce search query with loading state
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set searching state if query is different from debounced
    if (searchQuery !== debouncedSearchQuery) {
      setIsSearching(true);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setIsSearching(false);
    }, 300);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [searchQuery, debouncedSearchQuery]);

  const fetchUsers = async (searchKeyword?: string) => {
    try {
      let result: { status: number; payload: any };

      if (searchKeyword && searchKeyword.trim()) {
        // Use search API when there's a search keyword
        result = await usersApiRequests.searchUsers(searchKeyword.trim(), limit);
      } else {
        // Use regular getUsers API when no search
        result = await usersApiRequests.getUsers(page, limit);
      }

      if (result.status === 200 && result.payload) {
        const payload = result.payload;
        const users = get(payload, "docs", []);
        const totalDocs = get(payload, "totalDocs", 0);
        const totalPages = get(payload, "totalPages", 0);
        const hasPrevPage = get(payload, "hasPrevPage", false);
        const hasNextPage = get(payload, "hasNextPage", false);
        setTotalDocs(totalDocs);
        setTotalPages(totalPages);
        setHasPrevPage(hasPrevPage);
        setHasNextPage(hasNextPage);
        setUsers(users);
      }
    } catch (error) {
      console.log("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setLoading(true);
    void fetchUsers(debouncedSearchQuery);
  }, [limit, page, debouncedSearchQuery]);

  // Memoized search change handler - NO dependencies to prevent re-render
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    // Always reset to page 1 when searching
    setPage(1);
  }, []);

  const handlePageChange = (page: number) => {
    setPage(page);
  };

  const handleUser = (user: User | {}) => {
    const apiHandler = async () => {
      try {
        // Create user payload without role information
        // Roles will be managed separately through UserRoleManager
        let payload: User = {
          ...omitBy(user, isNil),
          orgId,
        };

        // Remove role-related fields from payload as they'll be managed via mappings
        delete payload.roles;
        delete payload.roleIds;

        const userId = get(user, "id");
        const userOrgId = get(user, "org.id");
        const currentOrgId = userOrgId ? userOrgId : orgId;

        if (userId) {
          await usersApiRequests.updateUser(currentOrgId, userId, payload);
        } else {
          await usersApiRequests.createUser(currentOrgId, payload);
        }

        void fetchUsers(debouncedSearchQuery);
      } catch (error) {
        console.log("Error saving user:", error);
      } finally {
        setLoading(false);
        setUserModelMode(UserModelMode.close);
      }
    };
    setLoading(true);
    void apiHandler();
  };

  const handleDeleteClick = (user: User) => {
    setDeletingUser(user);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async (userId: string) => {
    try {
      await usersApiRequests.deleteUser(userId);
      setUsers(users.filter((u) => u.id !== userId));
      setIsDeleteDialogOpen(false);
      setDeletingUser(null);
      toast({
        title: "Success",
        description: "User deleted successfully",
      });
    } catch (error) {
      console.log("Error deleting user:", error);
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive",
      });
      throw error; // Re-throw để UserDeleteConfirm có thể handle
    }
  };

  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setDeletingUser(null);
  };

  const UserCard: React.FC<UserCardProps> = ({ user, index }) => (
    <Card className="w-full group hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 bg-gradient-to-br from-background to-muted/20 border-border/50">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 min-w-0 flex-1">
            <div className="relative flex-shrink-0">
              <Avatar className="h-12 w-12 ring-2 ring-blue-200/50 dark:ring-blue-800/50 group-hover:ring-blue-400/70 transition-all duration-200">
                <AvatarImage
                  src={user.photo?.path}
                  alt={`${user.firstName} ${user.lastName}`}
                />
                <AvatarFallback className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-semibold">
                  {get(user, "firstName[0]", "")}
                  {get(user, "lastName[0]", "")}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-2 border-background flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate">{`${user.firstName} ${user.lastName}`}</h3>
              <p className="text-sm text-muted-foreground truncate" title={user.email}>{user.email}</p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-blue-100 dark:hover:bg-blue-900/30">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                className="flex items-center gap-2"
                onClick={() => {
                  setEditUserIndex(index);
                  setUserModelMode(UserModelMode.edit);
                }}
              >
                <Edit className="h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem className="flex items-center gap-2">
                <Lock className="h-4 w-4" />
                Permissions
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-2 text-destructive"
                onClick={() => handleDeleteClick(user)}
              >
                <Trash className="h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="mt-4 flex flex-wrap gap-2">
          {get(user, "roles", []).map((role) => (
            <Badge key={role.id} className="bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 border-purple-200/50 dark:border-purple-800/50">
              {role.name}
            </Badge>
          ))}
          <Badge
            className={
              user.status === StatusEnum.ACTIVE
                ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 border-green-200/50 dark:border-green-800/50"
                : "bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400 border-gray-200/50 dark:border-gray-800/50"
            }
          >
            {user.status}
          </Badge>
        </div>
        <div className="mt-4 pt-3 border-t border-border/30 text-sm text-muted-foreground">
          Created: {format(new Date(get(user, "createdAt", Date.now())), "MMM dd, yyyy")}
        </div>
      </CardContent>
    </Card>
  );

  const UserModal: React.FC<UserModalProps> = ({
    mode,
    userData,
    onClose,
  }) => {
    const [roles, setRoles] = useState<Role[]>([]);
    const [user, setUser] = useState<User | {}>(userData);
    const { availableOrganizations } = useOrganization(); // Use organization context instead
    const organizations = availableOrganizations; // Use organizations directly - no mapping needed
    const currentOrg = organizations.find(org => org.id === orgId);
    console.log(currentOrg?.orgName);
    useEffect(() => {
      const fetchRoles = async () => {
        try {
          // Using organization context - no need to pass orgId
          const result = await rolesApiRequests.getRoles(1, 10);
          if (result.status === 200 && result.payload) {
            setRoles(get(result, "payload.docs", []));
          }
        } catch (error) {
          console.log("Error fetching roles:", error);
        } finally {
          setLoading(false); // Move setLoading here since we removed fetchOrganizations
        }
      };

      void fetchRoles();
    }, []); // Removed fetchOrganizations call - using context instead

    const onSave = () => {
      if (isEmpty(user)) {
        console.log("User data is empty");
        return;
      }

      handleUser(user as User);
    };

    return (
      <Dialog open={mode !== UserModelMode.close} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {mode === UserModelMode.create ? "Create New User" : "Edit User"}
            </DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={get(user, "firstName", "")}
                onChange={(e) =>
                  setUser({ ...user, firstName: e.target.value })
                }
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={get(user, "lastName", "")}
                onChange={(e) => setUser({ ...user, lastName: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={get(user, "email", "")}
                readOnly={mode === UserModelMode.edit}
                onChange={(e) => setUser({ ...user, email: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <input
                type="password"
                value={get(user, "password", "")}
                onChange={(e) => {
                  setUser({ ...user, password: e.target.value });
                }}
                className="w-full px-4 py-3 rounded-lg bg-slate-800/50 border border-slate-700 text-white
                                     focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300
                                     placeholder-slate-500 hover:bg-slate-800/70 hover:border-indigo-400"
                placeholder="Enter password"
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2 w-2 h-2 bg-indigo-500 rounded-full animate-pulse delay-200" />
            </div>
            {/* Role Management - Only show for existing users */}
            {get(user, "id") && (
              <div className="grid gap-2">
                <Label>Vai trò</Label>
                <UserRoleManager
                  user={user as User}
                  showTitle={false}
                  onUserRolesChanged={(userRoleMappings) => {
                    // Update user state if needed
                    console.log('User roles changed:', userRoleMappings);
                  }}
                />
              </div>
            )}

            {/* Note for new users */}
            {!get(user, "id") && (
              <div className="grid gap-2">
                <Label>Vai trò</Label>
                <div className="p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Vai trò có thể được gán sau khi tạo người dùng.
                  </p>
                </div>
              </div>
            )}
            <div className="grid gap-2">
              <Label htmlFor="status">Organization</Label>
              <Select
                value={get(user, 'org.orgName', currentOrg?.orgName)}
                onValueChange={(value) => {
                  const selectedOrg = organizations.find(
                    (org) => org.orgName === value
                  );
                  setUser({ ...user, org: selectedOrg });
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Organization" />
                </SelectTrigger>
                <SelectContent>
                  {organizations.map((organization) => (
                    <SelectItem key={organization.id} value={organization.orgName || organization.id}>
                      {organization.orgName || organization.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={onSave}>
              {mode === "create" ? "Create" : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const GridView = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-24">
        {users.map((user, index) => (
          <UserCard key={user.id} user={user} index={index} />
        ))}
      </div>
    );
  };

  const ListView = () => {
    return (
      <div className="overflow-x-auto pb-24">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User Info</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user, index) => (
              <TableRow key={user.id} className="hover:bg-muted/30 transition-colors">
                <TableCell>
                  <div className="flex items-center gap-3 min-w-0">
                    <div className="relative flex-shrink-0">
                      <Avatar className="ring-2 ring-blue-200/50 dark:ring-blue-800/50">
                        <AvatarImage
                          src={user.photo?.path}
                          alt={`${user.firstName} ${user.lastName}`}
                        />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 text-blue-600 dark:text-blue-400 font-semibold">
                          {get(user, "firstName[0]", "")}
                          {get(user, "lastName[0]", "")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-gradient-to-br from-green-400 to-green-600 rounded-full border border-background"></div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="font-medium truncate">{`${user.firstName} ${user.lastName}`}</div>
                      <div className="text-sm text-muted-foreground truncate" title={user.email}>{user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {get(user, "roles", []).map((role) => (
                      <Badge key={role.id} className="bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 border-purple-200/50 dark:border-purple-800/50">
                        {role.name}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    className={
                      user.status === StatusEnum.ACTIVE
                        ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 border-green-200/50 dark:border-green-800/50"
                        : "bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400 border-gray-200/50 dark:border-gray-800/50"
                    }
                  >
                    {user.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {format(new Date(get(user, "createdAt", Date.now())), "MMM dd, yyyy")}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/30">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="flex items-center gap-2"
                        onClick={() => {
                          setEditUserIndex(index);
                          setUserModelMode(UserModelMode.edit);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        Permissions
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="flex items-center gap-2 text-destructive"
                        onClick={() => handleDeleteClick(user)}
                      >
                        <Trash className="h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  return (
    <div className="p-6">
      {/* Modern Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-200/20 dark:border-blue-800/20">
            <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              User Management
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mt-1">
              Manage your organization users and their roles
            </p>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Search */}
          <div className="flex-1 sm:flex-none">
            <SearchInput
              value={searchQuery}
              onValueChange={handleSearchChange}
              placeholder="Search users..."
              className="w-full sm:w-64"
              isLoading={isSearching}
            />
          </div>

          {/* Controls Row - Compact on mobile */}
          <div className="flex items-center gap-2 justify-between sm:justify-start">
            {/* View Toggle */}
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={(mode) => setViewMode(mode as "list" | "grid")}
              options={VIEW_TOGGLE_PRESETS.standard}
            />

            {/* Add User Button */}
            <Button
              onClick={() => setUserModelMode(UserModelMode.create)}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add User</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 border-blue-200/50 dark:border-blue-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Users</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                  {totalDocs}
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/30 border-green-200/50 dark:border-green-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Active Users</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                  {users.filter(user => user.status === StatusEnum.ACTIVE).length}
                </p>
              </div>
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                <UserCheck className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/30 border-purple-200/50 dark:border-purple-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Admin Users</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                  {users.filter(user =>
                    get(user, "roles", []).some(role => role.name.toLowerCase().includes('admin'))
                  ).length}
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <Shield className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/30 border-orange-200/50 dark:border-orange-800/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">New This Month</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                  {users.filter(user => {
                    const createdDate = new Date(get(user, "createdAt", Date.now()));
                    const now = new Date();
                    return createdDate.getMonth() === now.getMonth() && createdDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
                <Calendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card className="bg-background/50 backdrop-blur-sm border-border/50 shadow-xl">
        <CardContent className="p-0 relative">
          {loading && (
            <div className="absolute inset-0 bg-background/60 backdrop-blur-sm z-10 flex items-center justify-center">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                Updating users...
              </div>
            </div>
          )}
          <div className="p-6">
            {viewMode === "list" ? <ListView /> : <GridView />}
          </div>
        </CardContent>
      </Card>

      {/* Create/Edit Modals */}
      <UserModal
        // isOpen={userModelMode !== UserModelMode.close}
        mode={userModelMode}
        onClose={() => {
          setEditUserIndex(-1);
          setUserModelMode(UserModelMode.close);
        }}
        userData={editUserIndex > -1 ? users[editUserIndex] : {}}
        // onChange={setNewUser}
        onSave={() =>
          handleUser(editUserIndex > -1 ? users[editUserIndex] : {})
        }
      />
      {/*<UserModal*/}
      {/*  isOpen={isEditModalOpen}*/}
      {/*  onClose={() => setIsEditModalOpen(false)}*/}
      {/*  mode="edit"*/}
      {/*  userData={editingUser}*/}
      {/*  onChange={(data) => setEditingUser(data as EditingUser)}*/}
      {/*  onSave={handleEditUser}*/}
      {/*/>*/}

      {/* Pagination */}
      <CustomPagination
        viewMode={viewMode}
        totalDocs={totalDocs}
        limit={limit}
        page={page}
        totalPages={totalPages}
        hasPrevPage={hastPrevPage}
        hasNextPage={hasNextPage}
        onPageChange={handlePageChange}
        onLimitChange={(limit) => setLimit(limit)}
      />

      {/* Delete Confirmation Dialog */}
      <UserDeleteConfirm
        user={deletingUser}
        open={isDeleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        onConfirmDelete={handleConfirmDelete}
      />
    </div>
  );
};

export default UserManagement;
