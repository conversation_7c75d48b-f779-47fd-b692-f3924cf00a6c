import UserManagement from "./Users";
import { Metadata } from "next";
import PermissionGuard from "@/components/PermissionGuard";

export const metadata: Metadata = {
  title: 'Users',
  openGraph: {
    title: 'Users',
  },
}

function ProtectedUserManagement() {
  return (
    <PermissionGuard
      // requiredPermissions={[
      //   { resource: 'user', action: 'get' }
      // ]}
      // fallbackToLegacy={true}
      showAccessDenied={true}
    >
      <UserManagement />
    </PermissionGuard>
  );
}

export default ProtectedUserManagement;