import {Organization} from "@/app/(DashboardLayout)/organizations/types";
import {Role} from "@/app/(DashboardLayout)/settings/roles/types";

export interface Photo {
  id: string;
  path: string;
}

export enum StatusEnum {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export interface User {
  id?: string;
  orgId?: string;
  org?: Organization
  email?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  roleIds?: string[];
  roles?: Role[];
  provider?: string;
  socialId?: string;
  photo?: Photo;
  status?: StatusEnum;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export enum UserModelMode {
  create = "create",
  edit = "edit",
  close = "close",
}

export interface UserModalProps {
  mode: UserModelMode;
  userData: User | {};
  onClose: () => void;
  onSave: () => void;
  // onChange: (data: User) => void;
}

export interface UserCardProps {
  index: number;
  user: User;
}

export type payload = {
  data: User[];
  hasNextPage: boolean;
};
