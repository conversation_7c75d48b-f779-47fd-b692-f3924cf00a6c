"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { User } from "./types";

interface UserDeleteConfirmProps {
  user: User | null;
  open: boolean;
  onClose: () => void;
  onConfirmDelete: (userId: string) => Promise<void>;
}

const UserDeleteConfirm = ({
  user,
  open,
  onClose,
  onConfirmDelete
}: UserDeleteConfirmProps) => {
  const [inputEmail, setInputEmail] = useState('');
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !user.id) return;
    
    if (inputEmail !== user.email) {
      setError('Email không chính xác. Vui lòng kiểm tra lại.');
      return;
    }

    setIsDeleting(true);
    try {
      await onConfirmDelete(user.id);
      onClose();
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa user. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setInputEmail('');
    setError('');
    onClose();
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Xác nhận xóa user</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <p className="mb-2">
              Để xóa user "<span className="font-medium">{user.firstName} {user.lastName}</span>", 
              vui lòng nhập email sau để xác nhận:
            </p>
            <p className="font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded select-all">
              {user.email}
            </p>
          </div>

          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/30 rounded-lg p-3 border border-blue-200/30 dark:border-blue-800/30">
            <div className="text-sm">
              <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">
                User Details:
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Name: <span className="font-medium">{user.firstName} {user.lastName}</span>
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Organization: <span className="font-medium">{user.org?.orgName || 'N/A'}</span>
              </p>
              <p className="text-blue-600 dark:text-blue-400">
                Status: <span className="font-medium">{user.status}</span>
              </p>
              {user.roles && user.roles.length > 0 && (
                <p className="text-blue-600 dark:text-blue-400">
                  Roles: <span className="font-medium">{user.roles.map(role => role.name).join(', ')}</span>
                </p>
              )}
              {user.roles && user.roles.length > 0 && (
                <p className="text-amber-600 dark:text-amber-400 mt-2">
                  ⚠️ Lưu ý: User này có {user.roles.length} role(s). Xóa user sẽ hủy tất cả quyền truy cập.
                </p>
              )}
            </div>
          </div>

          <Input
            value={inputEmail}
            onChange={(e) => {
              setInputEmail(e.target.value);
              setError('');
            }}
            placeholder="Nhập email để xác nhận"
            disabled={isDeleting}
          />

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Hủy
            </Button>
            <Button 
              type="submit"
              variant="destructive"
              disabled={!inputEmail || isDeleting}
            >
              {isDeleting ? 'Đang xóa...' : 'Xác nhận xóa'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default UserDeleteConfirm;
