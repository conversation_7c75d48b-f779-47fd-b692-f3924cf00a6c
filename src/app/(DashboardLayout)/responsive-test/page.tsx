"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  ResponsiveContainer, 
  ResponsiveHeader, 
  ResponsiveGrid, 
  ResponsiveStack 
} from "@/components/ui/responsive-container";
import { useBreakpoint } from "@/hooks/use-responsive";
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Search, 
  Plus,
  Settings,
  Users,
  LayoutGrid,
  LayoutList
} from "lucide-react";

const ResponsiveTestPage = () => {
  const { currentBreakpoint, windowWidth, isMobile, isTablet, isDesktop } = useBreakpoint();

  return (
    <ResponsiveContainer variant="page" padding="md">
      <ResponsiveHeader
        title="Responsive Test Page"
        description="Test responsive design across different screen sizes"
      >
        <ResponsiveStack direction="responsive" gap="md" align="center">
          <div className="flex bg-muted rounded-lg p-1">
            <Button variant="secondary" size="sm" className="h-8">
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-8">
              <LayoutList className="h-4 w-4" />
            </Button>
          </div>
          <Button className="btn-responsive">
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Add Item</span>
            <span className="sm:hidden">Add</span>
          </Button>
        </ResponsiveStack>
      </ResponsiveHeader>

      {/* Breakpoint Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isMobile && <Smartphone className="h-5 w-5" />}
            {isTablet && <Tablet className="h-5 w-5" />}
            {isDesktop && <Monitor className="h-5 w-5" />}
            Current Breakpoint Info
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Current Breakpoint</p>
              <Badge variant="secondary">{currentBreakpoint}</Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Window Width</p>
              <Badge variant="outline">{windowWidth}px</Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Device Type</p>
              <Badge variant={isMobile ? "destructive" : isTablet ? "default" : "secondary"}>
                {isMobile ? "Mobile" : isTablet ? "Tablet" : "Desktop"}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Responsive Classes</p>
              <div className="flex flex-wrap gap-1 mt-1">
                <span className="mobile-only text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Mobile Only</span>
                <span className="tablet-up text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Tablet+</span>
                <span className="desktop-only text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Desktop Only</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Controls */}
      <div className="flex-responsive-center mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search items..."
            className="pl-9 w-full"
          />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Settings</span>
          </Button>
          <Button variant="outline" size="sm">
            <Users className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Users</span>
          </Button>
        </div>
      </div>

      {/* Responsive Grid */}
      <ResponsiveGrid 
        columns={{ default: 1, sm: 2, lg: 3, xl: 4 }}
        gap="md"
        className="mb-6"
      >
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="text-lg">Card {index + 1}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                This is a responsive card that adapts to different screen sizes.
              </p>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="flex-1 sm:flex-none">
                  Action
                </Button>
                <Button size="sm" className="flex-1 sm:flex-none">
                  Primary
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </ResponsiveGrid>

      {/* Responsive Stack Examples */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Responsive Stack - Vertical to Horizontal</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveStack direction="responsive" gap="md" align="center">
              <div className="bg-primary/10 p-4 rounded-lg flex-1">
                <p className="text-sm">Item 1</p>
              </div>
              <div className="bg-secondary/10 p-4 rounded-lg flex-1">
                <p className="text-sm">Item 2</p>
              </div>
              <div className="bg-accent/10 p-4 rounded-lg flex-1">
                <p className="text-sm">Item 3</p>
              </div>
            </ResponsiveStack>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Responsive Text and Spacing</CardTitle>
          </CardHeader>
          <CardContent className="spacing-responsive">
            <h2 className="text-responsive-lg mb-4">Responsive Heading</h2>
            <p className="text-responsive mb-4">
              This text scales responsively across different screen sizes using utility classes.
            </p>
            <div className="gap-responsive flex flex-wrap">
              <Badge>Tag 1</Badge>
              <Badge>Tag 2</Badge>
              <Badge>Tag 3</Badge>
              <Badge>Tag 4</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </ResponsiveContainer>
  );
};

export default ResponsiveTestPage;
