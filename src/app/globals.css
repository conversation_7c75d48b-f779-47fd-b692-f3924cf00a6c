@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
   LAYOUT COMPONENTS
   ========================================================================== */

@layer components {
  /* Layout */
  .layout {
    @apply flex w-screen h-screen;
  }

  /* Sidebar */
  .sidebar {
    @apply w-64 transition-all duration-300;
  }

  .sidebar ul {
    @apply list-none p-0;
  }

  .link {
    @apply my-2 p-2 pl-4 w-full flex flex-row justify-start items-center
           rounded-r-3xl cursor-pointer transition-all duration-200
           hover:bg-accent/50;
  }

  .sidebar a {
    @apply no-underline transition-colors duration-200;
  }

  .sidebar a:hover {
    @apply text-primary;
  }

  .content {
    @apply flex-1 pl-4 overflow-y-auto;
  }

  .active {
    @apply text-primary-foreground bg-gradient-to-r from-primary/80 to-primary;
  }

  .dot {
    @apply w-fit h-fit text-foreground rounded-full p-1.5 border mr-4
           transition-all duration-200;
  }

  /* ==========================================================================
     RESPONSIVE UTILITIES
     ========================================================================== */

  /* Mobile-first responsive text */
  .text-responsive {
    @apply text-sm sm:text-base md:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
  }

  /* Responsive spacing */
  .spacing-responsive {
    @apply p-2 sm:p-4 md:p-6;
  }

  .spacing-responsive-lg {
    @apply p-4 sm:p-6 md:p-8 lg:p-12;
  }

  .gap-responsive {
    @apply gap-2 sm:gap-4 md:gap-6;
  }

  .gap-responsive-lg {
    @apply gap-4 sm:gap-6 md:gap-8;
  }

  /* Grid responsive */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .grid-responsive-dense {
    @apply grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3;
  }

  /* Flex responsive */
  .flex-responsive {
    @apply flex flex-col sm:flex-row gap-4;
  }

  .flex-responsive-center {
    @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  }

  /* Container responsive */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Button responsive */
  .btn-responsive {
    @apply w-full sm:w-auto;
  }

  /* Table responsive wrapper */
  .table-responsive {
    @apply overflow-x-auto -mx-4 sm:mx-0;
  }

  /* Hide/show on different screen sizes */
  .mobile-only {
    @apply block sm:hidden;
  }

  .tablet-up {
    @apply hidden sm:block;
  }

  .desktop-only {
    @apply hidden lg:block;
  }

  /* ==========================================================================
     ANIMATION UTILITIES
     ========================================================================== */

  .animate-fade-in {
    @apply animate-in fade-in duration-300;
  }

  .animate-slide-up {
    @apply animate-in slide-in-from-bottom-4 duration-300;
  }

  .animate-scale-in {
    @apply animate-in zoom-in-95 duration-200;
  }

  /* Hover animations */
  .hover-lift {
    @apply transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  /* ==========================================================================
     BACKGROUND PATTERNS
     ========================================================================== */

  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .bg-dot-pattern {
    background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 15px 15px;
  }

  .bg-circuit-pattern {
    background-image:
      linear-gradient(90deg, transparent 24%, rgba(255, 255, 255, 0.05) 25%, rgba(255, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(255, 255, 255, 0.05) 75%, rgba(255, 255, 255, 0.05) 76%, transparent 77%, transparent),
      linear-gradient(transparent 24%, rgba(255, 255, 255, 0.05) 25%, rgba(255, 255, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(255, 255, 255, 0.05) 75%, rgba(255, 255, 255, 0.05) 76%, transparent 77%, transparent);
    background-size: 50px 50px;
  }

  /* ==========================================================================
     GLASSMORPHISM EFFECTS
     ========================================================================== */

  .glass-card {
    @apply bg-white/10 dark:bg-slate-900/10 backdrop-blur-md border border-white/20 dark:border-slate-700/20;
  }

  .glass-sidebar {
    @apply bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-r border-white/20 dark:border-slate-700/20;
  }

  /* ==========================================================================
     SIDEBAR OVERLAY
     ========================================================================== */

  .sidebar-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden opacity-0 pointer-events-none transition-opacity duration-300;
  }

  .sidebar-overlay[data-state="open"] {
    @apply opacity-100 pointer-events-auto;
  }

  /* Enhanced sidebar animations */
  .sidebar-enter {
    @apply animate-in slide-in-from-left-full duration-300;
  }

  .sidebar-exit {
    @apply animate-out slide-out-to-left-full duration-300;
  }

  /* ==========================================================================
     DEVICE STATUS INDICATORS
     ========================================================================== */

  .status-online {
    @apply bg-emerald-500 shadow-lg shadow-emerald-500/30;
  }

  .status-offline {
    @apply bg-slate-500 shadow-lg shadow-slate-500/30;
  }

  .status-warning {
    @apply bg-amber-500 shadow-lg shadow-amber-500/30;
  }

  .status-error {
    @apply bg-red-500 shadow-lg shadow-red-500/30;
  }

  /* ==========================================================================
     MODERN SCROLLBARS
     ========================================================================== */

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/40;
  }

  /* ==========================================================================
     LOADING STATES
     ========================================================================== */

  .loading-shimmer {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted;
  }

  .loading-skeleton {
    @apply bg-muted rounded animate-pulse;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --warning: 48 96% 89%;
    --warning-foreground: 38 92% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* ==========================================================================
   MOBILE DIALOG OPTIMIZATIONS
   ========================================================================== */

/* Prevent body scroll when dialog is open on mobile */
@media (max-width: 768px) {
  .dialog-open {
    overflow: hidden;
  }
}

/* Mobile-specific dialog styles */
@media (max-width: 640px) {
  /* Dialog content responsive adjustments */
  [data-radix-dialog-content] {
    margin: 1rem !important;
    max-height: calc(100vh - 2rem) !important;
    width: calc(100vw - 2rem) !important;
    max-width: none !important;
  }

  /* Ensure proper spacing for mobile dialogs */
  .dialog-content-mobile {
    padding: 1rem;
    gap: 1rem;
  }

  /* Mobile-friendly form layouts */
  .dialog-form-mobile {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .dialog-form-mobile .form-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .dialog-form-mobile .form-row label {
    text-align: left;
    font-size: 0.875rem;
    font-weight: 500;
  }

  /* Mobile button layouts */
  .dialog-footer-mobile {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .dialog-footer-mobile button {
    width: 100%;
    justify-content: center;
  }

  /* Mobile popover adjustments */
  [data-radix-popover-content] {
    max-width: calc(100vw - 2rem);
    margin: 0 1rem;
  }

  /* Mobile command list height limits */
  .command-list-mobile {
    max-height: 200px;
    overflow-y: auto;
  }

  /* Mobile badge container */
  .badge-container-mobile {
    max-height: 100px;
    overflow-y: auto;
    padding: 0.5rem 0;
  }

  /* Mobile iframe adjustments */
  .iframe-container-mobile {
    height: calc(60vh - 80px);
    overflow: hidden;
  }

  .iframe-container-mobile iframe {
    width: 100%;
    height: 100%;
    border: none;
  }

  /* Enhanced device card animations */
  .device-card-glow {
    position: relative;
    overflow: hidden;
  }

  .device-card-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transition: left 0.5s;
  }

  .device-card-glow:hover::before {
    left: 100%;
  }

  /* Floating animation for status indicators */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-2px);
    }
  }

  .status-float {
    animation: float 2s ease-in-out infinite;
  }

  /* Updating glow effect for devices being updated */
  .updating-glow {
    position: relative;
    box-shadow: 0 0 20px rgba(251, 146, 60, 0.3);
  }

  .updating-glow::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
      rgba(251, 146, 60, 0.4),
      rgba(251, 146, 60, 0.2),
      rgba(251, 146, 60, 0.4)
    );
    border-radius: inherit;
    z-index: -1;
    animation: updating-pulse 2s ease-in-out infinite;
  }

  @keyframes updating-pulse {
    0%, 100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }

  /* Pulse glow effect for online devices */
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(34, 197, 94, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(34, 197, 94, 0.8), 0 0 30px rgba(34, 197, 94, 0.4);
    }
  }

  .online-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Enhanced iframe dialog animations */
  @keyframes iframe-fade-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes iframe-slide-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .iframe-dialog-enter {
    animation: iframe-fade-in 0.3s ease-out;
  }

  .iframe-content-enter {
    animation: iframe-slide-up 0.4s ease-out 0.1s both;
  }

  .loading-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  /* Iframe dialog specific styles */
  .analytics-dashboard {
    --dialog-accent: theme(colors.blue.500);
  }

  .device-monitoring {
    --dialog-accent: theme(colors.green.500);
  }

  .system-logs {
    --dialog-accent: theme(colors.orange.500);
  }

  .performance-metrics {
    --dialog-accent: theme(colors.purple.500);
  }

  /* Mobile-friendly input styling */
  .dialog-input-mobile {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }

  /* Mobile calendar adjustments */
  [data-radix-popover-content] .calendar {
    width: auto;
    max-width: calc(100vw - 2rem);
  }
}

/* Tablet adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  [data-radix-dialog-content] {
    margin: 2rem;
    max-height: calc(100vh - 4rem);
  }

  .iframe-container-tablet {
    height: calc(70vh - 80px);
  }
}

/* Utility classes for responsive dialogs */
.dialog-responsive {
  width: 100%;
  max-width: 28rem;
}

@media (max-width: 640px) {
  .dialog-responsive {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
}

.dialog-header-responsive {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid hsl(var(--border));
}

@media (max-width: 640px) {
  .dialog-header-responsive {
    padding: 1rem;
  }

  .dialog-header-responsive h2 {
    font-size: 1rem;
    line-height: 1.5;
  }
}

/* Prevent horizontal scroll on mobile */
.dialog-content-container {
  overflow-x: hidden;
  word-wrap: break-word;
}

/* Loading states for mobile */
.dialog-loading-mobile {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

/* Smooth transitions for mobile dialogs */
.dialog-mobile-transition {
  transition: all 0.2s ease-in-out;
}

/* Focus management for mobile */
@media (max-width: 640px) {
  .dialog-focus-trap {
    outline: none;
  }

  .dialog-focus-trap:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
}

/* Accessibility improvements for mobile */
.dialog-mobile-accessible {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Mobile sheet optimizations */
@media (max-width: 768px) {
  .sheet-mobile {
    width: 100vw;
    max-width: none;
    height: 100vh;
    max-height: none;
    border-radius: 0;
  }

  .sheet-mobile-content {
    padding: 1rem;
    overflow-y: auto;
    height: calc(100vh - 4rem);
  }

  /* SheetSide specific optimizations */
  [data-radix-dialog-content][data-side="right"] {
    width: 100vw !important;
    max-width: none !important;
    height: 100vh !important;
    max-height: none !important;
    border-radius: 0 !important;
    right: 0 !important;
    top: 0 !important;
    transform: none !important;
  }

  /* Ensure sheet content doesn't overflow */
  .sheet-side-content {
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

/* Tablet sheet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  [data-radix-dialog-content][data-side="right"] {
    width: 400px !important;
    max-width: 400px !important;
  }
}

/* Desktop sheet optimizations */
@media (min-width: 1025px) {
  [data-radix-dialog-content][data-side="right"] {
    width: 420px !important;
    max-width: 420px !important;
  }
}

/* Mobile sheet touch optimizations */
@media (max-width: 768px) {
  /* Improve touch targets for mobile */
  .sheet-mobile-button {
    min-height: 48px;
    min-width: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Mobile sheet trigger button */
  .sheet-trigger-mobile {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
  }

  /* Mobile sheet close button */
  .sheet-close-mobile {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    z-index: 10;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    backdrop-filter: blur(10px);
  }

  /* Mobile footer buttons */
  .sheet-footer-mobile {
    padding: 1rem;
    gap: 0.75rem;
  }

  .sheet-footer-mobile button {
    height: 3rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.75rem;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Mobile quick actions */
  .sheet-quick-actions {
    margin-top: 0.75rem;
    text-align: center;
  }

  .sheet-quick-actions button {
    padding: 0.5rem;
    border-radius: 0.5rem;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Swipe indicator */
  .sheet-swipe-indicator {
    position: relative;
  }

  .sheet-swipe-indicator::before {
    content: '';
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
    height: 0.25rem;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0.125rem;
  }

  /* Smooth transitions for mobile interactions */
  .sheet-mobile-transition {
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  }

  /* Prevent body scroll when sheet is open */
  body:has([data-radix-dialog-content][data-side="right"][data-state="open"]) {
    overflow: hidden;
  }

  /* Fix sheet-side footer positioning on mobile */
  [data-radix-dialog-content][data-side="right"] {
    display: flex !important;
    flex-direction: column !important;
  }

  /* Ensure footer stays at bottom */
  .sheet-side-footer {
    margin-top: auto;
    flex-shrink: 0;
  }

  /* Add safe area padding for mobile devices */
  @supports (padding-bottom: env(safe-area-inset-bottom)) {
    .sheet-side-footer {
      padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    }
  }

  /* Hide default close button in sheet-side - Multiple selectors for safety */
  [data-radix-dialog-content][data-side="right"] > button[data-radix-dialog-close] {
    display: none !important;
  }

  [data-radix-dialog-content][data-side="right"] > button {
    display: none !important;
  }

  /* More specific selector for Radix close button */
  [data-radix-dialog-content] button[data-radix-dialog-close] {
    display: none !important;
  }
}