import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { cookies } from "next/headers";
import { ThemeProvider } from "./theme-provider";
import { ProviderContext } from "@/context/theme-data-provider";
import { Toaster } from "@/components/ui/toaster";
import { OrganizationProvider } from "@/context/organization-context";
import { Footer } from "./components/footer";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const cookieStore = await cookies();
  // console.log('cookieStore: ', cookieStore.get('sessionToken'));
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="layout">
            <ProviderContext initialSessionToken={cookieStore.get('sessionToken')?.value as string} initialRefreshToken={cookieStore.get('refreshToken')?.value as string} initialUserInfo={cookieStore.get('user')?.value as string}>
              <OrganizationProvider>
                <main className="w-full h-full">{children}</main>
              </OrganizationProvider>
            </ProviderContext>
          </div>
        </ThemeProvider>
        <Toaster/>
      </body>
    </html>
  );
}
