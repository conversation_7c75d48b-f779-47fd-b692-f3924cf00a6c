{"name": "game-dashboard-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 4000", "start:prod": "next build && next start -p 4000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.13.5", "@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@hookform/resolvers": "^3.9.1", "@mui/material-nextjs": "^6.1.9", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@svgr/webpack": "^8.1.0", "@tanstack/react-table": "^8.20.5", "@toolpad/core": "^0.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.475.0", "moment": "^2.30.1", "motion": "^12.0.0", "mqtt": "^5.10.3", "next": "15.0.3", "next-themes": "^0.4.4", "react": "19.0.0-rc-66855b96-20241106", "react-day-picker": "8.10.1", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.54.0", "react-icons": "^5.4.0", "recharts": "^2.14.1", "sonner": "^1.7.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "web-vitals": "^5.0.2", "zod": "^3.23.8"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}